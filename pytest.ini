[pytest]
disable_test_id_escaping_and_forfeit_all_rights_to_community_support = True
log_cli = true
log_cli_level = info
log_cli_format = %(asctime)s %(filename)s:%(lineno)s [%(levelname)s]: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
markers =
    text2text
    image2text
    text2image
    text2image_face_merge
    text2image_stickers
    text2image_multi_turn
    text2image_photomaker
    text2image_goods
    text2image_photo_studio
    text2image_canny
    text2image_canny1
    text2image_resolution
    text2image_resolution1
    text2image_olympics
    text2video
    text23d
    prerelease
    production
    image2image
