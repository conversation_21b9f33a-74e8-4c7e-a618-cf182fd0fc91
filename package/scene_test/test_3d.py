import base64
import difflib
import io
import os
import json
import logging
import random
import re
import uuid
import time
import zipfile
from mimetypes import guess_type

# import pyexiv2
import easyocr
import pytest
import requests
from allure import attach, attachment_type
from PIL import Image,ImageSequence

from package.common.prompt_map import prompt_plugin_map_reverse
from package.common.prompt_extract import prompt_list_to_map
from package.openapi.chat_prompt_enhance import *
from package.openapi.v1_tokenizer import *
from package.openapi.v1_chat_completions import *
from package.openapi.v1_images_chat_completions import *
from package.openapi.v1_images_generations import *
from package.openapi.v1_photo_maker_generations import *
from package.openapi.v1_photo_maker_validations import *
from package.openapi.v1_images_stickers_generations import *
from package.openapi.v1_images_stickers_validations import *
from package.openapi.v1_images_matting import *
from package.openapi.v1_images_edits import *
from package.openapi.v1_images_photo_studio_fine_tuning_submission import *
from package.openapi.v1_images_photo_studio_fine_tuning_task import *
from package.openapi.v1_images_photo_studio_validations_extended import *
from package.openapi.v1_images_photo_studio_validations_frontal import *
from package.openapi.v1_images_photo_studio_generations import *
from package.openapi.v1_videos_generations_submission import *
from package.openapi.v1_videos_generations_task import *
from package.openapi.v1_videos_stylizations_submission import *
from package.openapi.v1_videos_stylizations_task import *
from package.openapi.v1_videos_motion_submission import *
from package.openapi.v1_videos_motion_task import *
from package.openapi.v1_3d_generations_submission import *
from package.openapi.v1_3d_generations_task import *
from package.openapi.v1_videos_digital_human_generations_submission import *
from package.openapi.v1_videos_digital_human_generations_task import *
from package.openapi.v1_videos_animations_submission import *
from package.openapi.v1_videos_animations_task import *
from package.openapi.v1_images_canny import *
from package.openapi.v1_custom_images_face_fusion_olympics import *
try:
    from package.config import (api_key, cookie, openapi_domain)
except ImportError:
    api_key = os.environ.get('api_key', None)
    cookie = os.environ.get('cookie', None)
    openapi_domain = os.environ.get('openapi_domain', None)

logging.basicConfig(level=logging.DEBUG)
# log = logging.getLogger('test_1')


@pytest.mark.text23d
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,prompt, image_file,image_url,n,model",[
    (None, None, 'prompt_files/images/jj.jpg', None, None, 'hunyuan-3d-hpm'),
    (None, None, 'prompt_files/images/jj.jpg', None, None, 'hunyuan-3d-dit'),
    # (None, None, 'prompt_files/images/canny.jpg', None, 1, 'hunyuan-3d-hpm'),
    (None, '两个脖子很长很长很长的机器人', None, None, 1, 'hunyuan-3d-hpm'),
    (None, '两个脖子很长很长很长的机器人', None, None, 1, 'hunyuan-3d-dit'),
    ])
def test_v1_3d_generations_submission(version, prompt, image_file, image_url, n, model, record_property):
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    resp = v1_3d_generations_submission(domain=openapi_domain, api_key=api_key, model=model, version=version, prompt=prompt, image=image, image_url=image_url, n=n)
    print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']
    # task_id = '56a3be0e-08ae-4ae8-8b7f-83caa2cdcd99'
    status = None
    while status in (None,'queued','running'):
        resp = v1_3d_generations_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    assert status == 'succeeded', f'message: {resp["error"]["message"]}'
    gif_urls = resp['gif_urls']
    obj_urls = resp['obj_urls']
    assert len(gif_urls)==1
    assert len(obj_urls)==1
    for obj_url in obj_urls:
        assert zipfile.ZipFile(io.BytesIO(requests.get(obj_url).content)).testzip() is None # 检查ZIP文件是否有效且未损坏。如果ZIP文件有问题，它将返回有问题的第一个文件的名称；如果ZIP文件有效，它将返回None。

@pytest.mark.text23d
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,prompt,image_file,image_url,n,model",[
    (None, None, 'prompt_files/images/xi.jpg',None,1, 'hunyuan-3d-hpm'),
    (None, '习近平', None, None, 1, 'hunyuan-3d-hpm'),
    (None, None, 'prompt_files/images/xi.jpg',None,1, 'hunyuan-3d-dit'),
    (None, '习近平', None, None, 1, 'hunyuan-3d-dit')
    ])
def test_v1_3d_generations_submission_fail(version, prompt, image_file, image_url, n, model, record_property):
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    resp = v1_3d_generations_submission(domain=openapi_domain, api_key=api_key, model=model, version=version, prompt=prompt, image=image, image_url=image_url, n=n)
    print(resp['json'])
    assert resp['status_code'] == 200, f'3D任务提交失败: {resp["json"]["error"]}'
    task_id = resp['json']['task_id']
    # task_id = '56a3be0e-08ae-4ae8-8b7f-83caa2cdcd99'
    status = None
    while status in (None,'queued','running'):
        resp = v1_3d_generations_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    assert status == 'failed'
    assert resp['json']['message'] == '输入审核失败'

@pytest.mark.text23d
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,image_file,image_url,n,model",[
    # (None,'prompt_files/images/jj.jpg',None,2),
    (None,None,None,2,'hunyuan-3d-hpm'),
    (None,'prompt_files/images/jj.jpg',None,2,'hunyuan-3d-hpm')
    ])
def test_v1_3d_generations_submission_400(version, image_file, image_url, n, model, record_property):
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    resp = v1_3d_generations_submission(domain=openapi_domain, api_key=api_key, model=model, version=version, image=image, image_url=image_url, n=n)
    print(resp['json'])
    assert resp['status_code'] == 400
