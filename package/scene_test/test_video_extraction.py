import os
import logging
import time
import pytest
import math

from package.openapi.v1_video_extraction_generations_submission import *
from package.openapi.v1_video_extraction_generations_task import *

try:
    from package.config import (api_key, cookie, openapi_domain)
except ImportError:
    api_key = os.environ.get('api_key', None)
    cookie = os.environ.get('cookie', None)
    openapi_domain = os.environ.get('openapi_domain', None)

logging.basicConfig(level=logging.DEBUG)


@pytest.mark.skipif(openapi_domain == 'http://hunyuanapipre-release.woa.com', reason='下游云架平无预发布环境')
@pytest.mark.parametrize('video_url, expected', [
    ('https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/head.jpg', 200),
    ('https://hunyuantest.woa.com/api/resource/download?resourceId=f1a5d86d3917ca32bc042575e0df7b95', 200),
    (123123123, 400),
    (None, 400),
])
def test_v1_video_extraction_generations_fail(video_url, expected):
    res = v1_video_extraction_generations_submission(openapi_domain, api_key, video_url)
    assert res['status_code'] == expected
    if expected == 200:
        resp_json = res['json']
        task_id = resp_json['task_id']
        status = None
        start_time = time.time()
        while time.time() - start_time < 5 * 60:
            time.sleep(4)
            res = v1_video_extraction_generations_task(openapi_domain, api_key, task_id)
            assert res['status_code'] == 200
            status = res['json']['status']
            if status in ('completed', 'failed'):
                break
        else:
            pytest.fail("任务执行超时(5min)") 
        assert res['json']['status'] == 'failed'

@pytest.mark.skipif(openapi_domain == 'http://hunyuanapipre-release.woa.com', reason='下游云架平无预发布环境')
@pytest.mark.parametrize('video_url', [
    'https://v-cdn.zjol.com.cn/276988.mp4',
    'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/a86526295d2a4d96506b4fbbc0ce71ef.mov',
    'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/9b01a398bcea62e0fa952507e168347b.mov',
    # 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',
])
def test_v1_video_extraction_generations(video_url):
    res = v1_video_extraction_generations_submission(openapi_domain, api_key, video_url)
    assert res['status_code'] == 200
    assert res['created'] > 0 and res['created'] <= math.ceil(time.time())
    assert len(res['id']) > 0
    task_id = res['task_id']
    assert len(task_id) > 0
    status = None
    start_time = time.time()
    while time.time() - start_time < 10 * 60:
        time.sleep(20)
        res = v1_video_extraction_generations_task(openapi_domain, api_key, task_id)
        assert res['status_code'] == 200
        resp_json = res['json']
        assert resp_json['created'] > 0 and resp_json['created'] <= math.ceil(time.time())
        assert resp_json['start_time'] >= 0 and resp_json['start_time'] <= math.ceil(time.time())
        assert resp_json['finish_time'] == 0 or resp_json['finish_time'] >= resp_json['start_time'] and resp_json['finish_time'] <= time.time()
        assert len(res['id']) > 0
        status = resp_json['status']
        if status in ('completed', 'failed'):
            break
    else:
        pytest.fail("任务执行超时(10min)") 
    print(resp_json)
    assert resp_json['status'] == 'completed'
    data = resp_json.get('data')
    assert data is not None
    asr = data.get('asr')
    assert asr is not None
    assert asr.get('error_msg') == ''
    assert asr.get('status') == '0'
    assert asr.get('result')
    abstract = data.get('abstract')
    assert abstract is not None
    assert abstract.get('data')
    assert abstract.get('error_msg') == ''
    summary = data.get('summary')
    assert summary is not None
    assert summary.get('error_msg') == ''
    summary_data = summary.get('data')
    assert summary_data
    for summary_detail in summary_data:
        assert summary_detail.get('topic')
        assert summary_detail.get('time')
        assert summary_detail.get('summary')
    keyword = data.get('keyword')
    assert keyword is not None
    assert keyword.get('error_msg') == ''
    keyword_data = keyword.get('data')
    assert keyword_data
    assert len(keyword_data) <= 8
    for keyword_detail in keyword_data:
        assert keyword_detail.get('keyword')
        assert keyword_detail.get('time')
        assert keyword_detail.get('explanation')
        assert isinstance(keyword_detail.get('deep_research'), bool)
    mind_map = data.get('mind_map')
    assert mind_map is not None
    assert mind_map.get('error_msg') == ''
    assert mind_map.get('url_high')
    assert mind_map.get('url')
