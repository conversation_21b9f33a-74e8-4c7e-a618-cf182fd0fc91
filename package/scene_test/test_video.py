import base64
import difflib
import io
import os
import json
import logging
import random
import re
import uuid
import time
import zipfile
from mimetypes import guess_type

# import pyexiv2
import easyocr
import pytest
import requests
from allure import attach, attachment_type
from PIL import Image,ImageSequence

from package.common.prompt_map import prompt_plugin_map_reverse
from package.common.prompt_extract import prompt_list_to_map
from package.openapi.chat_prompt_enhance import *
from package.openapi.v1_tokenizer import *
from package.openapi.v1_chat_completions import *
from package.openapi.v1_images_chat_completions import *
from package.openapi.v1_images_generations import *
from package.openapi.v1_photo_maker_generations import *
from package.openapi.v1_photo_maker_validations import *
from package.openapi.v1_images_stickers_generations import *
from package.openapi.v1_images_stickers_validations import *
from package.openapi.v1_images_matting import *
from package.openapi.v1_images_edits import *
from package.openapi.v1_images_photo_studio_fine_tuning_submission import *
from package.openapi.v1_images_photo_studio_fine_tuning_task import *
from package.openapi.v1_images_photo_studio_validations_extended import *
from package.openapi.v1_images_photo_studio_validations_frontal import *
from package.openapi.v1_images_photo_studio_generations import *
from package.openapi.v1_videos_generations_submission import *
from package.openapi.v1_videos_generations_task import *
from package.openapi.v1_videos_stylizations_submission import *
from package.openapi.v1_videos_stylizations_task import *
from package.openapi.v1_videos_motion_submission import *
from package.openapi.v1_videos_motion_task import *
from package.openapi.v1_videos_cartoon_submission import *
from package.openapi.v1_videos_cartoon_task import *
from package.openapi.v1_videos_canvas_submission import *
from package.openapi.v1_videos_canvas_task import *
from package.openapi.v1_3d_generations_submission import *
from package.openapi.v1_3d_generations_task import *
from package.openapi.v1_videos_digital_human_generations_submission import *
from package.openapi.v1_videos_digital_human_generations_task import *
from package.openapi.v1_videos_animations_submission import *
from package.openapi.v1_videos_animations_task import *
from package.openapi.v1_images_canny import *
from package.openapi.v1_custom_images_face_fusion_olympics import *
try:
    from package.config import (api_key, cookie, openapi_domain)
except ImportError:
    api_key = os.environ.get('api_key', None)
    cookie = os.environ.get('cookie', None)
    openapi_domain = os.environ.get('openapi_domain', None)

logging.basicConfig(level=logging.DEBUG)
# log = logging.getLogger('test_1')

# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("model,prompt,negative_prompt,version,n,aspect_radio,resolution,duration,footnote,submit_code,result_code",[
    # ('hunyuan-video-aries','生成一段沙漠里下雪的视频','模糊的画面',None,1,'16:9',540,None,'test',200,200),  # 无对应模型
    ('hunyuan-video-aries','生成一段沙漠里下雪的视频','模糊，静态的画面',None,1,'16:9',720,None,'hello world',200,200),
    ('hunyuan-video-aries','中国国家主席习近平遭遇枪杀倒地身亡','不清楚的人像, 西方人物',None,1,'16:9',720,None,'hello world',200,422),  # 审核有问题，已反馈
    ('hunyuan-video-aries','fish swimming in the blue sky','ocean, water, sea',None,None,'9:16',720,None,'我是自定义水印我是自定义水印我是自定义水印我是自定义水印我是自定义水印我是自定义水印',400,None),
    ('hunyuan-video-aries','生成一段沙漠里下雪的视频','模糊的画面',None,1,'16:9',720,None,'台湾独立',200,422),
    ('hunyuan-video-aries','倒啤酒到空杯子里','模糊的画面',None,1,'16:9',720,None,'习近平',200,422),
    ])
def test_v1_videos_generations_submission(model, prompt, version, negative_prompt, n, aspect_radio, resolution, duration, footnote, submit_code, result_code, record_property):
    time_consumptions = []
    resp = v1_videos_generations_submission(openapi_domain, api_key, model, prompt, cookie, version, negative_prompt, n, aspect_radio, resolution, duration, footnote)
    print(resp['json'])
    assert resp['status_code'] == submit_code
    if submit_code == 200:
        task_id = resp['task_id']
        assert task_id is not None
        status = 'queued'
        while status in ('queued','running'):
            time.sleep(10)
            resp = v1_videos_generations_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
            assert resp['status_code'] == 200
            status = resp['status']
        res_json = resp['json']
        if result_code == 200:
            assert status == 'succeeded'
            assert res_json.get('error') is None
            videos = res_json.get('videos')
            assert videos
            url = videos[0].get('url')
            assert url
        else:
            assert status == 'failed'
            error = res_json.get('error')
            assert error
            assert error.get('code') == result_code
            # assert error.get('message')  # 无需判断message是否有值

@pytest.mark.skip(reason='下线中')
@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,image,image_url,n,aspect_radio,resolution,duration,style",[
    (None,None,'https://img.tukuppt.com/bg_grid/05/37/54/v40ZCaqERa.jpg!/fh/350',None,None,None,None,None)
    ])
def test_v1_videos_motion_submission(version, image, image_url, n, aspect_radio, resolution, duration, style, record_property):
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    resp = v1_videos_motion_submission(domain=openapi_domain, api_key=api_key, image_url=image_url)
    print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']
    # task_id = '56a3be0e-08ae-4ae8-8b7f-83caa2cdcd99'
    status = None
    while status in (None,'queued','running'):
        resp = v1_videos_motion_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    assert status == 'succeeded'

@pytest.mark.skip(reason='下线中')
@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,image_file,image_url,audio_file,audio_url",[
    (None,'prompt_files/images/xi.jpg',None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/alipay.wav'),
    # (None,'prompt_files/images/car.JPG',None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/alipay.wav'),
    # (None,None,'https://hunyuan-multimodal-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_multimodal/2_img_cont_understand_img_new_35_1c383cd30b7c298ab50293adfecb7b18.jpg.jpg?q-sign-algorithm=sha1&q-ak=AKIDQfiXrtaCew0o0vAvhEYdW8AIAoCOlQK7&q-sign-time=1695129322%3B1726233382&q-key-time=1695129322%3B1726233382&q-header-list=host&q-url-param-list=&q-signature=200400a2bdf223cac3e4b910febe4796a886a232',None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/alipay.wav')
    # (None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/head.jpg',None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/alipay.wav')
    ])
def test_v1_videos_digital_human_generations_submission(version, image_file, image_url, audio_file, audio_url, record_property):
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    if audio_file:
        with open(audio_file,'rb') as f:
            audio = base64.b64encode(f.read()).decode('utf-8')
    else:
        audio = None
    resp = v1_videos_digital_human_generations_submission(
        domain=openapi_domain, api_key=api_key, version=version, 
        image=image,image_url=image_url,audio=audio,audio_url=audio_url)
    print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']
    # task_id = '56a3be0e-08ae-4ae8-8b7f-83caa2cdcd99'
    status = None
    while status in (None,'queued','running'):
        resp = v1_videos_digital_human_generations_task(
            domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    print(f"返回：{resp['json']}")
    try:
        assert status == 'succeeded'
    except Exception:
        print(f"返回：{resp['json']}")
        # print(f"尺寸：{size}")
        raise

@pytest.mark.text2video
@pytest.mark.text2video_animations
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("model,version,pose_id,pose_video_url,image_file,image_url,duration,aspect_ratio,n",[
    # (None,None,1,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",1),
    # (None,None,1,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",1),
    # (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'https://watermark.lovepik.com/photo/20211120/large/lovepik-a-self-confident-image-of-a-male-image-picture_500448879.jpg',8,"2:3",1),
    # (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/720_720_20fps_12s.mp4',None,'https://watermark.lovepik.com/photo/20211120/large/lovepik-a-self-confident-image-of-a-male-image-picture_500448879.jpg',8,"2:3",1),
    (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4',None,'https://watermark.lovepik.com/photo/20211120/large/lovepik-a-self-confident-image-of-a-male-image-picture_500448879.jpg',8,"2:3",1),
    # (None,None,1,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'https://watermark.lovepik.com/photo/20211120/large/lovepik-a-self-confident-image-of-a-male-image-picture_500448879.jpg',8,"2:3",1),
    # (None,None,1,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/temp/20240508161658_20240508161657_aa07e17838f371257b17.mp4',None,'https://watermark.lovepik.com/photo/20211120/large/lovepik-a-self-confident-image-of-a-male-image-picture_500448879.jpg',8,"2:3",1),
    # (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/temp/20240508161658_20240508161657_aa07e17838f371257b17.mp4',None,'http://cpc.people.com.cn/NMediaFile/2024/0720/MAIN172147809481455KFHVMRPF.JPG',8,"2:3",1),
    # (None,None,1,None,None,'http://cpc.people.com.cn/NMediaFile/2024/0529/MAIN171698515294299HZFRD550.JPG',8,"2:3",None),
    # (None,None,2,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,3,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # ('hunyuan-video-animations-pro',None,1013,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",1),
    # ('hunyuan-video-animations-pro',None,1013,None,None,'http://cpc.people.com.cn/NMediaFile/2024/0529/MAIN171698515294299HZFRD550.JPG',8,"2:3",1),
    # ('hunyuan-video-animations-pro',None,1001,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # ('hunyuan-video-animations-pro',None,1002,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # ('hunyuan-video-animations-pro',None,1003,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,None,None,'prompt_files/images/multi_pose_test_case_1.png',None,8,None,None),
    # ('hunyuan-video-animations-pro',None,None,None,'prompt_files/images/multi_pose_test_case_1.png',None,8,None,None),
    ])
def test_v1_videos_animations_submission(model, version, pose_id, pose_video_url,image_file, image_url, duration, aspect_ratio, n, record_property):
    messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    resp = v1_videos_animations_submission(
        domain=openapi_domain, api_key=api_key, model=model, version=version, pose_id=pose_id, pose_video_url=pose_video_url, 
        image=image, image_url=image_url, duration=duration, aspect_ratio=aspect_ratio, n=n)
    print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']
    status = None
    while status in (None,'queued','running'):
        resp = v1_videos_animations_task(
            domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    print(f"返回：{resp['json']}")
    try:
        assert status == 'succeeded'
    except Exception:
        print(f"返回：{resp['json']}")
        # print(f"尺寸：{size}")
        raise

@pytest.mark.text2video
@pytest.mark.text2video_animations
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("model,version,pose_id,pose_video_url,image_file,image_url,duration,aspect_ratio,n",[
    # (None,None,'prompt_files/images/xi.jpg',None,None,None,None),
    # (None,None,'prompt_files/images/car.JPG',None,None,None,None),
    # (None,None,None,None,None)
    # (None,None,1,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,2,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,3,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    ('hunyuan-video-animations-pro',None,1013,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",1),
    # ('hunyuan-video-animations-pro',None,1013,None,None,'http://cpc.people.com.cn/NMediaFile/2024/0529/MAIN171698515294299HZFRD550.JPG',8,"2:3",1),
    # ('hunyuan-video-animations-pro',None,1001,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # ('hunyuan-video-animations-pro',None,1002,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # ('hunyuan-video-animations-pro',None,1003,None,None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/multi_pose_test_case_1.png',8,"2:3",None),
    # (None,None,None,None,'prompt_files/images/multi_pose_test_case_1.png',None,8,None,None),
    # ('hunyuan-video-animations-pro',None,None,None,'prompt_files/images/multi_pose_test_case_1.png',None,8,None,None),
    ])
def test_v1_videos_animations_submission_pro(model, version, pose_id, pose_video_url,image_file, image_url, duration, aspect_ratio, n, record_property):
    messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    if image_file:
        with open(image_file,'rb') as f:
            image = base64.b64encode(f.read()).decode('utf-8')
    else:
        image = None
    resp = v1_videos_animations_submission(
        domain=openapi_domain, api_key=api_key, model=model, version=version, pose_id=pose_id, pose_video_url=pose_video_url, 
        image=image, image_url=image_url, duration=duration, aspect_ratio=aspect_ratio, n=n)
    print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']
    status = None
    while status in (None,'queued','running'):
        resp = v1_videos_animations_task(
            domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)
    print(f"返回：{resp['json']}")
    try:
        assert status == 'succeeded'
    except Exception:
        print(f"返回：{resp['json']}")
        # print(f"尺寸：{size}")
        raise

# TODO: v1_videos_stylizations_submission v1_videos_canvas_submission

@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,video_url,video_file,style,n,duration,prompt",[
    (None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'2d_anime',1,2,'你好'),
    (None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4',None,'3d_cartoon',None,2,'你好'),
])
def test_v1_videos_stylizations_submission(version, video_url, video_file, style, n, duration, prompt):
    if video_file:
        with open(video_file,'rb') as f:
            video = base64.b64encode(f.read()).decode('utf-8')
    else:
        video = None
    resp = v1_videos_stylizations_submission(domain=openapi_domain, api_key=api_key, version=version, 
                                             video=video, video_url=video_url, style=style,n=n, 
                                             duration=duration,prompt=prompt)
    # print(resp['json'])
    assert resp['status_code'] == 200
    task_id = resp['task_id']
    status = None
    while status in (None,'queued','running'):
        resp = v1_videos_stylizations_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(20)
    print(resp['json'])
    assert status == 'succeeded'

@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("version,video_url,video_file,style,n,duration,prompt",[
    (None,None,None,'2d_anime',None,None,'nihao'),
    (None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4',None,'2d_anime',None,65535,'nihao'),
    (None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4',None,'3d_cartoon',1,65535,'nihao'),
    (None,'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4',None,'2d_anime',2,None,'nihao'),
])
def test_v1_videos_stylizations_submission_fail(version, video_url, video_file, style, n, duration, prompt):
    if video_file:
        with open(video_file,'rb') as f:
            video = base64.b64encode(f.read()).decode('utf-8')
    else:
        video = None
    resp = v1_videos_stylizations_submission(domain=openapi_domain, api_key=api_key, version=version, 
                                             video=video, video_url=video_url, style=style,n=n, 
                                             duration=duration,prompt=prompt)
    print(resp)
    assert resp['status_code'] == 400
    assert resp['err_message']

    #预发布环境无画布资源

# @pytest.mark.text2video
# @pytest.mark.prerelease
# @pytest.mark.production
# @pytest.mark.parametrize("prompt,version,video_url,video_file,width,height,area,duration,n",[
#     (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4', None, 600, 600, {'x': 0, 'y': 0, 'width': 40, 'height': 30}, 2, 1),
#     # (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/dance13s.mp4', None, 1024, 768, {'x': 0, 'y': 0, 'width': 768, 'height': 564}, 12, 1),
# ])
# def test_v1_videos_canvas_submission(prompt, version, video_url, video_file, width, height, area, duration, n):
    
#     if video_file:
#         with open(video_file,'rb') as f:
#             video = base64.b64encode(f.read()).decode('utf-8')
#     else:
#         video = None
#     resp = v1_videos_canvas_submission(domain=openapi_domain, api_key=api_key, prompt=prompt, version=version, 
#                                        video_url=video_url, video=video, width=width, height=height,
#                                        area=area, duration=duration, n=n)
#     print(resp)
#     assert resp['status_code'] == 200
#     task_id = resp['task_id']
#     status = None
#     while status in (None,'queued','running'):
#         resp = v1_videos_canvas_task(domain=openapi_domain, api_key=api_key, task_id=task_id)
#         assert resp['status_code'] == 200
#         status = resp['status']
#         time.sleep(20)
#     print(resp['json'])
#     assert status == 'succeeded'

# @pytest.mark.text2video
# @pytest.mark.prerelease
# @pytest.mark.production
# @pytest.mark.parametrize("prompt,version,video_url,video_file,width,height,area,duration,n",[
#     (None, None, None, None, 1024, 768, {'x': 20, 'y': 90, 'width': 768, 'height': 564}, 2.0, 1),
#     (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4', None, None, None, {'x': 20, 'y': 90, 'width': 768, 'height': 564}, 2, 1),
#     (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4', None, 1024, 768, None, 2, 1),
#     (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4', None, 1024, 768, {'x': 20, 'y': 90, 'width': 768, 'height': 564}, 2, 2),
#     (None, None, 'https://adt-1258344703.cos.ap-guangzhou.myqcloud.com/aigc-api-test/8100_8100_60fps_12s_99m.mp4', None, 1024, 768, {'x': 20, 'y': 90, 'width': 768, 'height': 564}, 1, 2),
# ])
# def test_v1_videos_canvas_submission_fail(prompt, version, video_url, video_file, width, height, area, duration, n):
    
#     if video_file:
#         with open(video_file,'rb') as f:
#             video = base64.b64encode(f.read()).decode('utf-8')
#     else:
#         video = None
#     resp = v1_videos_canvas_submission(domain=openapi_domain, api_key=api_key, prompt=prompt, version=version, 
#                                        video_url=video_url, video=video, width=width, height=height,
#                                        area=area, duration=duration, n=n)
#     print(resp['json'])
#     assert resp['status_code'] == 400
#     assert resp['err_message']

@pytest.mark.text2video
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize('model,version,image,image_url,n,width,cfg,num_frames,seed,frame_rate',[
    ('hunyuan-video-cartoon',None,None,
     'https://hunyuan-prod-1258344703.cos.ap-guangzhou.myqcloud.com/text2img/aae540cce6f6f6ccb13cec1f913cab76/20250124115152h0_454e2bb4c6112cec6ddc00c1ff60b1f1706.png?q-sign-algorithm=sha1&q-ak=AKIDRl074nOsGdJ9zjMsCRWP3ShmgS3VtX4S&q-sign-time=1737690712;1769226712&q-key-time=1737690712;1769226712&q-header-list=host&q-url-param-list=&q-signature=ecbddb660f8423f2472cff8f993dcea6abc9a592',
     1,1360,8.0,1,1234,16),
    ('hunyuan-video-cartoon',None,None,
     'https://hunyuan-prod-1258344703.cos.ap-guangzhou.myqcloud.com/text2img/aae540cce6f6f6ccb13cec1f913cab76/20250124115152h0_454e2bb4c6112cec6ddc00c1ff60b1f1706.png?q-sign-algorithm=sha1&q-ak=AKIDRl074nOsGdJ9zjMsCRWP3ShmgS3VtX4S&q-sign-time=1737690712;1769226712&q-key-time=1737690712;1769226712&q-header-list=host&q-url-param-list=&q-signature=ecbddb660f8423f2472cff8f993dcea6abc9a592',
     None,None,None,None,None,None),
    ('hunyuan-video-cartoon', None, None,
     'https://hunyuan-prod-1258344703.cos.ap-guangzhou.myqcloud.com/text2img/aae540cce6f6f6ccb13cec1f913cab76/20250124115152h0_454e2bb4c6112cec6ddc00c1ff60b1f1706.png?q-sign-algorithm=sha1&q-ak=AKIDRl074nOsGdJ9zjMsCRWP3ShmgS3VtX4S&q-sign-time=1737690712;1769226712&q-key-time=1737690712;1769226712&q-header-list=host&q-url-param-list=&q-signature=ecbddb660f8423f2472cff8f993dcea6abc9a592',
     None, 768, 1.0, 145, None, None)
])
def test_v1_videos_cartoon(model,version,image,image_url,n,width,cfg,num_frames,seed,frame_rate):
    params = {
        'model': model,
        'version': version,
        'image': image,
        'image_url': image_url,
        'n': n,
        'width': width,
        'cfg': cfg,
        'num_frames': num_frames,
        'seed': seed,
        'frame_rate': frame_rate,
    }

    resp = v1_videos_cartoon_submission(domain=openapi_domain, api_key=api_key, **params)

    assert resp['status_code'] == 200
    task_id = resp['json']['task_id']

    status = None
    print("\n" + "~" * 30 + " v1/videos/cartoon/task " + "~" * 30 + "\n")
    while status in (None, 'queued', 'running'):
        resp = v1_videos_cartoon_task(
            domain=openapi_domain,
            api_key=api_key,
            task_id=task_id)
        assert resp['status_code'] == 200
        status = resp['status']
        time.sleep(5)

    print(f"任务状态: {status}")
    print(f"返回结果: {resp['json']}")

    assert status == 'succeeded'
    assert 'videos' in resp['json']
    assert isinstance(resp['json']['videos'], list)
    for video in resp['json']['videos']:
        assert 'url' in video
        assert video['url'].startswith('http')