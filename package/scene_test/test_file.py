import sys,os
import base64
import difflib
import io
import json
import logging
import random
import re
import uuid
import time
import zipfile
import threading
from mimetypes import guess_type

# import pyexiv2
# import easyocr
import pytest
import requests
from allure import attach, attachment_type
from PIL import Image,ImageSequence

from package.common.prompt_map import prompt_plugin_map_reverse
from package.common.prompt_extract import prompt_list_to_map
from package.openapi.chat_prompt_enhance import *
from package.openapi.v1_tokenizer import *
from package.openapi.v1_chat_completions import *
from package.openapi.v1_images_chat_completions import *
from package.openapi.v1_images_generations import *
from package.openapi.v1_photo_maker_generations import *
from package.openapi.v1_photo_maker_validations import *
from package.openapi.v1_images_stickers_generations import *
from package.openapi.v1_images_stickers_validations import *
from package.openapi.v1_images_matting import *
from package.openapi.v1_images_edits import *
from package.openapi.v1_images_photo_studio_fine_tuning_submission import *
from package.openapi.v1_images_photo_studio_fine_tuning_task import *
from package.openapi.v1_images_photo_studio_validations_extended import *
from package.openapi.v1_images_photo_studio_validations_frontal import *
from package.openapi.v1_images_photo_studio_generations import *
from package.openapi.v1_videos_generations_submission import *
from package.openapi.v1_videos_generations_task import *
from package.openapi.v1_videos_stylizations_submission import *
from package.openapi.v1_videos_stylizations_task import *
from package.openapi.v1_videos_motion_submission import *
from package.openapi.v1_videos_motion_task import *
from package.openapi.v1_3d_generations_submission import *
from package.openapi.v1_3d_generations_task import *
from package.openapi.v1_videos_digital_human_generations_submission import *
from package.openapi.v1_videos_digital_human_generations_task import *
from package.openapi.v1_videos_animations_submission import *
from package.openapi.v1_videos_animations_task import *
from package.openapi.v1_images_canny import *
from package.openapi.v1_files import *
from package.openapi.v1_files_deletions import *
from package.openapi.v1_files_uploads import *
from package.openapi.v1_document_mindmap import *
from package.openapi.v1_document_translations import *
try:
    from package.config import (api_key, cookie, openapi_domain)
except ImportError:
    api_key = os.environ.get('api_key', None)
    cookie = os.environ.get('cookie', None)
    openapi_domain = os.environ.get('openapi_domain', None)

logging.basicConfig(level=logging.DEBUG)

ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))))

@pytest.fixture(scope='module')
def uploaded_record():
    """上传文件
    
    作为文件删除/对话/查询的前置
    上传的文件列表保存在 file_names 中
    返回的结果包含字段：file_ids, purpose, user
    """
    file_name = '2020third-artificial-intelligence.pdf'
    purpose = 'file-extract'
    user = 'user_01'
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose=purpose, user=user)
    assert resp['status_code'] == 200, f"前置文件上传失败: {resp['err_message']}"
    file_id = resp['id']
    assert file_id is not None, "文件上传后返回文件id异常"
    yield {
        'file_id': file_id,
        'purpose': purpose,
        'user': user,
    }
    res = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)

@pytest.mark.parametrize("file_name,purpose,user",[
    ('2020third-artificial-intelligence.pdf', 'file-extract', 'user_01'),
    ('《Go语言编程》高清完整版电子书.pdf', 'file-extract', None),
    ('GitHub入门与实践.pdf', 'file-extract', 'user_02'),
    # ('text_file.txt', 'file-extract', 'user_01'),
    # ('excel_file.xlsx', 'file-extract', 'user_01'),
    # ('大数据演义.pptx', 'file-extract', 'user_01'),
    # ('doc_file.docx', 'file-extract', 'user_01'),
    # ('csv_file.csv', 'file-extract', 'user_02'),
    ])
def test_v1_files_uploads(file_name, purpose, user):
    """文件上传"""
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose=purpose, user=user)
    assert resp['status_code'] == 200
    assert resp['id'] is not None
    assert resp['object'] == 'file'
    assert resp['bytes'] > 0
    assert resp['created_at']
    assert resp['filename'] == file_name
    assert resp['purpose'] == purpose
    assert resp['file_type']
    res = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=resp['id'], user=user)
    assert res['status_code'] == 200, "文件删除失败"

@pytest.mark.parametrize("file_name,purpose,user",[
    ('dog.jpeg', 'file-extract', 'user_f1'),
    # # ('large_file.pdf', 'file-extract', 'user_f1'),
    (None, 'file-extract', 'user_f1'),
    ('GitHub入门与实践.pdf', 'file-update', 'user_f1'),
    ('GitHub入门与实践.pdf', None, 'user_f1'),
    ('sensitive_content.txt', 'file-extract', 'user_f1'),
    ('empty_content.txt', 'file-extract', 'user_f1'),
    ])
def test_v1_files_uploads_fail(file_name, purpose, user):
    """文件上传失败"""
    file = None
    if file_name is not None:
        ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))))
        file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
        with open(file_path,'rb') as file:
            resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose=purpose, user=user)
        assert resp['status_code'] == 400

@pytest.mark.parametrize("limit,offset",[(5,0), (1,None)])
def test_v1_files_list(limit, offset, uploaded_record):
    """文件查询"""
    user = uploaded_record['user']
    purpose = uploaded_record['purpose']
    resp = v1_files(domain=openapi_domain, api_key=api_key, cookie=cookie, 
                    user=user, purpose=purpose, offset=offset, limit=limit)
    print(f"lenth of query result: {len(resp['files'])}")
    assert resp['status_code'] == 200
    assert resp['object'] == 'list'
    assert resp['total']
    assert resp['files']
    assert len(resp['files']) > 0

@pytest.mark.parametrize("user,purpose,limit,offset",[
    ('user_01','file-extract','1','0'), 
    ('user_01','file-extract',2**63,0),
    ('user_01','file-upload',1,0), 
    ])
def test_v1_files_list_fail(user, purpose, limit, offset):
    """文件查询失败"""
    resp = v1_files(domain=openapi_domain, api_key=api_key, cookie=cookie, user=user, purpose=purpose, offset=offset, limit=limit)
    assert resp['status_code'] == 400
    # assert resp['err_message']

@pytest.mark.parametrize("content",[
    ('你好，请给所有文章撰写简介，如果有多个文章，请全部概括，分行输出，##输出## 的格式为##文章名称##: [文章简介]'), 
    ])
def test_v1_files_chat_completion(content, uploaded_record):
    """文件对话"""
    user = uploaded_record['user']
    file_id = uploaded_record['file_id']
    messages = [{'role': 'user', 'content': content, 'file_ids': [file_id]}]
    resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, cookie=cookie, messages=messages, user=user)
    assert resp['status_code'] == 200
    assert len(resp['message_content']) >= 50

def test_v1_files_deletion(uploaded_record):
    """文件删除"""
    user = uploaded_record['user']
    file_id = uploaded_record['file_id']
    resp = v1_files_deletions(domain=openapi_domain, api_key=api_key, 
                              cookie=cookie, file_id=file_id, user=user)
    assert resp['status_code'] == 200
    assert resp['deleted'] == True
    assert resp['id'] == file_id

@pytest.mark.parametrize("user,file_id",[
    ('user_deletion_fail','file-wrong-id'), 
    ('user_deletion_fail',None)
    ])
def test_v1_files_deletion_fail(user, file_id):
    """文件删除失败"""
    resp = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)
    assert resp['status_code'] == 400
    assert resp['err_message']

def test_v1_files_deletion_authorization_bypass(uploaded_record):
    """文件删除越权检查"""
    file_id = uploaded_record['file_id']
    user = 'other_user'
    resp = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)
    assert resp['status_code'] == 400
    assert resp['err_message']

@pytest.mark.parametrize('file_name', [])
def test_deep_read_with_multimedia(file_name):
    """文件深度阅读，返回图片"""
    model = 'hunyuan'
    stream = False
    user = 'user_deep_read'
    # 上传文件，获取文件id和文件类型
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose='file-extract', user=user)
    assert resp['status_code'] == 200
    file_id = resp['id']
    # 进行深度阅读对话
    messages = [{'role': 'user', 'content': '核心速览', 'file_ids': [file_id]}]
    resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model=model, stream=stream, user=user, 
                               enable_deep_read=True, enable_multimedia=True)
    assert resp['status_code'] == 200
    assert resp['replaces'], f"未返回图片(不保证一定返回图片，但是本用例基本可以返回图片)，id:{resp['id']}"
    res = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)
    assert res['status_code'] == 200, f"删除文件失败"

DEEP_READ_PROMPT_DICT = {
    '学术论文': ['核心速览', '论文评价', '关键问题及回答', '论文标题与作者'],
    '研报': ['主要内容', '关键问题及回答'],
    '财报': ['主要内容', '关键问题及回答', '财务分析'],
    '其他': ['核心速览', '关键问题及回答', '关联信息补充'],
    'excel_result': ['整理表格的信息，用一段话对表格的类型、用途、表头解释、大致内容进行输出', '根据表格内容给出几个关键结论。如果某些分析过程适合用图表展示，则选择合适的图表类型并展示最多2个可视化图表，每次写代码只输出一个图表。']
}  # 触发不同类型深度阅读的type-prompt列表: type为"研报"时, 使用对应value列表中的元素作为prompt即可触发深度阅读

@pytest.mark.parametrize('file_name', ['2020third-artificial-intelligence.pdf', 'text_file.txt', '腾讯财报.pdf'])  # 暂不支持表格文件，先skip用例：excel_file.xlsx
def test_deep_read_without_multimedia(file_name):
    """文件深度阅读，不返回图片"""
    model = 'hunyuan'
    stream = False
    user = 'user_deep_read'
    # 上传文件，获取文件id和文件类型
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose='file-extract', user=user)
    assert resp['status_code'] == 200
    file_id = resp['id']
    file_type = resp['file_type']
    assert file_type in DEEP_READ_PROMPT_DICT, (f"上传文件识别得到的文件类型不在已知可以进行深度阅读类型的列表中，file_type:'{file_type}', "
                                                f"已知可以进行深度阅读的文件类型列表：{list(DEEP_READ_PROMPT_DICT.keys())}")
    prompt_list = DEEP_READ_PROMPT_DICT[file_type]
    # 进行深度阅读对话
    messages = [{'role': 'user', 'content': random.choice(prompt_list), 'file_ids': [file_id]}]
    resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model=model, stream=stream, user=user,
                               enable_deep_read=True, enable_multimedia=True)
    assert resp['status_code'] == 200

@pytest.mark.parametrize('file_name', ['2020third-artificial-intelligence.pdf', '腾讯财报.pdf', 'text_file.txt'])
def test_v1_document_mindmap(file_name):
    """针对文档返回脑图相关内容"""
    model = 'hunyuan'
    user = 'user_deep_read'
    # 上传文件，获取文件id和文件类型
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose='file-extract', user=user)
    assert resp['status_code'] == 200
    file_id = resp['id']
    resp = v1_document_mindmap(domain=openapi_domain, api_key=api_key, file_id=file_id, model=model, user=user)
    assert resp['status_code'] == 200
    assert resp['id']
    assert resp['created']
    data = resp['data']
    assert data
    url = data.get('url')
    assert url
    res = requests.get(url=url, verify=False)
    assert res.status_code == 200
    thumb_url = data.get('thumb_url')
    assert thumb_url
    res = requests.get(url=thumb_url, verify=False)
    assert res.status_code == 200
    res = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)
    assert res['status_code'] == 200, f"删除文件失败"

@pytest.mark.parametrize('file_name, expect_finish_reason', [('english_essay.pdf', 'stop')])
def test_v1_document_translations(file_name,expect_finish_reason):
    """针对文档返回翻译内容"""
    index = 1
    model = 'hunyuan'
    user = 'user_translation'
    stream = False
    # 上传文件，获取文件id和文件类型
    file_path = os.path.join(ROOT_DIR, f'prompt_files/files/{file_name}')
    with open(file_path,'rb') as file:
        resp = v1_files_uploads(domain=openapi_domain, api_key=api_key, file=file, purpose='file-extract', user=user)
    assert resp['status_code'] == 200
    file_id = resp['id']
    TEST_TIMES = 3  # 查询三次不同分片
    for _ in range(TEST_TIMES):
        resp = v1_document_translations(domain=openapi_domain, api_key=api_key, file_id=file_id, model=model, user=user, stream=stream, index=index)
        assert resp['status_code'] == 200
        assert resp['message_content']
        translation_info = resp['translation_info']
        assert translation_info['target'] == 'zh'
        assert translation_info['index'] == index
        max_index = translation_info['max_index']
        assert max_index
        usage = resp['usage']
        assert usage
        finish_reason = resp['finish_reason']
        assert finish_reason == expect_finish_reason
        index = random.randint(1, max_index)
    res = v1_files_deletions(domain=openapi_domain, api_key=api_key, cookie=cookie, file_id=file_id, user=user)
    assert res['status_code'] == 200, f"删除文件失败"