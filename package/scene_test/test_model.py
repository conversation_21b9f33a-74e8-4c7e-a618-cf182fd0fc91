import re
import pytest
import json
from allure import attach, attachment_type
from package.config import domain, cookie, userids, secret_ids, secret_keys
from package.common.signature import get_signature
from package.common.prompt_map import prompt_plugin_map_reverse
from package.api.generate_id import generate_id
from package.api.chat import chat
from package.api.conv import conv
import os
import time
import uuid
import json
import requests
import sseclient
# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

from ..prompt.json import collection_type1, collection_type3, collection_plugin, collection_intention, multiply_modify_for_text_to_image
from ..prompt.csv import collection_sensitive_text2image, collection_openapi_and_webapi, collection_qa_t2t_deny, collection_qa_t2t_allow, collection_credible_model, collection_intention_csv, collection_multi_round_text2image
# try:
#     from ..prompt.json import collection_intention
# except ImportError:
#     pass
collection_intention.extend(collection_credible_model)
collection_intention.extend(collection_intention_csv)
# prompt_list_all = [*collection_type1, *collection_type3, *collection_plugin, *collection_openapi_and_webapi]
prompt_list_all = [*collection_type1, *collection_type3, *collection_plugin]


@pytest.fixture(scope='module')
def start_chat(request):
    index_id = request.param[0]
    adt_id = request.param[4]
    prompt_type = request.param[3]
    prompts = request.param[1]
    try:
        prompts = json.loads(prompts)
        prompts = json.loads(prompts)
    except Exception:
        pass
    try:
        prompts = eval(prompts)
    except Exception:
        pass
    if isinstance(prompts,str):
        prompts = [prompts]
    prompt = prompts[0]
    ref_answers = request.param[5] if len(request.param)>5 and request.param[5] else ''
    try:
        ref_answers = json.loads(ref_answers)
    except Exception:
        pass
    ref_answers = [ref_answers] if ref_answers and isinstance(ref_answers,str) else ref_answers
    ref_answer_regexs = request.param[6] if len(request.param)>6 and request.param[6] else ''
    try:
        ref_answer_regexs = json.loads(ref_answer_regexs)
    except Exception:
        pass
    ref_answer_regexs = [ref_answer_regexs] if ref_answer_regexs and isinstance(ref_answer_regexs,str) else ref_answer_regexs
    # print('prompts {}'.format(prompts))
    # print('prompt {}'.format(prompt))
    # print('prompt {}'.format(prompts[1]))
    # print('type{}'.format(type(prompts)))
    # exit(0)
    expected_plugin = prompt_plugin_map_reverse.get(request.param[2], 'Adaptive') 
    userid_list = userids.split(',')
    try:
        worker_divided = int(request.config.workerinput["workerid"].replace("gw", ""))%len(secret_ids)
    except:
        worker_divided = 0
    userid = ''
    # prompts = json.loads(request.param[1])
    print(prompts)
    if prompt_type == '多轮改写-文生图':
        prompts = prompts[:-1]
    answers = []
    

    yield {
        'id': id,
        'index_id': index_id,
        'prompt': prompt,
        'expected_plugin': expected_plugin,
        'prompt_type': prompt_type,
        'adt_id': adt_id,
        'userid': userid,
        'answers': answers,
        'ref_answers': ref_answers,
        'ref_answer_regexs': ref_answer_regexs,
        'worker_divided': worker_divided
    }

@pytest.mark.parametrize('start_chat', collection_intention, ids=['id={}, type={}, duplicated=0'.format(i[0], i[3]) for i in prompt_list_all], indirect=True)
def test_math(start_chat, record_property):
    ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:1081/openapi/chat/completions"
    model = "math_base"
    wsid = "10456"
    enable_stream = True

    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer 7auGXNATFSKl7dF",
        "Wsid": wsid,
    }

    json_data = {
        "model": model,
        "query_id": "test_query_id_" + str(uuid.uuid4()),
        "messages": [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": start_chat['prompt']}
        ],
        "temperature": 1,
        "top_p": 0.8,
        "top_k": 40,
        "repetition_penalty": 1,
        "output_seq_len": 1024,
        "max_input_seq_len": 2048,
        "stream": enable_stream,
    }

    print('Input:\n{} | {} | {}'.format(ss_url, headers, json_data))
    resp = requests.post(ss_url, headers=headers, json=json_data, stream=True)

    print('Output:')
    if enable_stream:
        client = sseclient.SSEClient(resp)
        for event in client.events():
            if event.data != '':
                data_js = json.loads(event.data)
                try:
                    print(data_js['choices'][0]['delta']['content'], end='', flush=True)
                except Exception as e:
                    print(data_js)
    else:
        print(resp.json())