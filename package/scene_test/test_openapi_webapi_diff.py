import re
import pytest
import json
import io
import requests
from allure import attach, attachment_type
from PIL import Image
from package.config import domain, cookie, userids, openapi_domain, api_key
from package.common.prompt_map import prompt_plugin_map_reverse
from package.api.generate_id import generate_id
from package.api.chat import chat
from package.api.conv import conv
from package.openapi.v1_chat_completions import v1_chat_completions
from package.openapi.v1_images_generations import v1_images_generations
# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

from ..prompt.adt import collection_type1, collection_type3, collection_plugin
from ..prompt.csv import collection_sensitive_text2image, collection_openapi_and_webapi
prompt_list_all = [*collection_type1, *collection_type3, *collection_plugin, *collection_openapi_and_webapi]

@pytest.fixture(scope='module')
def start_chat_openapi_and_webapi(request):
    adt_id = request.param[4]
    prompt_type = request.param[3]
    prompts = request.param[1]
    try:
        prompts = json.loads(prompts)
        prompts = json.loads(prompts)
    except Exception:
        pass
    try:
        prompts = eval(prompts)
    except Exception:
        pass
    if isinstance(prompts,str):
        prompts = [prompts]
    prompt = prompts[0]
    ref_answers = request.param[5] if len(request.param)>5 and request.param[5] else ''
    try:
        ref_answers = json.loads(ref_answers)
    except Exception:
        pass
    ref_answers = [ref_answers] if ref_answers and isinstance(ref_answers,str) else ref_answers
    ref_answer_regexs = request.param[6] if len(request.param)>6 and request.param[6] else ''
    try:
        ref_answer_regexs = json.loads(ref_answer_regexs)
    except Exception:
        pass
    ref_answer_regexs = [ref_answer_regexs] if ref_answer_regexs and isinstance(ref_answer_regexs,str) else ref_answer_regexs
    # print('prompts {}'.format(prompts))
    # print('prompt {}'.format(prompt))
    # print('prompt {}'.format(prompts[1]))
    # print('type{}'.format(type(prompts)))
    # exit(0)
    expected_plugin = prompt_plugin_map_reverse.get(request.param[2], 'Adaptive') 
    # prompts = json.loads(request.param[1])
    userid_list = userids.split(',')
    userid = userid_list[int(request.config.workerinput["workerid"].replace("gw", ""))%len(userid_list)]
    id = generate_id(domain, cookie, userid)
    assert re.match('([A-Za-z0-9]+-){4}[A-Za-z0-9]+', id)
    openapi_messages = []
    openapi_resps = []
    openapi_answers = []
    openapi_finish_reasons = []
    openapi_time_consumptions = []
    webapi_answers = []
    for prompt in prompts:
        openapi_messages.append({'role': 'user', 'content': prompt})
        openapi_resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=openapi_messages, model='hunyuan-13B', cookie=cookie)
        openapi_answer = openapi_resp['message_content'] 
        openapi_messages.append({'role': 'assistant', 'content': openapi_answer})
        openapi_answers.append(openapi_answer)
        openapi_resps.append(openapi_resp)
        openapi_finish_reasons.append(openapi_resp['finish_reason'])
        openapi_time_consumptions.append(openapi_resp['time_consumption'])
        webapi_resp = chat(domain=domain, cookie=cookie, userid=userid, id=id, prompt=prompt, model='gpt_175B_0404', plugin='Adaptive')
        webapi_answer = webapi_resp['msg_str']
        webapi_answers.append(webapi_answer)
    yield {
        'adt_id': adt_id,
        'prompts': prompts,
        'prompt_type': prompt_type,
        'openapi_answers': openapi_answers,
        'webapi_answers': webapi_answers,
        'openapi_resps': openapi_resps,
        'openapi_messages' : openapi_messages,
        'openapi_finish_reasons': openapi_finish_reasons,
        'openapi_time_consumptions': openapi_time_consumptions,
        'expected_plugin': expected_plugin,
        'ref_answers': ref_answers,
        'ref_answer_regexs': ref_answer_regexs
    }

@pytest.mark.parametrize('start_chat_openapi', prompt_list_all, ids=['id={}, type={}, duplicated=0'.format(i[0], i[3]) for i in prompt_list_all], indirect=True)
def test_diff_openapi_webapi(start_chat_openapi_and_webapi, record_property):
    record_property('adt_id', start_chat_openapi_and_webapi['adt_id'])
    prompts = start_chat_openapi_and_webapi['prompts']
    openapi_answers = start_chat_openapi_and_webapi['openapi_answers']
    webapi_answers = start_chat_openapi_and_webapi['webapi_answers']
    record_property('prompts', prompts)
    record_property('openapi_answers', openapi_answers)
    record_property('webapi_answers', webapi_answers)
    try:
        for n in range(len(prompts)):
            assert openapi_answers[n] == webapi_answers[n]
    except AssertionError:
        print("问题：{}".format(prompts))
        print("OpenAPI的回答：{}".format(openapi_answers))
        print("WebAPI的回答：{}".format(webapi_answers))
        raise