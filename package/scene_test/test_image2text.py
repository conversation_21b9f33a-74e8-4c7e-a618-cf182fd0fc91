import base64
import difflib
import io
import os
import json
import logging
import random
import re
import uuid
import time
import zipfile
from mimetypes import guess_type

# import pyexiv2
import easyocr
import pytest
import requests
from allure import attach, attachment_type
from PIL import Image,ImageSequence

from package.common.prompt_map import prompt_plugin_map_reverse
from package.common.prompt_extract import prompt_list_to_map
from package.openapi.chat_prompt_enhance import *
from package.openapi.v1_tokenizer import *
from package.openapi.v1_chat_completions import *
from package.openapi.v1_images_chat_completions import *
from package.openapi.v1_images_generations import *
from package.openapi.v1_photo_maker_generations import *
from package.openapi.v1_photo_maker_validations import *
from package.openapi.v1_images_stickers_generations import *
from package.openapi.v1_images_stickers_validations import *
from package.openapi.v1_images_matting import *
from package.openapi.v1_images_edits import *
from package.openapi.v1_images_photo_studio_fine_tuning_submission import *
from package.openapi.v1_images_photo_studio_fine_tuning_task import *
from package.openapi.v1_images_photo_studio_validations_extended import *
from package.openapi.v1_images_photo_studio_validations_frontal import *
from package.openapi.v1_images_photo_studio_generations import *
from package.openapi.v1_videos_generations_submission import *
from package.openapi.v1_videos_generations_task import *
from package.openapi.v1_videos_stylizations_submission import *
from package.openapi.v1_videos_stylizations_task import *
from package.openapi.v1_videos_motion_submission import *
from package.openapi.v1_videos_motion_task import *
from package.openapi.v1_3d_generations_submission import *
from package.openapi.v1_3d_generations_task import *
from package.openapi.v1_videos_digital_human_generations_submission import *
from package.openapi.v1_videos_digital_human_generations_task import *
from package.openapi.v1_videos_animations_submission import *
from package.openapi.v1_videos_animations_task import *
from package.openapi.v1_images_canny import *
from package.openapi.v1_custom_images_face_fusion_olympics import *
try:
    from package.config import (api_key, cookie, openapi_domain)
except ImportError:
    api_key = os.environ.get('api_key', None)
    cookie = os.environ.get('cookie', None)
    openapi_domain = os.environ.get('openapi_domain', None)

logging.basicConfig(level=logging.DEBUG)
# log = logging.getLogger('test_1')

# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

# from ..prompt.adt import collection_plugin, collection_type1, collection_type3
# from ..prompt.csv import (collection_openapi_and_webapi,
#                           collection_sensitive_text2image)

# prompt_list_all = [*collection_type1, *collection_type3, *collection_plugin, *collection_openapi_and_webapi]

@pytest.mark.image2text
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("prompts,user,model",[
    ([{'text':'描述一下', 'image_url': 'https://img.tukuppt.com/bg_grid/05/37/54/v40ZCaqERa.jpg!/fh/350'}, 'in english'], None,'hunyuan-vision'),
    ([{'text':'描述一下', 'image_url': 'https://img.tukuppt.com/bg_grid/05/37/54/v40ZCaqERa.jpg!/fh/350'}, 'in english'], 'test1','hunyuan-vision'),
    ([{'text':'描述一下', 'image_file': 'prompt_files/images/350.png'}, 'in english'], None,'hunyuan-vision'),
    ([{'text':'描述一下', 'image_file': 'prompt_files/images/text.jpg'}, 'in english'], 'test1','hunyuan-vision'),
    ([{'text':'描述一下', 'image_url': 'https://img.tukuppt.com/bg_grid/05/37/54/v40ZCaqERa.jpg!/fh/350'}, 'in english'], 'test1', 'hunyuan-turbo-vision'),
    ([{'text':'描述一下', 'image_file': 'prompt_files/images/350.png'}, 'in english'], None,'hunyuan-turbo-vision'),
    ([{'text':'图片中有什么内容？', 'image_url': 'https://img.tukuppt.com/bg_grid/05/37/54/v40ZCaqERa.jpg!/fh/350'}], None,'hunyuan-vision-7b-dense'),
    # (['描述一下', 'in english'], None, 'test1', None),
    ])
def test_image2text(prompts, user, model, record_property):
    messages = []
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    image_url = None
    for prompt in prompts:
        if isinstance(prompt, dict):
            if 'image_url' in prompt:
                image_url = prompt['image_url']
            elif 'image_file' in prompt:
                image_file = prompt['image_file']
                mime_type, _ = guess_type(image_file)
                if mime_type is None:
                    mime_type = 'application/octet-stream'
                with open(image_file,'rb') as f:
                    # url = base64.b64encode(f.read()).decode('utf-8')
                    image_url = f"data:{mime_type};base64,{base64.b64encode(f.read()).decode('utf-8')}"
            messages.append({'role': 'user', 'content': [
                {'type':'image_url','image_url':{'url': image_url}},
                {'type':'text','text': prompt['text']}
                ]})
        else:
            messages.append({'role': 'user', 'content': prompt})
        # print(image_url[0:50])
        resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model=model, cookie=cookie, authorization=f'Bearer {api_key}',user=user)
        answer = resp['message_content']
        messages.append({'role': 'assistant', 'content': answer})
        answers.append(answer)
        resps.append(resp)
        finish_reasons.append(resp['finish_reason'])
        time_consumptions.append(resp['time_consumption'])
        assert resp['status_code'] == 200
        assert 'usage' in resp['json']
        assert 'total_tokens' in resp['json']['usage']
        assert 'completion_tokens' in resp['json']['usage']
        assert 'prompt_tokens' in resp['json']['usage']
        assert 'search_info' in resp['json']
        assert 'mindmap' in resp['json']['search_info']
        print(resp['json'])

@pytest.mark.image2text
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("prompts,user,model",[
    ([{'text':'根据图片的清晰度、以及图片文字与题目相关性和完整度，将图片划分为：题目模糊、没有题目、题目不完整和题目完整四个 标签之一。对判定为题目完整的图片，需要对图片中所有完整的题目文字、表格和图片信息进行有效提取。 ', 'image_file': 'prompt_files/images/ocr1.png', 'assert_contain_all': ['图片中题目完整。','题目内容为：','（多选题）','3、雪天驾驶机动车在有雪泥的路上超车危险较大，其原因是什么？', '▢  A：雪泥可以增加轮胎的附着力', '▢  B：路面更容易打滑', '▢ C：遇紧急情况制动距离长', '▢ D：飞起的雪泥会阻碍视线']}], None,'hunyuan-ocr'),
    ([{'text':'有什么文字？', 'image_file': 'prompt_files/images/flg.jpg', 'assert_contain_any':['我无法提供相关信息。如果你有其他的问题，我会很乐意为你解答。', '很抱歉，我还未学习到如何回答这个问题的内容，暂时无法提供相关信息。']}], None,'hunyuan-ocr')
    ])
def test_ocr(prompts, user, model, record_property):
    messages = []
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    image_url = None
    for prompt in prompts:
        if isinstance(prompt, dict):
            if 'image_url' in prompt:
                image_url = prompt['image_url']
            elif 'image_file' in prompt:
                image_file = prompt['image_file']
                mime_type, _ = guess_type(image_file)
                if mime_type is None:
                    mime_type = 'application/octet-stream'
                with open(image_file,'rb') as f:
                    # url = base64.b64encode(f.read()).decode('utf-8')
                    image_url = f"data:{mime_type};base64,{base64.b64encode(f.read()).decode('utf-8')}"
            messages.append({'role': 'user', 'content': [
                {'type':'image_url','image_url':{'url': image_url}},
                {'type':'text','text': prompt['text']}
                ]})
        else:
            messages.append({'role': 'user', 'content': prompt})
        # print(image_url[0:50])
        resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model=model, cookie=cookie, authorization=f'Bearer {api_key}',user=user)
        answer = resp['message_content']
        messages.append({'role': 'assistant', 'content': answer})
        answers.append(answer)
        resps.append(resp)
        finish_reasons.append(resp['finish_reason'])
        time_consumptions.append(resp['time_consumption'])
        assert resp['status_code'] == 200
        assert 'usage' in resp['json']
        assert 'total_tokens' in resp['json']['usage']
        assert 'completion_tokens' in resp['json']['usage']
        assert 'prompt_tokens' in resp['json']['usage']
        assert 'search_info' in resp['json']
        assert 'mindmap' in resp['json']['search_info']
        if 'assert_contain_all' in prompt:
            for assert_contain in prompt['assert_contain_all']:
                assert assert_contain in answer
        if 'assert_contain_any' in prompt:
            assert any([True for assert_contain in prompt['assert_contain_any'] if assert_contain in answer])
        print(resp['json'])

@pytest.mark.image2text
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("prompt,user",[
    ({'text':'描述一下', 'image_url': 'https://hunyuan-multimodal-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_multimodal/2_img_cont_understand_img_new_76_fbd7939d674997cdb4692d34de8633c4.jpg.jpg?q-sign-algorithm=sha1&q-ak=AKIDQfiXrtaCew0o0vAvhEYdW8AIAoCOlQK7&q-sign-time=1695129332%3B1726233392&q-key-time=1695129332%3B1726233392&q-header-list=host&q-url-param-list=&q-signature=83048bff0d0bbce64fe739411964a55b92ed2b5b'}, 'test1'),
    ])
def test_image2text_400(prompt, user, record_property):
    messages = []
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    image_url = None
    if isinstance(prompt, dict):
        if 'image_url' in prompt:
            image_url = prompt['image_url']
        elif 'image_file' in prompt:
            image_file = prompt['image_file']
            mime_type, _ = guess_type(image_file)
            if mime_type is None:
                mime_type = 'application/octet-stream'
            with open(image_file,'rb') as f:
                # url = base64.b64encode(f.read()).decode('utf-8')
                image_url = f"data:{mime_type};base64,{base64.b64encode(f.read()).decode('utf-8')}"
        messages.append({'role': 'user', 'content': [
            {'type':'image_url','image_url':{'url': image_url}},
            {'type':'text','text': prompt['text']}
            ]})
    else:
        messages.append({'role': 'user', 'content': prompt})
    # print(image_url[0:50])
    resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model='hunyuan-vision', cookie=cookie, authorization=f'Bearer {api_key}',user=user)
    assert resp['status_code'] == 400

@pytest.mark.image2text
@pytest.mark.prerelease
@pytest.mark.production
@pytest.mark.parametrize("prompts,user",[
    ([{'text':'描述一下', 'image_url': 'https://adt-1258344703.cos-internal.ap-guangzhou.tencentcos.cn/prompt/prod/20240305115016_C8c7468f69237%E8%8B%B1%E6%96%87-%E7%9B%B4%E6%96%B9%E5%9B%BE-view.jpg'},'in english'],None),
    ([{'text':'描述一下', 'image_url': 'https://adt-1258344703.cos-internal.ap-guangzhou.tencentcos.cn/prompt/prod/20240305115016_C8c7468f69237%E8%8B%B1%E6%96%87-%E7%9B%B4%E6%96%B9%E5%9B%BE-view.jpg'},'in english'],'test1'),
    ([{'text':'描述一下', 'image_url': 'http://pic.huke88.com/upload/content/2021/03/19/16161170341540.png'},'in english'],'test1'),
    ([{'text':'描述一下', 'image_file': 'prompt_files/images/image_chart.jpg'},'in english'],'test1'),
    ([{'text':'描述一下', 'image_file': 'prompt_files/images/flowchart.jpeg'},'in english'],'test1')
    ])
def test_image2text_chart(prompts, user, record_property):
    messages = []
    # messages = [{'role': 'system', 'content': '你是一个机器人'}]
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    image_url = None
    for prompt in prompts:
        if isinstance(prompt, dict):
            if 'image_url' in prompt:
                image_url = prompt['image_url']
            elif 'image_file' in prompt:
                image_file = prompt['image_file']
                mime_type, _ = guess_type(image_file)
                if mime_type is None:
                    mime_type = 'application/octet-stream'
                with open(image_file,'rb') as f:
                    # url = base64.b64encode(f.read()).decode('utf-8')
                    image_url = f"data:{mime_type};base64,{base64.b64encode(f.read()).decode('utf-8')}"
            messages.append({'role': 'user', 'content': [
                {'type':'image_url','image_url':{'url': image_url}},
                {'type':'text','text': prompt['text']}
                ]})
        else:
            messages.append({'role': 'user', 'content': prompt})
        print(messages)
        resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model='hunyuan-vision', cookie=cookie, authorization=f'Bearer {api_key}',user=user)
        answer = resp['message_content']
        messages.append({'role': 'assistant', 'content': answer})
        answers.append(answer)
        resps.append(resp)
        finish_reasons.append(resp['finish_reason'])
        time_consumptions.append(resp['time_consumption'])
        assert resp['status_code'] == 200
        print(resp['json'])
