import requests

def convs(domain, cookie, userid, order_by='last', source='web', limit=40, offset=0, chat_type=0):
    url = '{domain}/api/convs'.format(domain=domain)
    headers = {
        'cookie': cookie,
        'T-Userid': userid,
        'STAFFNAME': userid
        }
    params = {
        'order_by': order_by,
        'source': source,
        'limit' :limit,
        'offset': offset,
        'chat_type': chat_type
        }
    ret = requests.post(url, headers=headers, params=params, verify=False).json()
    return ret
