import requests
import time
import random
from urllib.parse import urlparse
from ..common.signature import get_authorization

def generate_id(secret_id, secret_signing, domain, cookie, userid):
    url = '{domain}/api/generate/id'.format(domain=domain)
    ct = "application/json; charset=utf-8"
    http_request_method = 'POST'
    # nonce = 1428497820
    nonce = random.randint(1000000000, 9999999999)
    canonical_querystring = ''
    host = urlparse(domain).hostname
    timestamp = int(time.time())
    payload = ''
    authorization = get_authorization(secret_id, secret_signing, http_request_method, canonical_querystring, timestamp, nonce, ct, payload, host)
    # headers = {
    #     'cookie': cookie,
    #     'T-Userid': userid,
    #     'STAFFNAME': userid
    #     }
    headers = {
        # 'cookie': cookie,
        'Authorization': authorization,
        'Content-Type': ct,
        'X-TC-Nonce': str(nonce),
        'Host': host,
        'X-TC-Timestamp': str(timestamp),
        }
    id = requests.post(url, headers=headers, verify=False).text
    return id
