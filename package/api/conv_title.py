import requests

def conv_title(domain, id, cookie, userid, title):
    url = '{domain}/api/conv/title/{id}'.format(domain=domain, id=id)
    headers = {
        'cookie': cookie,
        'T-Userid': userid,
        'STAFFNAME': userid
        }
    payload = {'title': title}
    resp = requests.post(url, headers=headers, json=payload, verify=False)
    return {
        'total_seconds': resp.elapsed.total_seconds()
    }