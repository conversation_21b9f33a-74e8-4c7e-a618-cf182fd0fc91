import requests
import time
import random
from urllib.parse import urlparse
from ..common.signature import get_authorization

def conv(secret_id, secret_signing, domain, id, cookie, userid):
    url = '{domain}/api/conv/{id}'.format(domain=domain, id=id)
    # headers = {
    #     'cookie': cookie,
    #     'T-Userid': userid,
    #     'STAFFNAME': userid
    #     }
    
    ct = "application/json"
    http_request_method = 'GET'
    # nonce = 1428497820
    nonce = random.randint(1000000000, 9999999999)
    canonical_querystring = ''
    host = urlparse(domain).hostname
    timestamp = int(time.time())
    payload = ''
    authorization = get_authorization(secret_id, secret_signing, http_request_method, canonical_querystring, timestamp, nonce, ct, payload, host)
    
    headers = {
        # 'cookie': cookie,
        'Authorization': authorization,
        'Content-Type': ct,
        'X-TC-Nonce': str(nonce),
        'Host': host,
        'X-TC-Timestamp': str(timestamp),
        }
    
    resp = requests.get(url, headers=headers, verify=False)
    print(resp.text)
    return {
        'total_seconds': resp.elapsed.total_seconds(),
        'msg_str': resp.json(),
    }