import requests
import json
import time
import sys
from datetime import datetime
from urllib.parse import urlparse
from ..common.signature import get_authorization
import random



def chat(secret_id, secret_signing, domain, id, prompt, model='gpt_175B_0404', plugin='Adaptive', timeout=300, tries=1, cookie=None, userid=None):
    url = '{domain}/api/chat/{id}'.format(domain=domain, id=id)
    # headers = {
    #     'cookie': cookie,
    #     'T-Userid': userid,
    #     'STAFFNAME': userid
    #     }
    payload = {
        'model': model,
        'plugin': plugin,
        'prompt': prompt
        }
    # print(json.dumps(payload), file=sys.stderr)
    ct = "text/plain; charset=UTF-8"
    # ct = "application/json; charset=utf-8"
    http_request_method = 'POST'
    # nonce = 1428497820
    nonce = random.randint(1000000000, 9999999999)
    canonical_querystring = ''
    host = urlparse(domain).hostname
    timestamp = int(time.time())
    authorization = get_authorization(secret_id, secret_signing, http_request_method, canonical_querystring, timestamp, nonce, ct, json.dumps(payload), host)
    
    headers = {
        # 'cookie': cookie,
        'Authorization': authorization,
        'Content-Type': ct,
        'X-TC-Nonce': str(nonce),
        'Host': host,
        'X-TC-Timestamp': str(timestamp),
        }
    
    for _ in range(tries):
        msg = []
        image_url_low = []
        image_url_high = []
        revoked_msg = None
        lines = []
        msg_type = []
        end_time = None
        break_flag = False
        start_time= datetime.now()

        # from requests import Request
        # req =Request('POST',url, headers=headers, json=payload)
        # prepared = req.prepare()
        # print(prepared.body, file=sys.stderr)

        with requests.post(url, headers=headers, json=payload, stream=True, verify=False, timeout=timeout) as resp:
            for line in resp.iter_lines():
                if line:
                    # if not end_time:
                    #     end_time= datetime.now()
                    line_str = line.decode('utf-8')
                    print(line_str)
                    lines.append(line_str)
                    if line_str.startswith('data: {'):
                        line_json = json.loads(line_str.replace('data:', ''))
                        if not end_time:
                            end_time= datetime.now()
                        if 'type' in line_json:
                            current_type = line_json['type']
                            msg_type.append(current_type)
                            if current_type == 'text' and 'msg' in line_json:
                                msg.append(line_json['msg'])
                        if 'imageUrlLow' in line_json:
                            image_url_low.append('![]({})'.format(line_json['imageUrlLow']))
                        if 'imageUrlHigh' in line_json:
                            image_url_high.append('![]({})'.format(line_json['imageUrlHigh']))
                    elif line_str.startswith('event: message_revoke'):
                        revoked_msg = msg
                        msg = []
                    elif '正在处理您的其他请求，请稍后再试' in line_str:
                        break_flag = True
                        break
                    elif '今日提问次数已达上限，请明日再试' in line_str:
                        print(prompt)
                        raise Exception('今日提问次数已达上限，请明日再试')
        if not end_time:
            end_time= datetime.now()
        msg_str = ''.join(msg)
        revoked_msg_str = ''.join(revoked_msg) if revoked_msg else ''
        if (msg_str and "抱歉，当前访问人数过多，服务繁忙，请稍后再试。" not in msg_str) or image_url_low or break_flag:
            break
        print(lines)
        time.sleep(1)
    return {
        'total_seconds': (end_time-start_time).total_seconds(),
        'revoked_msg_str': revoked_msg_str,
        'msg_str': msg_str,
        'lines': lines,
        'msg_type': msg_type,
        'image_url_low': image_url_low,
        'image_url_high': image_url_high
    }