import hashlib

# prompt_list = []
# for plugin in ('Weather','Time','Poem','Calculating','KnowledgeGraph','Browsing','Adaptive'):
#     with open("prompt/{}.txt".format(plugin), 'r') as f:
#         prompt_list.extend([(x, plugin) for x in f.read().strip().split('\n')])

class Prompt():
    def __init__(self, prompt_tuple):
        self.prompt = prompt_tuple[0]
        self.expected_plugin = prompt_tuple[1]

    def __repr__(self):
        md5 = hashlib.md5()
        md5.update(self.prompt.encode('utf-8'))
        return "Prompt(md5):{}".format(md5.hexdigest())
    
prompt_list = [Prompt(x) for x in prompt_list]