import mysql.connector
from ..config import db_user, db_pwd, db_host, db_name, prompt_policy
import random

multiply_modify_for_text_to_image = []
try:
    if prompt_policy == "none":
        collection_type1 = []
        collection_plugin = []
        collection_type3 = []
        collection_intention = collection_plugin[:]
    elif prompt_policy == "multiply_modify_for_text_to_image":
        db = mysql.connector.connect(user=db_user, password=db_pwd, host=db_host, database='hunyuan')
        cursor = db.cursor()
        def fetch_prompt(query):
            cursor.execute(query)
            values = cursor.fetchall()
            return values

        # l3Names = map(lambda x:x[0],fetch_prompt('SELECT distinct l3Name FROM multiply_modify_for_text_to_image'))
        l3Names = ("图文混合-不相关","图文混合-弱相关","图文混合-强相关","文生图-不相关","文生图-弱相关","文生图-强相关","文生文-不相关","文生文-弱相关","文生文-强相关","图文混合-前一轮文","图文混合-前多轮文","文生文-前一轮","文生文-前多轮")
        for l3_name in l3Names:
            fetched = fetch_prompt('SELECT count(1) from multiply_modify_for_text_to_image WHERE l3Name = "{}" AND ref_answer IS NOT NULL'.format(l3_name))[0][0]
            # multiply_modify_for_text_to_image.extend(fetch_prompt('SELECT id, prompt, l3Name, l1Name, id, predict FROM multiply_modify_for_text_to_image WHERE l3Name = "{}" ORDER BY RAND() LIMIT 100'.format(l3Name[0])))
            unfetched = 100 - fetched
            multiply_modify_for_text_to_image.extend(fetch_prompt('SELECT id, prompt, l3Name, l1Name, id, predict FROM multiply_modify_for_text_to_image WHERE l3Name = "{}" AND ref_answer IS NULL ORDER BY RAND() LIMIT {}'.format(l3_name, unfetched)))
        collection_type1 = []
        collection_plugin = []
        collection_type3 = []
        collection_intention = collection_plugin
        db.close()
    else:
        db = mysql.connector.connect(user=db_user, password=db_pwd, host=db_host, database=db_name)
        cursor = db.cursor()
        def fetch_prompt(query):
            cursor.execute(query)
            values = cursor.fetchall()
            return values
        if prompt_policy == "smoke":
            collection_plugin = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "计算插件" LIMIT 2')
            collection_plugin.extend(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "日历插件" LIMIT 2'))
            collection_plugin.extend(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "藏头诗" LIMIT 2'))
            collection_plugin.extend(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "天气插件" LIMIT 2'))
            collection_plugin.extend(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "搜索插件" LIMIT 2'))
            collection_type3 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 3 LIMIT 3')
            collection_type1 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 1 LIMIT 3')
            collection_intention = collection_plugin
        elif prompt_policy == "sample":
            collection_type1 = []
            level3_type1 = fetch_prompt('SELECT distinct level3 FROM prompt WHERE type = 1')
            for level3 in level3_type1:
                collection_type1.append(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 1 AND level3 = "{}" LIMIT 1'.format(level3[0]))[0])
            collection_plugin = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2')
            collection_type3 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 3')
            collection_intention = collection_plugin
        elif prompt_policy == "intention":
            collection_type1 = []
            collection_plugin = []
            collection_type3 = []
            # level1_type2 = fetch_prompt('SELECT distinct level1 FROM prompt WHERE type = 2')
            # for level1 in level1_type2:
            #     collection_plugin.append(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "{}" LIMIT 200'.format(level1[0]))[0])
            
            # collection_intention = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 in ("代码插件","天气插件","搜索插件","数学插件","日历插件","藏头诗","金融股票插件")')
            collection_intention = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "数学插件"')
            collection_intention.extend(fetch_prompt('SELECT id, questions, "主模型", type, adt_id FROM prompt WHERE type = 1 LIMIT 200'))
            # collection_intention = fetch_prompt('SELECT @rownum:=@rownum+1 as id, prompt, "文生图插件", 2, @rownum as adt_id FROM (select @rownum:=0) a, hunyuan_text_to_image')
        elif prompt_policy == "math":
            collection_type1 = []
            collection_plugin = []
            collection_type3 = []
            collection_intention = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE level1 = "数学插件"')
        elif prompt_policy == "whole":
            collection_type1 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 1')
            collection_plugin = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2')
            collection_type3 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 3')
            collection_intention = collection_plugin
            # collection_plugin_poem = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "藏头诗"')
        elif prompt_policy == "load":
            collection_type1 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 1')
            # collection_plugin = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2')
            level1_type2 = fetch_prompt('SELECT distinct level1 FROM prompt WHERE type = 2')
            collection_plugin = []
            for level1 in level1_type2:
                collection_plugin.append(fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "{}" LIMIT 30'.format(level1[0]))[0])
            # collection_type3 = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 3')
            # collection_plugin = []
            collection_type3 = []
            collection_intention = collection_plugin*100
            random.seed(0)
            random.shuffle(collection_intention)
            collection_type1 = []
            # collection_plugin_poem = fetch_prompt('SELECT id, questions, level1, type, adt_id FROM prompt WHERE type = 2 AND level1 = "藏头诗"')
        db.close()
except Exception:
    collection_type1 = []
    collection_plugin = []
    collection_type3 = []
    collection_intention = collection_plugin[:]