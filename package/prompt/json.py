import os
import json
from ..prompt.adt import collection_type1, collection_type3, collection_plugin, collection_intention, multiply_modify_for_text_to_image

file_path = 'collection_intention.json'
if os.path.exists(file_path):
    with open(file_path, 'r') as f:
        collection_intention = json.load(f)
file_path = 'multiply_modify_for_text_to_image.json'
if os.path.exists(file_path):
    with open(file_path, 'r') as f:
        multiply_modify_for_text_to_image = json.load(f)