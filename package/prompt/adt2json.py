import os
import json
from .adt import collection_type1, collection_type3, collection_plugin, collection_intention, multiply_modify_for_text_to_image

with open(os.path.join(os.getcwd(), "collection_intention.json"),"w") as file:
    file.write(json.dumps(collection_intention))
with open(os.path.join(os.getcwd(), "multiply_modify_for_text_to_image.json"),"w") as file:
    file.write(json.dumps(multiply_modify_for_text_to_image))