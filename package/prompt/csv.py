import os
import json
import pandas as pd
file_path = 'collection_qa_t2t_deny.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    # df = df[df["标签"].str.contains('拒绝', na=False)]
    for index,prompt in df.iterrows():
        print(json.dumps(prompt['问题']))
    collection_qa_t2t_deny = [(index, json.dumps(prompt['问题']), 'QualityAssurance', 4, index, prompt['参考答案']) for index,prompt in df.iterrows()]
else:
    collection_qa_t2t_deny = []

file_path = 'collection_qa_t2t_allow.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    # df = df[df["标签"].str.contains('通过', na=False)]
    for index,prompt in df.iterrows():
        print(prompt['问题'])
    collection_qa_t2t_allow = [(index, json.dumps(prompt['问题']), 'QualityAssurance', 4, index, prompt['参考答案']) for index,prompt in df.iterrows()]
else:
    collection_qa_t2t_allow = []

file_path = 'quality_assurance_img.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    df = df[df["标签"].str.contains('拒绝', na=False)]
    collection_sensitive_text2image = [(index, json.dumps(prompt['prompt']), 'QualityAssurance', 4, index) for index,prompt in df.iterrows()]
else:
    collection_sensitive_text2image = []

file_path = 'credible_model.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    collection_credible_model = [(index, json.dumps(prompt['问题']), '可信模型', 4, index) for index,prompt in df.iterrows()]
else:
    collection_credible_model = []

file_path = 'intention.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    # collection_intention_csv = [(index, json.dumps(prompt['问题']), json.dumps(prompt['插件']), 4, index) for index,prompt in df.iterrows()]
    collection_intention_csv = [(index, json.dumps(prompt.get('问题',prompt.get('相关文章',prompt.get('')))), prompt.get('插件',prompt.get('一级类目','主模型')), 4, index) for index,prompt in df.iterrows()]
else:
    collection_intention_csv = []

file_path = 'persona.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    personas = [(index, prompt['prompt'], '可信模型', 4, index, prompt['ref_answer']) for index,prompt in df.iterrows()]
    personas_dict = {}
    for id,prompt,level1,type,adt_id,answer in personas:
        if prompt not in personas_dict:
            personas_dict[prompt] = [id,prompt,level1,type,adt_id,[]]
        personas_dict[prompt][-1].append(answer)
    collection_persona = list(personas_dict.values())
else:
    collection_persona = []

file_path = 'record_persona.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    personas = [(index, prompt['prompt'], '主模型', 4, index, prompt['ref_answer']) for index,prompt in df.iterrows()]
    personas_dict = {}
    for id,prompt,level1,type,adt_id,answer in personas:
        if prompt not in personas_dict:
            personas_dict[prompt] = [id,prompt,level1,type,adt_id,[]]
        personas_dict[prompt][-1].append(answer)
    collection_record_persona = list(personas_dict.values())
else:
    collection_record_persona = []

file_path = 'openapi_and_webapi.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    collection_openapi_and_webapi = [(index, prompt['questions'], prompt['level1'], 4, index, prompt['ref_answers'], prompt['ref_answer_regexs']) for index,prompt in df.iterrows()]
else:
    collection_openapi_and_webapi = []

file_path = 'collection_multi_round_text2image.csv'
if os.path.exists(file_path):
    df = pd.read_csv(file_path, header=0, keep_default_na=False)
    # for index,prompt in df.iterrows():
    #     print(json.dumps(prompt['问题']))
    # collection_multi_round_text2image = [(index, json.dumps(prompt['问题']), 'Draw', 4, prompt['类目'], prompt['预测绘图结果']) for index,prompt in df.iterrows()]
    # collection_multi_round_text2image = [(index, json.dumps(prompt.get('prompt', prompt.get('问题'))), 'Draw', 4, prompt.get('id',index), prompt.get('predict', prompt.get('预测绘图结果'))) for index,prompt in df.iterrows()]
    collection_multi_round_text2image = [(index, json.dumps(prompt.get('prompt', prompt.get('问题'))), 'Draw', 4, prompt.get('adt_id',prompt.get('id',index)), prompt.get('predict', prompt.get('预测绘图结果',prompt.get('参考答案','')))) for index,prompt in df.iterrows()]
else:
    collection_multi_round_text2image = []