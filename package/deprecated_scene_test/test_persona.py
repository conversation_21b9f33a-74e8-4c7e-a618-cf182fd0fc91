import re
import pytest
import json
from allure import attach, attachment_type
from package.config import domain, cookie, userids
from package.common.prompt_map import prompt_plugin_map_reverse
from package.api.generate_id import generate_id
from package.api.chat import chat
from package.api.conv import conv
from package.openapi.v1_chat_completions import v1_chat_completions
# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

from ..prompt.csv import collection_persona, collection_record_persona

@pytest.fixture(scope='module')
def start_chat(request):
    adt_id = request.param[4]
    prompt_type = request.param[3]
    # prompt_data = json.loads(request.param[1])
    prompt_data = request.param[1]
    if isinstance(prompt_data,list):
        prompts = json.loads(request.param[1])
        prompts = json.loads(prompts)
    else:
        prompts = [prompt_data]
        prompt = prompt_data
        print(prompts)
    prompt = prompts[0]
    ref_answer = request.param[5]
    # print('prompts {}'.format(prompts))
    # print('prompt {}'.format(prompt))
    # print('prompt {}'.format(prompts[1]))
    # print('type{}'.format(type(prompts)))
    # exit(0)
    expected_plugin = prompt_plugin_map_reverse.get(request.param[2], 'Adaptive') 
    userid_list = userids.split(',')
    userid = userid_list[int(request.config.workerinput["workerid"].replace("gw", ""))%len(userid_list)]
    id = generate_id(domain, cookie, userid)
    assert re.match('([A-Za-z0-9]+-){4}[A-Za-z0-9]+', id)
    # prompts = json.loads(request.param[1])
    answers = []
    for prompt in prompts:
        answer = chat(domain=domain, cookie=cookie, userid=userid, id=id, prompt=prompt, model='gpt_175B_0404', plugin='Adaptive')
        answers.append({
            'total_seconds': answer['total_seconds'],
            'msg_str': answer['msg_str'],
            'msg_type': answer['msg_type'],
            'lines': answer['lines'],
            'id': id,
            'prompt': prompt,
            'expected_plugin': expected_plugin,
            'prompt_type': prompt_type,
            'adt_id': adt_id,
            'userid': userid,
            'image_url_low': answer['image_url_low'],
            'image_url_high': answer['image_url_high'],
            'answers': answers
            })
        print(answers)
    answer = answers[0]
    yield {
        'total_seconds': answer['total_seconds'],
        'msg_str': answer['msg_str'],
        'msg_type': answer['msg_type'],
        'lines': answer['lines'],
        'id': id,
        'prompt': prompt,
        'expected_plugin': expected_plugin,
        'prompt_type': prompt_type,
        'adt_id': adt_id,
        'userid': userid,
        'image_url_low': answer['image_url_low'],
        'image_url_high': answer['image_url_high'],
        'answers': answers,
        'ref_answer': ref_answer
    }


@pytest.mark.parametrize('start_chat', 100*collection_persona, ids=100*['id={}, type={}, duplicated=0'.format(i[0], i[3]) for i in collection_persona], indirect=True)
def test_persona(start_chat, record_property):
    record_property('adt_id', start_chat['adt_id'])
    msg_str = start_chat['msg_str']
    prompt = start_chat['prompt']
    lines = start_chat['lines']
    conversation = conv(domain=domain, id=start_chat['id'], cookie=cookie, userid=start_chat['userid'])
    conversation_content = conversation['msg_str']
    plugin_ids = conversation_content['convs'][-1]['pluginContext']['pluginIds']
    print("问题：{}".format(prompt))
    print("收到的回答：{}".format(msg_str))
    print("实际执行的插件：{}".format(plugin_ids))
    record_property('plugin_ids', plugin_ids)
    record_property('questions', prompt)
    record_property('answers', msg_str)
    assert msg_str in start_chat['ref_answer']


@pytest.mark.parametrize('start_chat', 20*collection_record_persona, ids=20*['id={}, type={}, duplicated=0'.format(i[0], i[3]) for i in collection_record_persona], indirect=True)
def test_record_persona(start_chat, record_property):
    record_property('adt_id', start_chat['adt_id'])
    msg_str = start_chat['msg_str']
    prompt = start_chat['prompt']
    lines = start_chat['lines']
    conversation = conv(domain=domain, id=start_chat['id'], cookie=cookie, userid=start_chat['userid'])
    conversation_content = conversation['msg_str']
    plugin_ids = conversation_content['convs'][-1]['pluginContext']['pluginIds']
    print("问题：{}".format(prompt))
    print("收到的回答：{}".format(msg_str))
    print("实际执行的插件：{}".format(plugin_ids))
    record_property('plugin_ids', plugin_ids)
    record_property('questions', prompt)
    record_property('answers', msg_str)

