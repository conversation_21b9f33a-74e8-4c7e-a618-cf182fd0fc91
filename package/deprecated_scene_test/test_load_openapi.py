import json

import pytest

from package.common.prompt_map import prompt_plugin_map_reverse
from package.config import api_key, cookie, openapi_domain
from package.openapi.v1_chat_completions import v1_chat_completions

from ..prompt.adt import collection_plugin, collection_type1, collection_type3
from ..prompt.csv import collection_openapi_and_webapi

# try:
#     from ..prompt.prompt_plugin import prompt_list
# except ImportError:
#     prompt_list = []

prompt_list_all = [*collection_type1, *collection_type3, *collection_plugin, *collection_openapi_and_webapi]

@pytest.fixture(scope='module')
def start_chat_openapi(request):
    adt_id = request.param[4]
    prompt_type = request.param[3]
    # prompt_data = json.loads(request.param[1])
    prompts = json.loads(request.param[1])
    print(f"prompts:{prompts},length:{len(prompts)}")
    if isinstance(prompts,str):
        prompts = [prompts]
    prompt = prompts[0]
    ref_answers = json.loads(request.param[5]) if len(request.param)>5 and request.param[5] else ''
    ref_answers = [ref_answers] if ref_answers and isinstance(ref_answers,str) else ref_answers
    ref_answer_regexs = json.loads(request.param[6]) if len(request.param)>6 and request.param[6] else ''
    ref_answer_regexs = [ref_answer_regexs] if ref_answer_regexs and isinstance(ref_answer_regexs,str) else ref_answer_regexs
    # print('prompts {}'.format(prompts))
    # print('prompt {}'.format(prompt))
    # print('prompt {}'.format(prompts[1]))
    # print('type{}'.format(type(prompts)))
    # exit(0)
    expected_plugin = prompt_plugin_map_reverse.get(request.param[2], 'Adaptive') 
    # prompts = json.loads(request.param[1])
    messages = []
    resps = []
    answers = []
    finish_reasons = []
    time_consumptions = []
    for prompt in prompts:
        messages.append({'role': 'user', 'content': prompt})
        resp = v1_chat_completions(domain=openapi_domain, api_key=api_key, messages=messages, model='hunyuan', cookie=cookie, enhance=False)
        answer = resp['message_content'] 
        messages.append({'role': 'assistant', 'content': answer})
        answers.append(answer)
        resps.append(resp)
        finish_reasons.append(resp['finish_reason'])
        time_consumptions.append(resp['time_consumption'])
    yield {
        'adt_id': adt_id,
        'prompts': prompts,
        'prompt_type': prompt_type,
        'answers': answers,
        'resps': resps,
        'messages' : messages,
        'finish_reasons': finish_reasons,
        'time_consumptions': time_consumptions,
        'expected_plugin': expected_plugin,
        'ref_answers': ref_answers,
        'ref_answer_regexs': ref_answer_regexs
    }

@pytest.mark.parametrize('start_chat_openapi', prompt_list_all, ids=[f'id={i[0]}, type={i[3]}, duplicated=0' for i in prompt_list_all], indirect=True)
def test_success_openapi(start_chat_openapi, record_property):
    record_property('adt_id', start_chat_openapi['adt_id'])
    prompts = start_chat_openapi['prompts']
    resps = start_chat_openapi['resps']
    answers = start_chat_openapi['answers']
    print(f"问题：{prompts}")
    print(f"收到的响应：{resps}")
    assert '请求频繁，请稍后再试' not in answers[0]
    # try:
    #     for resp in start_chat_openapi['resps']:
    #         if 'sensitive' != resp['finish_reason']:
    #             assert not re.search(r'抱歉｜不能回答｜无法回答', resp['message_content'])
    # except AssertionError:
    #     print("问题：{}".format(prompts))
    #     print("收到的回答：{}".format(answers))
    #     raise

