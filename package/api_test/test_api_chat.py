from ..config import domain, cookie, userid, secret_ids, secret_keys
from ..api.chat import chat
from package.common.signature import get_signature

secret_ids = secret_ids.split(',')
secret_keys = secret_keys.split(',')

def test_api_chat(id='c9b45e7b-898b-40dc-86fd-b677142cecd1', prompt='以花香满径为头作一首藏头诗', model='gpt_175B_0404', plugin='Adaptive'):
    secret_signing = get_signature(secret_keys[0])
    ret = chat(secret_ids[0], secret_signing=secret_signing, domain=domain, cookie=cookie, userid=userid, id=id, prompt=prompt, model=model, plugin=plugin)
    # total_seconds = ret['total_seconds']
    msg_str = ret['msg_str']
    assert len(msg_str) != 0
    assert '花' in ret['msg_str']