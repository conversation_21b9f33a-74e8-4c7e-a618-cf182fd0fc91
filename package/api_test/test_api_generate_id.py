import re
from ..config import domain, cookie, userid, secret_ids, secret_keys
from ..api.generate_id import generate_id
from package.common.signature import get_signature

secret_ids = secret_ids.split(',')
secret_keys = secret_keys.split(',')

def test_api_generate_id():
    secret_signing = get_signature(secret_keys[0])
    id = generate_id(secret_ids[0], secret_signing=secret_signing, domain=domain, cookie=cookie, userid=userid)
    assert re.match('([A-Za-z0-9]+-){4}[A-Za-z0-9]+', id)
