from ..config import domain, cookie, secret_ids, secret_keys
from ..api.conv import conv
from package.common.signature import get_signature

secret_ids = secret_ids.split(',')
secret_keys = secret_keys.split(',')

def test_api_conv(id='e742bd2c-bcc4-4846-a491-1d02b414b341'):
    secret_signing = get_signature(secret_keys[0])
    ret = conv(secret_ids[0], secret_signing=secret_signing, domain=domain, id=id, cookie=cookie, userid='')
    total_seconds = ret['total_seconds']
    msg_str = ret['msg_str']
    assert 'error' not in msg_str
    # TODO: 断言（待明确）