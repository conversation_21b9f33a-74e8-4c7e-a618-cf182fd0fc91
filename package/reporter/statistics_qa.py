import os
import pandas as pd

data = pd.read_json(path_or_buf=os.path.join(os.getcwd(), 'log.jsonl'), lines=True)

# setup和teardown不计入
test_all = data[data.when == "call"]

# 安全审核
test_qa_text2text_all = test_all[test_all.nodeid.str.contains('::test_quality_assurance_text2text_deny', na=False)]
count_qa_text2text_all = len(test_qa_text2text_all)
count_qa_text2text_passed = len(test_qa_text2text_all[test_qa_text2text_all.outcome == 'passed'])
count_qa_text2text_skipped = len(test_qa_text2text_all[test_qa_text2text_all.outcome == 'skipped'])
count_qa_text2text_unskipped = count_qa_text2text_all - count_qa_text2text_skipped
test_qa_text2text_result = {
    'total': count_qa_text2text_all,
    'passed': count_qa_text2text_passed,
    'skipped': count_qa_text2text_skipped,
    'percentage': '{}%'.format(100*count_qa_text2text_passed/count_qa_text2text_unskipped) if count_qa_text2text_unskipped!=0 else "NA"
}
if not test_qa_text2text_all.empty:
    adt_id = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
    )
    # plugin_ids = test_qa_text2text_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
    #     )
    questions = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    ref_answers = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='ref_answers'][0]
        )
    answers = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
        )
    results = test_qa_text2text_all['outcome']
    all_humans = []
    for index,row in test_qa_text2text_all.iterrows():
        qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
        ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
        humans = []
        for n in range(len(qes)):
            human = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
            humans.append(human)
        all_humans.append(''.join(humans))
    df = pd.DataFrame({
        'id': adt_id,
        # '实际插件': plugin_ids,
        '问题': questions,
        '参考答案': ref_answers,
        '答案': answers,
        '结果': results,
        '人工': all_humans
    })
    df.to_csv('qa_text2text_deny_all.csv',encoding='utf-8-sig',index=False)

    # test_qa_text2text_failed = test_qa_text2text_all[test_qa_text2text_all.outcome == 'failed']
    # if not test_qa_text2text_failed.empty:
    #     adt_id = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
    #         )
    #     # plugin_ids = test_qa_text2text_failed.user_properties.apply(
    #     #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
    #     #     )
    #     questions = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
    #         )
    #     answers = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
    #         )
    #     results = test_qa_text2text_failed['outcome']
    #     all_humans = []
    #     for index,row in test_qa_text2text_failed.iterrows():
    #         qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
    #         ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
    #         humans = []
    #         for n in range(len(qes)):
    #             human = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
    #             humans.append(human)
    #         all_humans.append(''.join(humans))
    #     df = pd.DataFrame({
    #         'id': adt_id,
    #         # '实际插件': plugin_ids,
    #         '问题': questions,
    #         '答案': answers,
    #         '结果': results,
    #         '人工': all_humans
    #     })
    #     df.to_csv('qa_text2text_deny_failed.csv',encoding='utf-8-sig',index=False)
        # df = pd.DataFrame({
        #     'class1': '安全审核',
        #     'class2': '多轮会话',
        #     'class3': '黑样本',
        #     'tags': []
        #     'qid': adt_id,
        #     # '实际插件': plugin_ids,
        #     'model_code':['model_qa']
        #     '问题': questions,
        #     '答案': answers,
        #     '结果': results,
        #     '人工': all_humans
        # })

# 安全审核
test_qa_text2text_all = test_all[test_all.nodeid.str.contains('::test_quality_assurance_text2text_allow', na=False)]
count_qa_text2text_all = len(test_qa_text2text_all)
count_qa_text2text_passed = len(test_qa_text2text_all[test_qa_text2text_all.outcome == 'passed'])
count_qa_text2text_skipped = len(test_qa_text2text_all[test_qa_text2text_all.outcome == 'skipped'])
count_qa_text2text_unskipped = count_qa_text2text_all - count_qa_text2text_skipped
test_qa_text2text_result = {
    'total': count_qa_text2text_all,
    'passed': count_qa_text2text_passed,
    'skipped': count_qa_text2text_skipped,
    'percentage': '{}%'.format(100*count_qa_text2text_passed/count_qa_text2text_unskipped) if count_qa_text2text_unskipped!=0 else "NA"
}
if not test_qa_text2text_all.empty:
    adt_id = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
    )
    # plugin_ids = test_qa_text2text_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
    #     )
    questions = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    ref_answers = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='ref_answers'][0]
        )
    answers = test_qa_text2text_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
        )
    results = test_qa_text2text_all['outcome']
    all_humans = []
    for index,row in test_qa_text2text_all.iterrows():
        qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
        ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
        humans = []
        for n in range(len(qes)):
            human = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
            humans.append(human)
        all_humans.append(''.join(humans))
    df = pd.DataFrame({
        'id': adt_id,
        # '实际插件': plugin_ids,
        '问题': questions,
        '参考答案': ref_answers,
        '答案': answers,
        '结果': results,
        '人工': all_humans
    })
    df.to_csv('qa_text2text_allow_all.csv',encoding='utf-8-sig',index=False)

    # test_qa_text2text_failed = test_qa_text2text_all[test_qa_text2text_all.outcome == 'failed']
    # if not test_qa_text2text_failed.empty:
    #     adt_id = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
    #         )
    #     # plugin_ids = test_qa_text2text_failed.user_properties.apply(
    #     #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
    #     #     )
    #     questions = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
    #         )
    #     answers = test_qa_text2text_failed.user_properties.apply(
    #         lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
    #         )
    #     results = test_qa_text2text_failed['outcome']
    #     all_humans = []
    #     for index,row in test_qa_text2text_failed.iterrows():
    #         qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
    #         ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
    #         humans = []
    #         for n in range(len(qes)):
    #             human = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
    #             humans.append(human)
    #         all_humans.append(''.join(humans))
    #     df = pd.DataFrame({
    #         'id': adt_id,
    #         # '实际插件': plugin_ids,
    #         '问题': questions,
    #         '答案': answers,
    #         '结果': results,
    #         '人工': all_humans
    #     })
    #     df.to_csv('qa_text2text_allow_failed.csv',encoding='utf-8-sig',index=False)

# 安全审核文生图
test_qa_text2image_all = test_all[test_all.nodeid.str.contains('::test_quality_assurance_text2image', na=False)]
count_qa_text2image_all = len(test_qa_text2image_all)
count_qa_text2image_passed = len(test_qa_text2image_all[test_qa_text2image_all.outcome == 'passed'])
count_qa_text2image_skipped = len(test_qa_text2image_all[test_qa_text2image_all.outcome == 'skipped'])
count_qa_text2image_unskipped = count_qa_text2image_all - count_qa_text2image_skipped
test_qa_text2image_result = {
    'total': count_qa_text2image_all,
    'passed': count_qa_text2image_passed,
    'skipped': count_qa_text2image_skipped,
    'percentage': '{}%'.format(100*count_qa_text2image_passed/count_qa_text2image_unskipped) if count_qa_text2image_unskipped!=0 else "NA"
}
if not test_qa_text2image_all.empty:
    test_qa_text2image_passed = test_qa_text2image_all[test_qa_text2image_all.outcome == 'passed']
    test_qa_text2image_failed = test_qa_text2image_all[test_qa_text2image_all.outcome == 'failed']
    adt_id = test_qa_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
        )
    questions = test_qa_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    answers = test_qa_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
        )
    results = test_qa_text2image_all['outcome']
    jsons = test_qa_text2image_failed.user_properties.apply(
        lambda val:len([item[1] for item in val if item[0]=='image_url_low'][0])!=0 and {
            'id': [item[1] for item in val if item[0]=='adt_id'][0],
            'prompt': [item[1] for item in val if item[0]=='questions'][0],
            'url_small': [item[1] for item in val if item[0]=='image_url_low'][0],
            'url_big': [item[1] for item in val if item[0]=='image_url_high'][0],
            'answer': [item[1] for item in val if item[0]=='answers'][0],
            }
        )
    df = pd.DataFrame({
        'id': adt_id,
        'prompt': questions,
        'answer': answers,
        'result': results,
        'json' : jsons
    })
    df.to_csv('qa_text2image.csv',encoding='utf-8-sig',index=False)
    
    if not test_qa_text2image_passed.empty:
        adt_id = test_qa_text2image_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        questions = test_qa_text2image_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        answers = test_qa_text2image_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
            )
        df = pd.DataFrame({
            'id': adt_id,
            'prompt': questions,
            'answer': answers
        })
        df.to_csv('qa_text2image_passed.csv',encoding='utf-8-sig',index=False)
    if not test_qa_text2image_failed.empty:
        adt_id = test_qa_text2image_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        questions = test_qa_text2image_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        answers = test_qa_text2image_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
            )
        result = test_qa_text2image_failed.user_properties.apply(
            lambda val:{
                'id': [item[1] for item in val if item[0]=='adt_id'][0],
                'prompt': [item[1] for item in val if item[0]=='questions'][0],
                'url_small': [item[1] for item in val if item[0]=='image_url_low'][0],
                'url_big': [item[1] for item in val if item[0]=='image_url_high'][0],
                'answer': [item[1] for item in val if item[0]=='answers'][0],
                }
            )
        df = pd.DataFrame({
            'result': result
        })
        df.to_csv('qa_text2image_failed.csv',encoding='utf-8-sig',index=False)
