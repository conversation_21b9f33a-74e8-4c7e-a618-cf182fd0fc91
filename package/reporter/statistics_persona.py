import os
import re
import pandas as pd
import base64
import jinja2
import numpy as np
from io import BytesIO
from package.config import compare_policy, version, version_to_compare, last_n
from package.common.prompt_map import prompt_type_map, prompt_plugin_map
from package.common.models import ExecutionHistory

data = pd.read_json(path_or_buf=os.path.join(os.getcwd(), 'log.jsonl'), lines=True)

# setup和teardown不计入
test_all = data[data.when == "call"]

# 人格测试
test_persona_all = test_all[test_all.nodeid.str.contains('::test_persona', na=False)]
test_persona_result = {}
count_persona_all = len(test_persona_all)
count_persona_passed = len(test_persona_all[test_persona_all.outcome == 'passed'])
count_persona_skipped = len(test_persona_all[test_persona_all.outcome == 'skipped'])
count_persona_unskipped = count_persona_all - count_persona_skipped
test_persona_result = {
    'total': count_persona_all,
    'passed': count_persona_passed,
    'skipped': count_persona_skipped,
    'percentage': '{}%'.format(100*count_persona_passed/count_persona_unskipped) if count_persona_unskipped!=0 else "NA"
}
print(test_persona_result)
if not test_persona_all.empty:
    test_persona_failed = test_persona_all[test_persona_all.outcome == 'failed']
    if not test_persona_failed.empty:
        adt_id = test_persona_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        plugin_ids = test_persona_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
            )
        questions = test_persona_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        answers = test_persona_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
            )
        df = pd.DataFrame({
            'adt_id': adt_id,
            '实际插件': plugin_ids,
            '问题': questions,
            '答案': answers
        })
        df.sort_values(by=['adt_id']).to_csv('persona_failed.csv',encoding='utf-8-sig')

# 人格记录
test_record_persona_all = test_all[test_all.nodeid.str.contains('::test_record_persona', na=False)]
test_record_persona_result = {}
count_record_persona_all = len(test_persona_all)
count_record_persona_passed = len(test_persona_all[test_persona_all.outcome == 'passed'])
count_record_persona_skipped = len(test_persona_all[test_persona_all.outcome == 'skipped'])
count_record_persona_unskipped = count_record_persona_all - count_record_persona_skipped
test_record_persona_result = {
    'total': count_record_persona_all,
    'passed': count_record_persona_passed,
    'skipped': count_record_persona_skipped,
    'percentage': '{}%'.format(100*count_record_persona_passed/count_record_persona_unskipped) if count_record_persona_unskipped!=0 else "NA"
}
print(test_record_persona_result)
if not test_record_persona_all.empty:
    test_record_persona_passed = test_record_persona_all[test_record_persona_all.outcome == 'passed']
    if not test_record_persona_passed.empty:
        adt_id = test_record_persona_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        plugin_ids = test_record_persona_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
            )
        questions = test_record_persona_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        answers = test_record_persona_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
            )
        df = pd.DataFrame({
            'adt_id': adt_id,
            '实际插件': plugin_ids,
            '问题': questions,
            '答案': answers
        })
        df.drop_duplicates(subset=['adt_id','答案'],inplace=True)
        df.sort_values(by=['adt_id']).to_csv('record_persona_export.csv',encoding='utf-8-sig')