import os
import re
import pandas as pd
import base64
import jinja2
import numpy as np
from io import BytesIO
from package.config import compare_policy, version, version_to_compare, last_n
from package.common.prompt_map import prompt_type_map, prompt_plugin_map
from package.common.models import ExecutionHistory

data = pd.read_json(path_or_buf=os.path.join(os.getcwd(), 'log.jsonl'), lines=True)

# setup和teardown不计入
test_all = data[data.when == "call"]

# 链路测试
test_success_all = test_all[test_all.nodeid.str.contains('::test_success', na=False)]
test_success_result = {}
for prompt_type in ['全部', *prompt_type_map.keys()]:
    if prompt_type == '全部':
        prompt_type_name = prompt_type
        test_success_type_all = test_success_all
    else:
        prompt_type_name = prompt_type_map[prompt_type]
        test_success_type_all = test_success_all[test_success_all.nodeid.str.contains('type={}'.format(prompt_type), na=False)]
    count_success_type_all = len(test_success_type_all)
    count_success_type_passed = len(test_success_type_all[test_success_type_all.outcome == 'passed'])
    count_success_type_skipped = len(test_success_type_all[test_success_type_all.outcome == 'skipped'])
    count_susscess_unskipped = count_success_type_all - count_success_type_skipped
    test_success_result[prompt_type_name] = {
        'total': count_success_type_all,
        'passed': count_success_type_passed,
        'skipped': count_success_type_skipped,
        'percentage': '{}%'.format(100*count_success_type_passed/count_susscess_unskipped) if count_susscess_unskipped!=0 else "NA"
    }

# 意图识别
test_intention_all = test_all[test_all.nodeid.str.contains('::test_intention', na=False)]
test_intention_all = test_intention_all[test_intention_all.user_properties.apply(lambda val:len(val)!=0 and len([item[1] for item in val if item[0]=='expected_plugin'])!=0)]
test_intention_result = {}
if not test_intention_all.empty:
    for prompt_plugin in ['全部', *prompt_plugin_map.keys()]:
        if prompt_plugin == '全部':
            prompt_plugin_name = prompt_plugin
            test_intention_plugin_all = test_intention_all
        else:
            prompt_plugin_name = prompt_plugin_map[prompt_plugin]
            test_intention_plugin_all = test_intention_all[test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and len([item[1] for item in val if item[0]=='expected_plugin'])!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]==prompt_plugin
                )]
        count_intention_plugin_all = len(test_intention_plugin_all)
        count_intention_plugin_passed = 0 if test_intention_plugin_all.empty else len(test_intention_plugin_all[test_intention_plugin_all.outcome == 'passed'])
        count_intention_plugin_skipped = 0 if test_intention_plugin_all.empty else len(test_intention_plugin_all[test_intention_plugin_all.outcome == 'skipped'])
        count_intention_plugin_unskipped = count_intention_plugin_all - count_intention_plugin_skipped
        test_intention_result[prompt_plugin_name] = {
            'total': count_intention_plugin_all,
            'passed': count_intention_plugin_passed,
            'skipped': count_intention_plugin_skipped,
            'percentage': '{}%'.format(100*count_intention_plugin_passed/count_intention_plugin_unskipped) if count_intention_plugin_unskipped!=0 else "NA"
        }
    if not test_intention_all.empty:
        test_intention_failed = test_intention_all[test_intention_all.outcome == 'failed']
        test_intention_passed = test_intention_all[test_intention_all.outcome == 'passed']
        if not test_intention_all.empty:
            index_id = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='index_id'][0]
                )
            adt_id = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
                )
            expected_plugin = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]
                )
            plugin_ids = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
                )
            # intent_plugin_ids = test_intention_all.user_properties.apply(
            #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][-1]
            #     )
            intent_plugin_ids = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='intent_plugin_ids'][0][0]
                )
            questions = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
                )
            answers = test_intention_all.user_properties.apply(
                lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
                )
            results = test_intention_all['outcome']
            df = pd.DataFrame({
                'index': index_id,
                # 'adt_id': adt_id,
                '预期插件': expected_plugin,
                # '实际插件': plugin_ids,
                '意图插件': intent_plugin_ids,
                'result': results,
                # '问题': questions,
                '答案': answers
            }).set_index('index')
            df.to_csv('intention_result.csv',encoding='utf-8-sig')
            df_o = pd.read_csv('intention.csv')
            print(df_o.join(df))
            df_o.join(df).to_csv('intention_merged.csv',encoding='utf-8-sig',index=False)


# 多轮改写文生图
test_multi_round_text2image_all = test_all[test_all.nodeid.str.contains('::test_multi_round_text2image', na=False)]
count_multi_round_text2image_all = len(test_multi_round_text2image_all)
count_multi_round_text2image_passed = len(test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'passed'])
count_multi_round_text2image_skipped = len(test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'skipped'])
count_multi_round_text2image_unskipped = count_multi_round_text2image_all - count_multi_round_text2image_skipped
test_multi_round_text2image_result = {
    'total': count_multi_round_text2image_all,
    'passed': count_multi_round_text2image_passed,
    'skipped': count_multi_round_text2image_skipped,
    'percentage': '{}%'.format(100*count_multi_round_text2image_passed/count_multi_round_text2image_unskipped) if count_multi_round_text2image_unskipped!=0 else "NA"
}
if not test_multi_round_text2image_all.empty:
    test_multi_round_text2image_passed = test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'passed']
    test_multi_round_text2image_failed = test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'failed']
    index_id = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [int(item[1]) for item in val if item[0]=='index_id'][0]
        )
    adt_id = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
        )
    questions = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    answers = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
        )
    total_seconds = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='total_seconds'][0]
        )
    max_seconds = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='max_seconds'][0]
        )
    average_seconds = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='average_seconds'][0]
        )
    # prediction = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='prediction'][0]
    #     )
    # final_answer = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='final_answer'][0]
    #     )
    # context = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='context'][0]
    #     )
    plugin_ids = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0]
        )
    results = test_multi_round_text2image_all['outcome']

    all_contexts = []
    all_final_answers = []
    for index,row in test_multi_round_text2image_all.iterrows():
        qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
        ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
        contexts = []
        for n in range(len(qes)-1):
            context = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
            contexts.append(context)
        contexts.append('人类：{}\n'.format(qes[-1]))
        all_contexts.append(''.join(contexts))
        all_final_answers.append(ans[-1])
        
    df = pd.DataFrame({
        'id': index_id,
        # 'adt_id': adt_id,
        # 'prompt': questions,
        # 'predict': prediction,
        'answer': answers,
        'total_seconds' : total_seconds,
        'max_seconds' : max_seconds,
        'average_seconds' : average_seconds,
        'result': results,
        # 'plugin_ids': plugin_ids,
        # 'context': all_contexts,
        # 'final_answer': all_final_answers,
        # 'context' : context,
        # 'final_answer': final_answer
    }).set_index('id')
    df.to_csv('multi_rounds_text2image.csv',encoding='utf-8-sig',index=False)
    df_o = pd.read_csv('collection_multi_round_text2image.csv')
    print(df_o.join(df))
    df_o.join(df).to_csv('multi_rounds_text2image_merged.csv',encoding='utf-8-sig',index=False)
