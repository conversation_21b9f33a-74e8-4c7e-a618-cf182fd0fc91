<html>
<body>
<h2>版本比对</h2>
<br/>

<h3>整体执行情况</h3>
<table>
    <tr>
        <td>版本</td>
        {% for record in compare_records %}
        <td>{{ record.version }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>总用例数</td>
        {% for record in compare_records %}
        <td>{{ record.totals }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>Passed</td>
        {% for record in compare_records %}
        <td>{{ record.passed }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>Failed</td>
        {% for record in compare_records %}
        <td>{{ record.failed }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>Skipped</td>
        {% for record in compare_records %}
        <td>{{ record.skipped }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>Errors</td>
        {% for record in compare_records %}
        <td>{{ record.errors }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>通过率</td>
        {% for record in compare_records %}
        <td>{{ 100*record.passed/record.totals }}%</td>
        {% endfor %}
    </tr>
</table>

<h3>链路测试情况</h3>
<table>
    <tr>
        <td>版本</td>
        {% for record in compare_records %}
        <td>{{ record.version }}</td>
        {% endfor %}
    </tr>
    {% set all_prompt_types = ["全部"] + prompt_type_map.values()|list %}
    {% for prompt_type_name in all_prompt_types %}
    <tr>
        <td>{{ prompt_type_name }}通过率</td>
        {% for record in compare_records %}
        <td>{{ record.custom_properties["test_success_result"][prompt_type_name]["percentage"] }}</td>
        {% endfor %}
    </tr>
    {% endfor %}
</table>

<h3>时耗测试情况</h3>
<table>
    {% set all_prompt_types = ["全部"] + prompt_type_map.values()|list %}
    {% for prompt_type_name in all_prompt_types %}
    <tr><td><br/></td></tr></tr>
    {% for latency_type in ("最小", "最大", "中位", "平均") %}
    <tr>
        <td>{{ prompt_type_name }}{{ latency_type }}时耗</td>
        {% for record in compare_records %}
        {% if record.custom_properties["test_latency_result"][prompt_type_name] is defined %}
        <td>{{ record.custom_properties["test_latency_result"][prompt_type_name]["latency"][latency_type] }}</td>
        {% endif %}
        {% endfor %}
    </tr>
    {% endfor %}
    {% endfor %}
</table>


<h3>意图测试情况</h3>
<table>
    <tr>
        <td>版本</td>
        {% for record in compare_records %}
        <td>{{ record.version }}</td>
        {% endfor %}
    </tr>
    {% set all_prompt_plugins = ["全部","计算插件"] + prompt_plugin_map.values()|list %}
    {% for prompt_plugin_name in all_prompt_plugins %}
    <tr>
        <td>{{ prompt_plugin_name }}通过率</td>
        {% for record in compare_records %}
            {% if record.custom_properties["test_intention_result"][prompt_plugin_name] is defined %}
            <td>{{ record.custom_properties["test_intention_result"][prompt_plugin_name]["percentage"] }}({{ record.custom_properties["test_intention_result"][prompt_plugin_name]["passed"] }}/{{ record.custom_properties["test_intention_result"][prompt_plugin_name]["total"] }})</td>
            {% else %}
            <td>NA(0/0)</td>
            {% endif %}
        {% endfor %}
    </tr>
    {% endfor %}
</table>

<h3>藏头诗测试情况</h3>
<table>
    <tr>
        <td>版本</td>
        {% for record in compare_records %}
        <td>{{ record.version }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>总用例数</td>
        {% for record in compare_records %}
        <td>{{ record.custom_properties["test_poem_result"]["total"] }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>Passed</td>
        {% for record in compare_records %}
        <td>{{ record.custom_properties["test_poem_result"]["passed"] }}</td>
        {% endfor %}
    </tr>
    <tr>
        <td>通过率</td>
        {% for record in compare_records %}
        <td>{{ record.custom_properties["test_poem_result"]["percentage"] }}</td>
        {% endfor %}
    </tr>
</table>