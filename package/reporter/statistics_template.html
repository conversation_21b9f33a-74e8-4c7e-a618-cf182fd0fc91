<html>
<body>
<h2>test_success 链路测试</h2>
<p>通过率：{{ test_success_result["全部"]["percentage"] }}</p>
<br/>
{% for prompt_type_name in prompt_type_map.values() %}
<p>{{ prompt_type_name }}通过率：{{ test_success_result[prompt_type_name]["percentage"] }}</p>
{% endfor %}
<br/>
<h2>test_latency 时耗测试</h2>
<p>通过率：{{ test_latency_result["全部"]["percentage"] }}</p>
<br/>
{% if test_latency_result is defined and test_latency_result | length %}
{% for type_name, type_result in test_latency_result.items() %}
    {% if type_result is defined and test_latency_result | length %}
    {% for lantency_type, latency in type_result['latency'].items() %}
        <p>{{ type_name }}{{ lantency_type }}时耗：{{ latency }}s</p>
    {% endfor %}
    {% endif %}
    <br/>
{% endfor %}
{% endif %}
<!-- <p>时耗分布：</p>
<p><img src="data:image/jpeg;base64,{{ img_str }}" width="600"></p>
<br/> -->
<h2>test_intention 意图识别测试</h2>
<p>总通过率：{{ test_intention_result["全部"]["percentage"] }}</p>
<br/>
{% for prompt_plugin_name in prompt_plugin_map.values() %}
<p>{{ prompt_plugin_name }}通过率：{{ test_intention_result[prompt_plugin_name]["percentage"] }}</p>
{% endfor %}
<br/>
<h2>test_poem 藏头诗测试</h2>
<p>总通过率：{{ test_poem_result["percentage"] }}</p>
</body>
</html>