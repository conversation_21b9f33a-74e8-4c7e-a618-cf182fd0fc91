import os
import re
import pandas as pd
import base64
import jinja2
import numpy as np
from io import BytesIO
from package.config import compare_policy, version, version_to_compare, last_n
from package.common.prompt_map import prompt_type_map, prompt_plugin_map
from package.common.models import ExecutionHistory

data = pd.read_json(path_or_buf=os.path.join(os.getcwd(), 'log.jsonl'), lines=True)

# setup和teardown不计入
test_all = data[data.when == "call"]

# 链路测试
test_success_all = test_all[test_all.nodeid.str.contains('::test_success', na=False)]
test_success_result = {}
for prompt_type in ['全部', *prompt_type_map.keys()]:
    if prompt_type == '全部':
        prompt_type_name = prompt_type
        test_success_type_all = test_success_all
    else:
        prompt_type_name = prompt_type_map[prompt_type]
        test_success_type_all = test_success_all[test_success_all.nodeid.str.contains('type={}'.format(prompt_type), na=False)]
    count_success_type_all = len(test_success_type_all)
    count_success_type_passed = len(test_success_type_all[test_success_type_all.outcome == 'passed'])
    count_success_type_skipped = len(test_success_type_all[test_success_type_all.outcome == 'skipped'])
    count_susscess_unskipped = count_success_type_all - count_success_type_skipped
    test_success_result[prompt_type_name] = {
        'total': count_success_type_all,
        'passed': count_success_type_passed,
        'skipped': count_success_type_skipped,
        'percentage': '{}%'.format(100*count_success_type_passed/count_susscess_unskipped) if count_susscess_unskipped!=0 else "NA"
    }

# 意图识别
test_intention_all = test_all[test_all.nodeid.str.contains('::test_intention', na=False)]
test_intention_all = test_all[test_intention_all.user_properties.apply(lambda val:len(val)!=0 and len([item[1] for item in val if item[0]=='expected_plugin'])!=0)]
test_intention_result = {}
for prompt_plugin in ['全部', *prompt_plugin_map.keys()]:
    if prompt_plugin == '全部':
        prompt_plugin_name = prompt_plugin
        test_intention_plugin_all = test_intention_all
    else:
        prompt_plugin_name = prompt_plugin_map[prompt_plugin]
        test_intention_plugin_all = test_intention_all[test_intention_all.user_properties.apply(
            lambda val:len(val)!=0 and len([item[1] for item in val if item[0]=='expected_plugin'])!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]==prompt_plugin
            )]
    count_intention_plugin_all = len(test_intention_plugin_all)
    count_intention_plugin_passed = 0 if test_intention_plugin_all.empty else len(test_intention_plugin_all[test_intention_plugin_all.outcome == 'passed'])
    count_intention_plugin_skipped = 0 if test_intention_plugin_all.empty else len(test_intention_plugin_all[test_intention_plugin_all.outcome == 'skipped'])
    count_intention_plugin_unskipped = count_intention_plugin_all - count_intention_plugin_skipped
    test_intention_result[prompt_plugin_name] = {
        'total': count_intention_plugin_all,
        'passed': count_intention_plugin_passed,
        'skipped': count_intention_plugin_skipped,
        'percentage': '{}%'.format(100*count_intention_plugin_passed/count_intention_plugin_unskipped) if count_intention_plugin_unskipped!=0 else "NA"
    }
if not test_intention_all.empty:
    test_intention_failed = test_intention_all[test_intention_all.outcome == 'failed']
    if not test_intention_failed.empty:
        adt_id = test_intention_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        expected_plugin = test_intention_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]
            )
        intent_plugin_ids = test_intention_failed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='intent_plugin_ids'][0][0]
            )
        q_and_a = test_intention_failed.sections.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='Captured stdout call'][0]
            )
        questions = test_intention_failed.sections.apply(
            lambda val: re.split('问题：|\n收到的回答：', [item[1] for item in val if item[0]=='Captured stdout call'][0])[1]
            )
        answers = test_intention_failed.sections.apply(
            lambda val: re.split('问题：|\n收到的回答：|\n实际执行的插件：', [item[1] for item in val if item[0]=='Captured stdout call'][0])[2]
            )
        df = pd.DataFrame({
            'adt_id': adt_id,
            '预期插件': expected_plugin,
            '实际插件': intent_plugin_ids,
            '问题': questions,
            '答案': answers
        })
        df.to_csv('intention_failed.csv',encoding='utf-8-sig')
    test_intention_passed = test_intention_all[test_intention_all.outcome == 'passed']
    if not test_intention_passed.empty:
        adt_id = test_intention_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
            )
        expected_plugin = test_intention_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]
            )
        intent_plugin_ids = test_intention_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='intent_plugin_ids'][0][0]
            )
        questions = test_intention_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        answers = test_intention_passed.user_properties.apply(
            lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
            )
        df = pd.DataFrame({
            'adt_id': adt_id,
            '预期插件': expected_plugin,
            '实际插件': intent_plugin_ids,
            '问题': questions,
            '答案': answers
        })
        df.to_csv('intention_passed.csv',encoding='utf-8-sig')
        
# test_intention_passed = test_intention_all[test_intention_all.outcome == 'passed']
# adt_id = test_intention_passed.user_properties.apply(
#     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
#     )
# expected_plugin = test_intention_passed.user_properties.apply(
#     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='expected_plugin'][0]
#     )
# plugin_ids = test_intention_passed.user_properties.apply(
#     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0][0]
#     )
# questions = []
# file_path = 'credible_model.csv'
# df_all = pd.read_csv(file_path, header=0, keep_default_na=False)
# for index,prompt in df_all.iterrows():
#     if index in adt_id.values:
#         questions.append(prompt['问题'])
# df = pd.DataFrame({
#     'adt_id': adt_id,
#     '预期插件': expected_plugin,
#     '实际插件': plugin_ids,
#     '问题': questions
# })
# df.to_csv('intention_passed.csv',encoding='utf-8-sig')

# 藏头诗
test_poem_all = test_all[test_all.nodeid.str.contains('::test_poem', na=False)]
count_poem_all = len(test_poem_all)
count_poem_passed = len(test_poem_all[test_poem_all.outcome == 'passed'])
count_poem_skipped = len(test_poem_all[test_poem_all.outcome == 'skipped'])
count_poem_unskipped = count_poem_all - count_poem_skipped
test_poem_result = {
    'total': count_poem_all,
    'passed': count_poem_passed,
    'skipped': count_poem_skipped,
    'percentage': '{}%'.format(100*count_poem_passed/count_poem_unskipped) if count_poem_unskipped!=0 else "NA"
}

# 时耗
test_latency_all = test_all[data.nodeid.str.contains('::test_latency', na=False)]
count_latency_all = len(test_latency_all)
count_latency_passed = len(test_latency_all[test_latency_all.outcome == 'passed'])
count_latency_skipped = len(test_latency_all[test_latency_all.outcome == 'passed'])
count_latency_unskipped = count_latency_all - count_latency_skipped
test_latency_result = {
    '全部': {
        'total': count_latency_all,
        'passed': count_latency_passed,
        'skipped': count_latency_skipped,
        'percentage': '{}%'.format(100*count_latency_passed/count_latency_unskipped) if count_latency_unskipped!=0 else "NA",
        'latency': {}
    }
}

if not test_latency_all.empty:
    for prompt_type in ['全部', *prompt_type_map.keys()]:
        if prompt_type == '全部':
            prompt_type_name = prompt_type
            test_latency_types = test_latency_all
        else:
            prompt_type_name = prompt_type_map[prompt_type]
            test_latency_types = test_latency_all[test_latency_all.nodeid.str.contains('type={}'.format(prompt_type), na=False)]
        test_latency_types_time = test_latency_types.user_properties.apply(lambda val:[item[1] for item in val if item[0]=='answer_total_seconds'][0])
        test_latency_result[prompt_type_name] = {}
        test_latency_result[prompt_type_name]['latency'] = {
            "最小": np.min(test_latency_types_time) if len(test_latency_types_time)!=0 else 'NA',
            "最大": np.max(test_latency_types_time) if len(test_latency_types_time)!=0 else 'NA',
            "中位": np.median(test_latency_types_time) if len(test_latency_types_time)!=0 else 'NA',
            "平均": np.mean(test_latency_types_time) if len(test_latency_types_time)!=0 else 'NA'
        }
        # ax = test_latency_types_time.hist()
        # fig = ax.get_figure()
        # figfile = BytesIO()
        # fig.savefig(figfile)
        # figfile.seek(0)
        # figdata_png = base64.b64encode(figfile.getvalue())
        # img_str = str(figdata_png, "utf-8")


templateLoader = jinja2.FileSystemLoader(searchpath="./")
templateEnv = jinja2.Environment(loader=templateLoader)
# TEMPLATE_FILE = os.path.join(os.path.dirname(__file__), "statistics_template.html")
TEMPLATE_FILE = os.path.join('package', "reporter", "statistics_template.html")
template = templateEnv.get_template(TEMPLATE_FILE)
outputText = template.render(
    prompt_type_map=prompt_type_map,
    prompt_plugin_map=prompt_plugin_map,
    test_success_result=test_success_result,
    test_intention_result=test_intention_result,
    test_latency_result=test_latency_result,
    test_poem_result=test_poem_result
    )

os.makedirs(os.path.join(os.getcwd(), 'statistics'), exist_ok=True)
with open(os.path.join(os.getcwd(), 'statistics', "statistics.html"),"w") as file:
    file.write(outputText)


custom_properties = {
    'test_success_result': test_success_result,
    'test_intention_result': test_intention_result,
    'test_latency_result': test_latency_result,
    'test_poem_result': test_poem_result,
    'version': version
}

totals = len(test_all)
passed = len(test_all[test_all.outcome == 'passed'])
failed = len(test_all[test_all.outcome == 'failed'])
skipped = len(test_all[test_all.outcome == 'skipped'])
errors = len(test_all[test_all.outcome == 'errors'])
expected_failures = len(test_all[test_all.outcome == 'expected_failures'])
unexpected_passes = len(test_all[test_all.outcome == 'unexpected_passes'])
execution_url = os.environ.get('BK_CI_BUILD_URL')
triggered_by = os.environ.get('BK_CI_START_USER_NAME', 'local')

if compare_policy == 'last':
    compare_records = [ExecutionHistory.select().order_by(ExecutionHistory.id.desc()).get()]
elif compare_policy == 'version':
    compare_records = [
        ExecutionHistory.select().where(ExecutionHistory.version == version_to_compare).order_by(ExecutionHistory.id.desc()).get()
    ]
elif compare_policy == 'last_n':
    compare_records = list(ExecutionHistory.select().order_by(ExecutionHistory.id.desc()).limit(last_n))[::-1]
else:
    compare_records = list()

new_record = ExecutionHistory.create(
    totals=totals,
    passed=passed,
    failed=failed,
    skipped=skipped,
    errors=errors,
    expected_failures=expected_failures,
    unexpected_passes=unexpected_passes,
    custom_properties=custom_properties,
    version=version
)

compare_records.append(new_record)

templateLoader = jinja2.FileSystemLoader(searchpath="./")
templateEnv = jinja2.Environment(loader=templateLoader)
# TEMPLATE_FILE = os.path.join(os.path.dirname(__file__), "statistics_template.html")
TEMPLATE_FILE = os.path.join('package', "reporter", "compare_template.html")
template = templateEnv.get_template(TEMPLATE_FILE)
outputText = template.render(
    compare_records=compare_records,
    prompt_type_map=prompt_type_map,
    prompt_plugin_map=prompt_plugin_map
    )
os.makedirs(os.path.join(os.getcwd(), 'statistics'), exist_ok=True)
with open(os.path.join(os.getcwd(), 'statistics', "compare.html"),"w") as file:
    file.write(outputText)
