import os
import pandas as pd
import json

data = pd.read_json(path_or_buf=os.path.join(os.getcwd(), 'log.jsonl'), lines=True)

# setup和teardown不计入
test_all = data[data.when == "call"]

# 多轮改写文生图
test_multi_round_text2image_all = test_all[test_all.nodeid.str.contains('::test_multi_round_text2image', na=False)]
count_multi_round_text2image_all = len(test_multi_round_text2image_all)
count_multi_round_text2image_passed = len(test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'passed'])
count_multi_round_text2image_skipped = len(test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'skipped'])
count_multi_round_text2image_unskipped = count_multi_round_text2image_all - count_multi_round_text2image_skipped
test_multi_round_text2image_result = {
    'total': count_multi_round_text2image_all,
    'passed': count_multi_round_text2image_passed,
    'skipped': count_multi_round_text2image_skipped,
    'percentage': '{}%'.format(100*count_multi_round_text2image_passed/count_multi_round_text2image_unskipped) if count_multi_round_text2image_unskipped!=0 else "NA"
}
if not test_multi_round_text2image_all.empty:
    test_multi_round_text2image_passed = test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'passed']
    test_multi_round_text2image_failed = test_multi_round_text2image_all[test_multi_round_text2image_all.outcome == 'failed']
    index_id = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [int(item[1]) for item in val if item[0]=='index_id'][0]
        )
    adt_id = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
        )
    questions = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    answers = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='answers'][0]
        )
    # prediction = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='prediction'][0]
    #     )
    # final_answer = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='final_answer'][0]
    #     )
    # context = test_multi_round_text2image_all.user_properties.apply(
    #     lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='context'][0]
    #     )
    plugin_ids = test_multi_round_text2image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='plugin_ids'][0]
        )
    results = test_multi_round_text2image_all['outcome']

    all_contexts = []
    all_final_answers = []
    for index,row in test_multi_round_text2image_all.iterrows():
        qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
        ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
        contexts = []
        for n in range(len(qes)-1):
            context = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
            contexts.append(context)
        contexts.append('人类：{}\n'.format(qes[-1]))
        all_contexts.append(''.join(contexts))
        all_final_answers.append(ans[-1])
        
    df = pd.DataFrame({
        'id': index_id,
        # 'adt_id': adt_id,
        # 'prompt': questions,
        # 'predict': prediction,
        'answer': answers,
        # 'result': results,
        # 'plugin_ids': plugin_ids,
        # 'context': all_contexts,
        # 'final_answer': all_final_answers,
        # 'context' : context,
        # 'final_answer': final_answer
    }).set_index('id')
    df.to_csv('multi_rounds_text2image.csv',encoding='utf-8-sig',index=False)
    df_o = pd.read_csv('collection_multi_round_text2image.csv')
    print(df_o.join(df))
    df_o.join(df).to_csv('multi_rounds_text2image_merged.csv',encoding='utf-8-sig',index=False)


# 多轮改写文生图
test_multiply_modify_for_text_to_image_all = test_all[test_all.nodeid.str.contains('::test_multiply_modify_for_text_to_image', na=False)]
test_multiply_modify_for_text_to_image_all = test_multiply_modify_for_text_to_image_all[test_multiply_modify_for_text_to_image_all.outcome == 'passed']
count_multiply_modify_for_text_to_image_all = len(test_multiply_modify_for_text_to_image_all)

if not test_multiply_modify_for_text_to_image_all.empty:
    test_multiply_modify_for_text_to_image_passed = test_multiply_modify_for_text_to_image_all[test_multiply_modify_for_text_to_image_all.outcome == 'passed']
    test_multiply_modify_for_text_to_image_failed = test_multiply_modify_for_text_to_image_all[test_multiply_modify_for_text_to_image_all.outcome == 'failed']
    adt_id = test_multiply_modify_for_text_to_image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='adt_id'][0]
        )
    questions = test_multiply_modify_for_text_to_image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1] for item in val if item[0]=='questions'][0]
        )
    # for index,row in test_multiply_modify_for_text_to_image_all.iterrows():
    #     ref_answers = [item[1] for item in row['user_properties'] if item[0]=='ref_answers'][0]
    ref_answers = test_multiply_modify_for_text_to_image_all.user_properties.apply(
        lambda val:len(val)!=0 and [item[1][:-1]+item[1][-1] for item in val if item[0]=='ref_answers'][0]
        )
    # ref_answers = [json.dumps(eval(ref_answer), ensure_ascii=False) for _,ref_answer in ref_answers.iterrows()]
    results = test_multiply_modify_for_text_to_image_all['outcome']
    # all_contexts = []
    # for index,row in test_multi_round_text2image_all.iterrows():
    #     qes = [item[1] for item in row['user_properties'] if item[0]=='questions'][0]
    #     ans = [item[1] for item in row['user_properties'] if item[0]=='answers'][0]
    #     contexts = []
    #     for n in range(len(qes)):
    #         context = '人类：{}\n模型：{}\n'.format(qes[n],ans[n])
    #         contexts.append(context)
    #     all_contexts.append(''.join(contexts))
    df = pd.DataFrame({
        'id': adt_id,
        # 'prompt': questions,
        'ref_answer': ref_answers
    })
    df.to_csv('multi_round_text2image_all.csv',encoding='utf-8-sig',index=False)
    df_o = pd.read_csv('collection_multi_round_text2image.csv')
    print(df_o.join(df))
    df_o.join(df).to_csv('multi_rounds_text2image_merged.csv',encoding='utf-8-sig',index=False)
