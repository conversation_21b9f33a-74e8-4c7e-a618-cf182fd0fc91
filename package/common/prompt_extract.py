import json
from package.common.prompt_map import prompt_plugin_map_reverse

def prompt_list_to_map(prompt_data):
    adt_id = prompt_data[4]
    prompt_type = prompt_data[3]
    # prompt_data = json.loads(request.param[1])
    prompts = json.loads(prompt_data[1])
    print("prompts:{},length:{}".format(prompts,len(prompts)))
    if isinstance(prompts,str):
        prompts = [prompts]
    # prompt = prompts[0]
    ref_answers = json.loads(prompt_data[5]) if len(prompt_data)>5 and prompt_data[5] else ''
    ref_answers = [ref_answers] if ref_answers and isinstance(ref_answers,str) else ref_answers
    ref_answer_regexs = json.loads(prompt_data[6]) if len(prompt_data)>6 and prompt_data[6] else ''
    ref_answer_regexs = [ref_answer_regexs] if ref_answer_regexs and isinstance(ref_answer_regexs,str) else ref_answer_regexs
    # print('prompts {}'.format(prompts))
    # print('prompt {}'.format(prompt))
    # print('prompt {}'.format(prompts[1]))
    # print('type{}'.format(type(prompts)))
    # exit(0)
    expected_plugin = prompt_plugin_map_reverse.get(prompt_data[2], 'Adaptive')
    # prompts = json.loads(request.param[1])
    return {
        'adt_id': adt_id,
        'prompts': prompts,
        'prompt_type': prompt_type,
        'ref_answers': ref_answers,
        'ref_answer_regexs': ref_answer_regexs,
        'expected_plugin': expected_plugin
    }
