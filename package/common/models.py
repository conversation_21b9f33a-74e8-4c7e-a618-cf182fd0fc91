from peewee import *
from ..config import db_user, db_pwd, db_host, db_name

from playhouse.mysql_ext import MySQLConnectorDatabase, JSONField
db = MySQLConnectorDatabase(db_name, user=db_user, password=db_pwd, host=db_host)

class BaseModel(Model):
    class Meta:
        database = db

class ExecutionHistory(BaseModel):
    id = PrimaryKeyField()
    totals = IntegerField()
    passed = IntegerField()
    failed = IntegerField()
    skipped = IntegerField()
    errors = IntegerField()
    expected_failures = IntegerField()
    unexpected_passes = IntegerField()
    custom_properties = JSONField()
    created_at = DateTimeField()
    execution_url = CharField(255)
    triggered_by = CharField(50)
    version = CharField(50)
    class Meta:
        table_name = 'execution_history'

db.connect()