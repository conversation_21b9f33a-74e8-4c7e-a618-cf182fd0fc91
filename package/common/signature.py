import hashlib, hmac, json, os, sys, time
from datetime import datetime
import time
# from ..config import secret_id, secret_key

def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

def get_signature(secret_key):
    timestamp = int(time.time())
    date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")
    service = 'amai'
    secret_date = sign(("TC3" + secret_key).encode("utf-8"), date)
    secret_service = sign(secret_date, service)
    secret_signing = sign(secret_service, "tc3_request")
    return secret_signing

def get_authorization(secret_id, secret_signing, http_request_method, canonical_querystring, timestamp, nonce, ct, payload, host):
    timestamp = int(time.time())
    date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")
    algorithm = "TC3-HMAC-SHA256"
    service = 'amai'
    canonical_headers = "content-type:{}\nhost:{}\n".format(ct, host)
    signed_headers = "content-type;host"
    hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
    canonical_uri = "/"
    canonical_request = (http_request_method + "\n" +
                         canonical_uri + "\n" +
                         canonical_querystring + "\n" +
                         canonical_headers + "\n" +
                         signed_headers + "\n" +
                         hashed_request_payload)
    credential_scope = date + "/" + service + "/" + "tc3_request"
    hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
    string_to_sign = (algorithm + "\n" +
                    str(timestamp) + "\n" +
                    str(nonce) + "\n" +
                    credential_scope + "\n" +
                    hashed_canonical_request)
    signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()
    authorization = (algorithm + " " +
                 "Credential=" + secret_id + "/" + credential_scope + ", " +
                 "SignedHeaders=" + signed_headers + ", " +
                 "Signature=" + signature)
    return authorization



# host = "hunyuan.tencent.com"
# endpoint = "https://" + host + '/api/chat/c54b34ee-00ba-4580-ba4a-db6cb0a1d422'