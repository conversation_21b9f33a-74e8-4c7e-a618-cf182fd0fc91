import os
import requests
import time
import json
import urllib.parse

try:
    from package.config import zhiyan_token
except ImportError:
    zhiyan_token = os.environ.get('zhiyan_token', None)


def search_logs(dataflowId=None,dataflowList=None,traceId=None,query=None,orderBy=None,page=None,limit=None,fields=None,orderFields=None,storageType='elasticsearch'):
    """
    查询日志
    dataflowId是接入点id，详见 http://zhiyan.oa.com/log/ → 接入管理 → 接入名称，将traceId和query组合成查询条件
    接口文档：https://iwiki.woa.com/p/4007943077#%E9%80%9A%E7%94%A8%E6%97%A5%E5%BF%97%E6%90%9C%E7%B4%A2%E6%8E%A5%E5%8F%A3
    dataflowName: 
    测试环境: hunyuan_openapi
    生产环境: hunyuan_openapi_prod
    预发布环境: openapi_pre
    文生图独立产品算法策略服务 生产/预发环境: hunyuan_text2image2_strategy_prod
    文生图独立产品算法策略服务 测试环境: hunyuan_text2image2_strategy_test
    """
    url = 'http://openapi.zhiyan.oa.com/log/v2/search/query'
    STAFFNAME = 'jacejiang'
    PROJECTNAME = 'hunyuanbot'
    headers = {
        'Content-type': 'application/json',
        'token': zhiyan_token,
        'staffname': STAFFNAME,
        'projectname': PROJECTNAME
        # 'x-api-version': 'v2',
    }
    if traceId is not None:
        if query is None:
            query = f'traceID:"{traceId}"'
        else:
            query = f'({query}) AND traceID:"{traceId}"'
    endTime = time.time() * 1000 # 单位ms
    startTime = endTime - 3600000 # 查询1h内的日志内容，单位ms
    payload_variables = ['startTime','endTime','dataflowList','dataflowId','storageType','includeTotal','filter','query','orderBy','page','limit','fields','orderFields']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    print(payload)
    resp = requests.post(url=url, headers=headers, json=payload, verify=False, timeout=60)
    dataflowIds = ""
    if dataflowId is not None:
        dataflowIds = dataflowId
    elif dataflowList is not None:
        for dataflow in dataflowList:
            dataflowIds += f"&dataflowIds={dataflow['dataflowId']}"
    web_url = f'https://zhiyan.woa.com/log/9860/analyze/#/analyze/query?from=1&size=30&showMode=source&filters=%5B%5D&order=desc&pid=9860&{dataflowIds}&query={urllib.parse.quote(query)}&queryType=0&searchType=lucene&startTimeValue={startTime}&endTimeValue={endTime}&startTimeType=absolute&endTimeType=absolute'
    print(web_url)
    json = resp.json()
    print(f"log total: {json.get('data').get('total')}")
    return {
        'json': json,
        'web_url': web_url
    }
