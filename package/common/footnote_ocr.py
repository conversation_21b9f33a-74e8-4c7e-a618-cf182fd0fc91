import difflib
import io
import easyocr
import requests
from PIL import Image,ImageEnhance

def footnote_ocr(img, footnote, policy=0, block_size=7, c=12):
    reader = easyocr.Reader(['ch_sim'], gpu=False)
    box = (img.width-40*len(footnote), img.height-40, img.width, img.height)
    image = img.crop(box)
    img_byte_arr = io.BytesIO()

    image = ImageEnhance.Contrast(image).enhance(2.0)
    image = ImageEnhance.Brightness(image).enhance(0.5)
    image = image.convert('L')

    if policy != 0:
        width, height = image.size
        binary_img = Image.new('L', (width, height))
        
        for i in range(width):
            for j in range(height):
                # 计算当前像素邻域的平均值
                neighbors_sum = 0
                count = 0
                for x in range(max(0, i-block_size//2), min(width, i+block_size//2+1)):
                    for y in range(max(0, j-block_size//2), min(height, j+block_size//2+1)):
                        neighbors_sum += image.getpixel((x, y))
                        count += 1
                
                mean_val = neighbors_sum // count
                
            # 根据背景颜色设置阈值方向
                if policy == 1:
                    threshold = mean_val + c
                else:
                    threshold = mean_val - c
                
                # 应用自适应阈值
                if image.getpixel((i, j)) > threshold:
                    binary_img.putpixel((i, j), 255)
                else:
                    binary_img.putpixel((i, j), 0)
        
        final_image = binary_img
    else:
        final_image = image
    # final_image.show()
    final_image.save(img_byte_arr, format='PNG')
    img_byte_arr = img_byte_arr.getvalue()
    result = reader.readtext(img_byte_arr, detail = 0)
    similarity = difflib.SequenceMatcher(lambda x:x==' ', ''.join(result), footnote).quick_ratio()
    print(f'[ocr] footnote:{result}, similarity{similarity}')
    return similarity

# with (Image.open(io.BytesIO(requests.get('https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/text2image/result/20240813/20240813111705115/d5666f26c0cc884ce34cd804fd8616b4.png?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKID0qSq0xJRL7h3A4nIYJFrFOJ1VlnbIm26%26q-sign-time%3D1723519025%3B1723605425%26q-key-time%3D1723519025%3B1723605425%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D7b6f7ae58c09faff2602a77541494bf82d0551a8').content)) as img):
#     footnote = '我是一个水印'
#     size = '1082×1187'
#     similarity = adaptive_threshold(img, footnote)
#     assert similarity > 0.4