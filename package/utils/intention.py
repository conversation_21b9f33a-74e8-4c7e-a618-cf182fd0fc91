import os
import pandas as pd
from package.config import domain, cookie, userids, intention_start_row, intention_read_col, intention_write_col
from package.common.prompt_map import prompt_plugin_map
from package.api.generate_id import generate_id
from package.api.chat import chat
from package.api.conv import conv

prompt_intention_map = prompt_intention_map = {**prompt_plugin_map, "Adaptive": "主模型"}
file_path = 'prompt.csv'
df = pd.read_csv(file_path, header=None)
prompts = df.iloc[intention_start_row-1:, intention_read_col-1]
userid = userids.split(',')[0]
id = generate_id(domain, cookie, userid)
plugins = []
for prompt in prompts:
    answer = chat(domain=domain, cookie=cookie, userid=userid, id=id, prompt=prompt, model='gpt_175B_0404', plugin='Adaptive')
    conversation = conv(domain=domain, id=id, cookie=cookie, userid=userid)
    conversation_content = conversation['msg_str']
    plugin_ids = conversation_content['convs'][-1]['pluginContext']['intentPluginIds']
    plugins.append('、'.join([prompt_intention_map.get(plugin_id, plugin_id) for plugin_id in plugin_ids]))

if intention_write_col >= df.shape[1]:
    for _ in range(intention_write_col - df.shape[1] + 1):
        df[df.shape[1]] = None
df.loc[intention_start_row-1:, intention_write_col] = plugins
df.to_csv(file_path, index=False, header=None)