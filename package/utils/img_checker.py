# -*- coding: UTF-8 -*-
# !/usr/bin/env python3

"""
    这个文件用于生图接口返回的image_url的size比对
"""

import logging
import requests
from PIL import Image
from io import BytesIO
from typing import Tuple

class ImageChecker:
    """
    ImgSizeChecker Class
    """

    def __init__(self, **kwargs):
        """
        init ImageChecker
        """
        pass
        # self.img_url = kwargs['img_url']
        # self.size = kwargs['size']

    def _get_size(self, size: str) -> Tuple[int, int]:
        """
        获取图像尺寸，如果尺寸无效则抛出异常。
        """
        if not isinstance(size, str):
            raise ValueError(f"size must be a string with {size}")

        if size is None or len(size.split('x')) != 2:
            raise ValueError(f"size format should be 'width x height' with {size}")

        try:
            width = int(size.split('x')[0])
            height = int(size.split('x')[1])
        except (ValueError, IndexError):
            raise ValueError(f"Invalid size format. Expected 'width x height'. With {size}")
        return width, height
    def _get_img_size(self, img_url: str) -> Tuple[int, int]:
        """
        根据图像url获取实际图片尺寸
        """
        if not isinstance(img_url, str):
            raise ValueError(f"img_url must be a string with {img_url}")
        if img_url is None or len(img_url) == 0:
            raise ValueError(f"img_url must not be None with {img_url}")

        try:
            # 发送HTTP GET请求获取图片数据
            response = requests.get(img_url, timeout=10)
            response.raise_for_status()  # 检查请求是否成功

            # 使用BytesIO将二进制数据转换为文件对象
            image_data = BytesIO(response.content)

            # 使用Pillow打开图片，获取网络图片尺寸
            with Image.open(image_data) as img:
                width, height = img.size
                return (width, height)

        except requests.exceptions.RequestException as e:
            print(f"网络请求错误: {e}")
        except IOError as e:
            print(f"无法处理图片: {e}")


    def img_size_checker(self, size: str, image_url: str) -> bool:
        """
        比对图像尺寸是否一致
        """
        try:
            except_size = self._get_size(size)
            actual_size = self._get_img_size(image_url)
            if except_size == actual_size:
                return True
            else:
                print(f"expect size is {except_size} not match actual size {actual_size}, with {size} and img_url:{image_url}")
                return False
        except Exception as e:
            print(f"Exception with {e}")
            return False

if __name__ == '__main__':
    img_checker = ImageChecker()
    print(img_checker.img_size_checker("1024x1024", "https://hunyuan-base-test-1258344703.cos.ap-guangzhou.myqcloud.com/public/249.jpeg"))