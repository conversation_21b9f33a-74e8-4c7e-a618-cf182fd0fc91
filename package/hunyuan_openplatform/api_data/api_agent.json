{"agent_config": {"description": "获取智能体配置信息", "url": "/api/agent/config", "method": "GET"}, "agent_create": {"description": "创建智能体", "url": "/api/agent/personal/agent/create", "method": "POST"}, "agent_detail": {"description": "智能体详情", "url": "/api/agent/personal/agent/detail", "method": "GET"}, "agent_delete": {"description": "删除智能体", "url": "/api/agent/personal/agent/delete", "method": "POST"}, "generate_character": {"description": "AI辅助创建智能体", "url": "/api/agent/personal/agent/generate_character", "method": "POST"}, "agent_update": {"description": "智能体更新", "url": "/api/agent/personal/agent/update", "method": "POST"}, "agent_publish": {"description": "智能体发布", "url": "/api/agent/personal/agent/publish", "method": "POST"}, "getwxaqrcode": {"description": "获取微信小程序二维码", "url": "/api/weixin/getwxaqrcode", "method": "POST"}, "person_agent_list": {"description": "获取智能体列表", "url": "/api/agent/personal/agent/list", "method": "GET"}, "agent_generate_logo": {"description": "生成智能体Logo", "url": "/api/agent/personal/agent/generate_logo", "method": "POST"}, "agent_demos": {"description": "获取智能体demo列表", "url": "/api/agent/personal/agent/demos", "method": "GET"}, "agent_copy": {"description": "智能体复制", "url": "/api/agent/personal/agent/copy", "method": "GET"}, "agent_openapi_info": {"description": "获取智能体的OpenAPI信息", "url": "/api/agent/personal/agent/openapi_info", "method": "GET"}, "agent_expand_background_image": {"description": "扩展智能体背景图片", "url": "/api/agent/personal/agent/expand_background_image", "method": "POST"}, "get_yuanbaoId": {"description": "获取元宝智能体ID", "url": "/api/agent/yuanbaoId", "method": "POST"}}