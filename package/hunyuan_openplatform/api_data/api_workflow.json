{"workflow_create": {"description": "创建工作流", "url": "/api/agent/workflow/create", "method": "POST"}, "workflow_delete": {"description": "删除工作流", "url": "/api/agent/workflow/delete", "method": "POST"}, "workflow_detail": {"description": "工作流详情", "url": "/api/agent/workflow/detail", "method": "POST"}, "workflow_list": {"description": "工作流列表", "url": "/api/agent/workflow/list", "method": "POST"}, "workflow_save": {"description": "保存工作流", "url": "/api/agent/workflow/save", "method": "POST"}, "workflow_tags": {"description": "获取工作流标签", "url": "/api/agent/workflow/tags", "method": "GET"}, "workflow_publish": {"description": "发布工作流", "url": "/api/agent/workflow/publish", "method": "POST"}, "workflow_check": {"description": "检查工作流", "url": "/api/agent/workflow/check", "method": "POST"}, "workflow_testrun": {"description": "测试运行工作流", "url": "/api/agent/workflow/testrun", "method": "POST"}, "workflow_process": {"description": "工作流测试监听", "url": "/api/agent/workflow/process", "method": "POST"}}