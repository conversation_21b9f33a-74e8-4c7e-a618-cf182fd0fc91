{"plugin_create": {"description": "创建插件", "url": "/api/agent/personal/plugin/create", "method": "POST"}, "plugin_detail": {"description": "插件详情", "url": "/api/agent/personal/plugin/detail", "method": "GET"}, "plugin_update": {"description": "修改插件", "url": "/api/agent/personal/plugin/update", "method": "POST"}, "plugin_parse_api": {"description": "插件yaml解析", "url": "/api/agent/personal/plugin/parse_api", "method": "POST"}, "plugin_api_test": {"description": "插件API校验", "url": "/api/agent/personal/plugin/api_test", "method": "POST"}, "plugin_categories": {"description": "插件分类list", "url": "/api/agent/personal/plugin/categories", "method": "GET"}, "plugin_publish": {"description": "插件发布", "url": "/api/agent/personal/plugin/publish", "method": "POST"}, "plugin_list": {"description": "我的创建-插件列表", "url": "/api/agent/personal/plugin/list", "method": "GET"}, "plugin_store_list": {"description": "商店-插件列表", "url": "/api/agent/store/plugin/list", "method": "GET"}, "plugin_store_detail": {"description": "商店-插件详情", "url": "/api/agent/store/plugin/detail", "method": "GET"}, "plugin_agent_list": {"description": "插件关联的智能体列表", "url": "/api/agent/store/plugin/agent_list", "method": "GET"}}