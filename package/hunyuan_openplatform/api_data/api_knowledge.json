{"knowledge_id": {"description": "生成知识库ID", "url": "/api/agent/knowledge/id", "method": "POST"}, "knowledge_doc_update": {"description": "知识库更新文档", "url": "/api/agent/knowledge/doc/update", "method": "POST"}, "knowledge_detail": {"description": "知识库详情", "url": "/api/agent/knowledge/detail", "method": "GET"}, "knowledge_list": {"description": "知识库列表", "url": "/api/agent/personal/knowledge/list", "method": "GET"}, "agent_related_agents": {"description": "获取集成该知识库的智能体ID列表", "url": "/api/agent/knowledge/related_agents", "method": "GET"}, "knowledge_delete": {"description": "删除指定知识库", "url": "/api/agent/knowledge/delete", "method": "POST"}, "knowledge_update": {"description": "知识库基础信息更新", "url": "/api/agent/knowledge/update", "method": "POST", "source": "yuanqi"}}