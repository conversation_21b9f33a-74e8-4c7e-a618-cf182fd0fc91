{"api_chat": {"description": "智能体聊天", "url": "/api/chat", "method": "POST", "source": "yuanbao"}, "get_conversations": {"description": "获取聊天记录", "url": "/api/user/agent/v1/conversation", "method": "POST", "source": "yuanbao"}, "stop_conversations": {"description": "停止聊天", "url": "/api/stop/conversation", "method": "POST", "source": "yuanbao"}, "conversation_clear": {"description": "清除对话", "url": "/api/user/agent/conversation/v1/clear", "method": "POST", "source": "yuanbao"}}