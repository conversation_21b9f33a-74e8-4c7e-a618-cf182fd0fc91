{"knowledge_file": {"case_title": "创建文件类型知识库", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "生成知识库ID", "callfunname": "knowledge_id", "request": {"knowledgeType": "doc", "name": "马拉松训练指南", "description": "马拉松训练指南", "logoUrl": ""}, "assertions": {"status_code": 200, "response_body": {"knowledgeId": "assert_str", "error": {"code": "0", "message": "success"}}}}, {"stepname": "知识库文件上传", "callfunname": "getUploadInfo", "local_func": true, "request": {"filename": "马拉松终极训练指南（原书第4版）.pdf"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "知识库更新-添加文件", "callfunname": "knowledge_doc_update", "request": {"knowledgeId": "trans_0_response(knowledgeId)", "deleteFileList": [], "addFileList": [{"fileName": "trans_1_request(filename)", "fileUrl": "trans_1_response($.resourceInfos[*].resourceUrl)"}]}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "success"}}}}, {"stepname": "知识库详情", "callfunname": "knowledge_detail", "request": {"knowledgeId": "trans_0_response(knowledgeId)"}, "assertions": {"status_code": 200, "response_body": {"knowledgeInfo": {"knowledgeId": "trans_0_response(knowledgeId)", "knowledgeType": "doc", "knowledgeVersion": "4", "status": 2}, "error": {"code": "0", "message": "success"}}}}, {"stepname": "更新知识库基础信息", "callfunname": "knowledge_update", "request": {"knowledgeId": "trans_0_response(knowledgeId)", "name": "马拉松训练指南-更新", "description": "马拉松训练指南-更新", "logoUrl": ""}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "success"}}}}, {"stepname": "知识库详情", "callfunname": "knowledge_detail", "request": {"knowledgeId": "trans_0_response(knowledgeId)"}, "assertions": {"status_code": 200, "response_body": {"knowledgeInfo": {"knowledgeId": "trans_0_response(knowledgeId)", "knowledgeType": "doc", "knowledgeVersion": "4", "status": 2}, "error": {"code": "0", "message": "success"}}}}]}, "knowledge_QA": {"case_title": "创建QA对类型知识库", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "知识库ID", "callfunname": "knowledge_id", "request": {"knowledgeType": "qa", "name": "家属投保常见问题", "description": "- 针对家属投保的相关问题的解答", "logoUrl": ""}, "assertions": {"status_code": 200, "response_body": {"knowledgeId": "assert_str", "error": {"code": "0", "message": "success"}}}}, {"stepname": "知识库文件上传", "callfunname": "getUploadInfo", "local_func": true, "request": {"filename": "QA对知识库.csv"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "知识库更新-添加文件", "callfunname": "knowledge_doc_update", "request": {"knowledgeId": "trans_0_response(knowledgeId)", "deleteFileList": [], "addFileList": [{"fileName": "trans_1_request(filename)", "fileUrl": "trans_1_response($.resourceInfos[*].resourceUrl)"}]}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "success"}}}}, {"stepname": "知识库详情", "callfunname": "knowledge_detail", "sleep": 30, "request": {"knowledgeId": "trans_0_response(knowledgeId)"}, "assertions": {"status_code": 200, "response_body": {"knowledgeInfo": {"knowledgeId": "trans_0_response(knowledgeId)", "knowledgeType": "qa", "knowledgeVersion": "1", "status": 3}, "error": {"code": "0", "message": "success"}}}}, {"stepname": "获取知识库列表", "callfunname": "knowledge_list", "request": {"limit": 20}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "success"}, "knowledgeList": [{"knowledgeId": "trans_0_response(knowledgeId)", "env": "prod", "logoUrl": "", "name": "家属投保常见问题", "description": "- 针对家属投保的相关问题的解答", "knowledgeType": "qa", "knowledgeVersion": "1", "knowledgeVersionBak": "", "status": 3, "embNum": 7, "usage": 0}]}}}, {"stepname": "智能体创建 - 添加问答对知识库", "callfunname": "agent_create", "request": {"name": "保险百科", "description": "家属投保小助手", "rule": "- 你是一个保险专员，可以为为用户提供投保相关的知识", "logo": "$logoUrl", "avatar": [], "notice": "", "samplePrompts": [""], "toolIds": [], "knowledgeIds": ["trans_0_response(knowledgeId)"], "workflowIds": [], "suggestionEnable": false, "model": {"manufacturer": "Hunyuan", "manufacturerUI": "混元", "logo": "$logoModel", "modelName": "hun<PERSON>", "modelNameUI": "腾讯混元大模型-32k", "contextRounds": 16, "maxTokens": 4096, "temperature": 0.5}}, "assertions": {"status_code": 200, "response_body": {"agentId": "assert_str"}}}, {"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "trans_5_response(agentId)", "index": 0, "limit": 10, "source": "yuanqi", "agentVersion": "preview"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str", "stopGenerating": false, "sensitive": false, "title": "", "modelId": "", "modelVersion": "", "pluginId": "", "loraId": "", "inspirationTemplate": "", "agentId": "", "agentName": "", "extendSpeech": false, "chatType": 0, "firstRepliedAt": 0, "lastRepliedAt": 0, "deletedAt": 0, "noteId": 0, "externalAgentId": "", "hasMore": false, "dataTimeTag": "", "topTime": 0, "lastRepliedDatetime": "", "forAppAgentAssets": 0, "chatScene": 0}}}, {"stepname": "智能体调试聊天", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_6_response(id)", "model": "gpt_175B_0404", "prompt": "家属与员工不在同一个城市可否参加自选计划？", "plugin": "Adaptive", "displayPrompt": "家属与员工不在同一个城市可否参加自选计划？", "displayPromptType": 1, "multimedia": [], "agentId": "trans_5_response(agentId)", "version": "v2", "source": "yuanqi", "agentVersion": "preview"}, "assertions": {"status_code": 200, "response_body": {"content": "家属私立医院就诊指南"}}}, {"stepname": "智能体删除", "callfunname": "agent_delete", "headers": {}, "request": {"agentId": "trans_5_response(agentId)"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "获取已添加知识库的智能体ID", "callfunname": "agent_related_agents", "request": {"knowledgeId": "trans_0_response(knowledgeId)"}, "assertions": {"status_code": 200, "response_body": {"agentIdList": [], "workflowIdList": [], "error": {"code": "0", "message": "success"}}}}, {"stepname": "删除知识库", "callfunname": "knowledge_delete", "request": {"knowledgeId": "trans_0_response(knowledgeId)"}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "success"}}}}]}}