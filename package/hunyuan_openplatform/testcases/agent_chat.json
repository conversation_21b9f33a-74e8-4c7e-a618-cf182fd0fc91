{"agent_chat_01": {"author": "<PERSON><PERSON><PERSON>", "case_title": "智能体聊天-不调用工具", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体调试聊天", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "你好", "plugin": "Adaptive", "displayPrompt": "你好", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId", "version": "v2", "source": "yuanqi", "supportHint": 2}, "assertions": {"status_code": 200, "response_body": {"steps": ["data: {\"type\":\"text\"}", "event: speech_type", "data: status", "event: speech_type", "data: text", "data: {\"type\":\"usage\",\"prompt_tokens\": \"assert_int\",\"completion_tokens\":\"assert_int\",\"total_tokens\":\"assert_int\",\"total_cost\":\"assert_number\"}", "data: \\[plugin: \\]", "data: {\"type\":\"meta\",\"messageId\":\"assert_str\",\"index\":\"assert_int\",\"replyId\":\"assert_str\",\"replyIndex\":\"assert_int\",\"traceId\":\"assert_str\",\"guideId\":0,\"unSupportRepeat\":false}", "data: \\[TRACEID:.*\\]", "data: \\[DONE\\]"]}}}, {"stepname": "获取元宝智能体ID", "callfunname": "get_yuanbaoId", "request": {"agentId": "$agentId"}, "assertions": {"status_code": 200, "response_body": {"yuanbaoAgentId": "assert_str"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "trans_2_response(yuanbaoAgentId)"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "agent_chat_02": {"author": "<PERSON><PERSON><PERSON>", "case_title": "智能体聊天-工作流调用", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_P", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体调试聊天", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "深圳天气怎么样", "plugin": "Adaptive", "displayPrompt": "深圳天气怎么样", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_P", "version": "v2", "source": "yuanqi", "supportHint": 2}, "assertions": {"status_code": 200, "response_body": {"steps": ["data: {\"type\":\"text\"}", "event: speech_type", "data: status", "data: {\"type\":\"step\",\"msg\":\"生成中...\",\"index\":0,\"writeExtend\":{\"fileName\":\"\",\"docEditable\":false,\"fileType\":\"\",\"outline\":\"\",\"templateId\":\"\"}}", "event: speech_type", "data: text", "data: {\"type\":\"step\",\"msg\":\"正在调用工作流：天气预报\",\"scene\":\"yuanqi\",\"name\":\"天气预报\",\"index\":0,\"iconType\":5,\"top\":true,\"toolCallType\":\"工作流\"}", "data: {\"type\":\"step\",\"msg\":\"工作流[天气预报]正在运行\",\"scene\":\"yuanqi\",\"name\":\"天气预报\",\"index\":0,\"iconType\":6,\"process\":{\"total\":4,\"current\":1},\"top\":true,\"toolCallType\":\"工作流\"}", "data: {\"type\":\"step\",\"msg\":\"工作流[天气预报]正在运行\",\"scene\":\"yuanqi\",\"name\":\"天气预报\",\"index\":0,\"iconType\":6,\"process\":{\"total\":4,\"current\":2},\"top\":true,\"toolCallType\":\"工作流\"}", "data: {\"type\":\"step\",\"msg\":\"工作流[天气预报]正在运行\",\"scene\":\"yuanqi\",\"name\":\"天气预报\",\"index\":0,\"iconType\":6,\"process\":{\"total\":4,\"current\":3},\"top\":true,\"toolCallType\":\"工作流\"}", "data: {\"type\":\"step\",\"msg\":\"工作流[天气预报]正在运行\",\"scene\":\"yuanqi\",\"name\":\"天气预报\",\"index\":0,\"iconType\":6,\"process\":{\"total\":4,\"current\":4},\"top\":true,\"toolCallType\":\"工作流\"}", "data: {\"type\":\"tool_calls\",\"tool_type\":\"workflow\",\"call_id\":\"assert_str\",\"name\":\"天气预报\",\"arguments\":\"{\\\"cityName\\\":\\\"深圳\\\"}\",\"content\":\"assert_str\"}", "data: {\"type\":\"usage\",\"prompt_tokens\":\"assert_int\",\"completion_tokens\":\"assert_int\",\"total_tokens\":\"assert_int\",\"total_cost\":\"assert_number\"}", "data: {\"type\":\"yuanqi_tool_call\",\"title\":\"工作流[天气预报] 提供参考\",\"toolCallType\":\"工作流\",\"name\":\"天气预报\",\"toolCalls\":[{\"index\":1,\"title\":\"调用工作流：天气预报\"}]}", "data: \\[plugin: \\]", "data: {\"type\":\"meta\",\"messageId\":\"assert_str\",\"index\":\"assert_int\",\"replyId\":\"assert_str\",\"replyIndex\":\"assert_int\",\"traceId\":\"assert_str\",\"guideId\":0,\"unSupportRepeat\":false}", "data: \\[TRACEID:.*\\]", "data: \\[DONE\\]"], "content": "温度|湿度|风力"}}}, {"stepname": "获取元宝智能体ID", "callfunname": "get_yuanbaoId", "request": {"agentId": "$agentId_P"}, "assertions": {"status_code": 200, "response_body": {"yuanbaoAgentId": "assert_str"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "trans_2_response(yuanbaoAgentId)"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "agent_chat_03": {"author": "<PERSON><PERSON><PERSON>", "case_title": "智能体聊天-知识库链路", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_K", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体聊天-知识库调用", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "马拉松训练有哪些注意事项", "plugin": "Adaptive", "displayPrompt": "马拉松训练有哪些注意事项", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_K", "version": "v2", "source": "yuanqi", "supportHint": 1}, "assertions": {"status_code": 200, "response_body": {"steps": ["data: {\"type\":\"text\"}", "event: speech_type", "data: status", "event: speech_type", "data: text", "data: {\"type\":\"step\",\"msg\":\"正在调用知识库：「勿删」马拉松训练指南\",\"scene\":\"yuanqi\",\"name\":\"「勿删」马拉松训练指南\",\"index\":0,\"iconType\":5,\"top\":true,\"toolCallType\":\"知识库\",\"writeExtend\":{\"fileName\":\"\",\"docEditable\":false,\"fileType\":\"\",\"outline\":\"\",\"templateId\":\"\"}}", "data: {\"type\":\"tool_calls\",\"tool_type\":\"knowledge\",\"call_id\":\"assert_str\",\"name\":\"「勿删」马拉松训练指南\",\"arguments\":\"assert_str\",\"content\":\"assert_str\"}", "data: {\"type\":\"usage\",\"prompt_tokens\":\"assert_int\",\"completion_tokens\":\"assert_int\",\"total_tokens\":\"assert_int\",\"total_cost\":\"assert_number\"}", "data: {\"type\":\"yuanqi_tool_call\",\"title\":\"知识库[「勿删」马拉松训练指南] 提供参考\",\"toolCallType\":\"知识库\",\"name\":\"「勿删」马拉松训练指南\",\"toolCalls\":[{\"index\":1,\"title\":\"调用知识库：「勿删」马拉松训练指南\"}]}", "data: \\[plugin: \\]", "data: {\"type\":\"meta\",\"messageId\":\"assert_str\",\"index\":\"assert_int\",\"replyId\":\"assert_str\",\"replyIndex\":\"assert_int\",\"traceId\":\"assert_str\",\"guideId\":0,\"unSupportRepeat\":false}", "data: \\[TRACEID:.*\\]", "data: \\[DONE\\]"], "content": "马拉松训练|注意事项"}}}, {"stepname": "获取元宝智能体ID", "callfunname": "get_yuanbaoId", "request": {"agentId": "$agentId_K"}, "assertions": {"status_code": 200, "response_body": {"yuanbaoAgentId": "assert_str"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "trans_2_response(yuanbaoAgentId)"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "agent_chat_04": {"author": "<PERSON><PERSON><PERSON>", "case_title": "智能体聊天-插件调用", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_P", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体调试聊天-插件调用", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "请帮忙压缩这张图片", "plugin": "Adaptive", "displayPrompt": "请帮忙压缩这张图片", "displayPromptType": 1, "multimedia": [{"type": "image", "url": "$imgUrl", "fileName": "panda-happy.png", "size": 14822, "width": 256, "height": 256}], "agentId": "$agentId_P", "version": "v2", "source": "yuanqi", "supportHint": 1}, "assertions": {"status_code": 200, "response_body": {"steps": ["data: {\"type\":\"text\"}", "event: speech_type", "data: status", "data: {\"type\":\"step\",\"msg\":\"生成中...\",\"index\":0,\"writeExtend\":{\"fileName\":\"\",\"docEditable\":false,\"fileType\":\"\",\"outline\":\"\",\"templateId\":\"\"}}", "event: speech_type", "data: text", "data: {\"type\":\"step\",\"msg\":\"正在调用插件：「勿删」图片压缩工具2.0\",\"scene\":\"yuanqi\",\"name\":\"「勿删」图片压缩工具2.0\",\"index\":0,\"iconType\":5,\"top\":true,\"toolCallType\":\"插件\"}", "data: {\"type\":\"tool_calls\",\"tool_type\":\"function\",\"call_id\":\"assert_str\",\"name\":\"「勿删」图片压缩工具2.0\",\"arguments\":\"assert_str\",\"content\":\"assert_str\"}", "data: {\"type\":\"usage\",\"prompt_tokens\":\"assert_int\",\"completion_tokens\":\"assert_int\",\"total_tokens\":\"assert_int\",\"total_cost\":\"assert_number\"}", "data: {\"type\":\"yuanqi_tool_call\",\"title\":\"插件[「勿删」图片压缩工具2.0] 提供参考\",\"toolCallType\":\"插件\",\"name\":\"「勿删」图片压缩工具2.0\",\"toolCalls\":[{\"index\":1,\"title\":\"调用插件：「勿删」图片压缩工具2.0\"}]}", "data: \\[plugin: \\]", "data: {\"type\":\"meta\",\"messageId\":\"assert_str\",\"index\":\"assert_int\",\"replyId\":\"assert_str\",\"replyIndex\":\"assert_int\",\"traceId\":\"assert_str\",\"guideId\":0,\"unSupportRepeat\":false}", "data: \\[TRACEID:.*\\]", "data: \\[DONE\\]"], "content": "https"}}}, {"stepname": "获取元宝智能体ID", "callfunname": "get_yuanbaoId", "request": {"agentId": "$agentId_P"}, "assertions": {"status_code": 200, "response_body": {"yuanbaoAgentId": "assert_str"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "trans_2_response(yuanbaoAgentId)"}, "assertions": {"status_code": 200, "response_body": {}}}]}}