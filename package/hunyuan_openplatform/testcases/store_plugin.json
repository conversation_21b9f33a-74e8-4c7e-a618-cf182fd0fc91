{"store_plugin_list_01": {"author": "<PERSON><PERSON><PERSON>", "case_title": "商店-插件列表", "steps": [{"stepname": "获取插件列表-推荐", "callfunname": "plugin_store_list", "request": {"category": "recommend"}, "assertions": {"status_code": 200, "response_body": {"list": [{"pluginId": "EqI4VmNHVkRL", "name": "音乐生成"}], "totalPage": 1}}}, {"stepname": "获取插件列表-搜索", "callfunname": "plugin_store_list", "request": {"query": "天气查询"}, "assertions": {"status_code": 200, "response_body": {"list": [{"pluginId": "bBJ5DVLWQDYC", "name": "天气查询"}]}}}, {"stepname": "获取插件列表-筛选", "callfunname": "plugin_store_list", "request": {"filter": "official"}, "assertions": {"status_code": 200, "response_body": {"list": [{"pluginId": "xlmUifhR8XeS", "name": "文档解析"}]}}}]}, "plugin_detail_test": {"author": "<PERSON><PERSON><PERSON>", "case_title": "商店-查看插件详情", "steps": [{"stepname": "获取插件详情信息", "callfunname": "plugin_store_detail", "request": {"pluginId": "oHCkgYpPYqsS"}, "assertions": {"status_code": 200, "response_body": {"pluginId": "oHCkgYpPYqsS", "name": "腾讯文库", "description": "提取用户需要的关键信息，使用这些关键词检索腾讯文库（https://wenku.docs.qq.com/）", "logoUrl": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/652f1423/2024062811104774_b8fb1b12-7594-40fc-052b-d313614ff31b*&$@$txwk.png?imageMogr2/crop/!256x256r/gravity/center", "author": "wind", "tools": "assert_list"}}}]}, "plugin_agent_list_test": {"author": "<PERSON><PERSON><PERSON>", "case_title": "获取插件关联的智能体列表", "steps": [{"stepname": "获取插件关联的智能体列表信息", "callfunname": "plugin_agent_list", "request": {"toolId": "oHCkgYpPYqsS_TDocWenkuSearch"}, "assertions": {"status_code": 200, "response_body": {"error": {}, "list": "assert_list"}}}]}}