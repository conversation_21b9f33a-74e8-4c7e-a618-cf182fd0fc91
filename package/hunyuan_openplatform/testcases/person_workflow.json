{"create_workflow": {"author": "<PERSON><PERSON><PERSON>", "case_title": "创建工作流-查询最近3天的天气", "steps": [{"stepname": "创建工作流", "callfunname": "workflow_create", "request": {"logo": "$logoUrl", "description": "- 根据城市名称或者地址查询最近3天的天气情况", "name": "天气预报"}, "assertions": {"status_code": 200, "response_body": {"workflowId": "assert_str"}}}, {"stepname": "保存工作流", "callfunname": "workflow_save", "request": {"workflowId": "trans_0_response(workflowId)", "schema": "{\"nodes\":[{\"id\":\"10001\",\"type\":\"start\",\"data\":{\"nodeMeta\":{\"name\":\"开始\",\"logo\":\"\",\"description\":\"工作流的起点，设定开始工作流需要的信息\",\"subTitle\":\"\"},\"outputs\":{\"type\":\"object\",\"properties\":{\"cityName\":{\"type\":\"string\",\"description\":\"城市名称\"}},\"required\":[\"cityName\"]},\"inputs\":[]},\"position\":{\"x\":0,\"y\":105},\"selected\":false,\"width\":300,\"height\":580},{\"id\":\"90001\",\"type\":\"end\",\"data\":{\"nodeMeta\":{\"name\":\"结束\",\"logo\":\"\",\"description\":\"工作流的终点，返回工作流运行后的结果信息\",\"subTitle\":\"\"},\"endParam\":{\"specifyContent\":false,\"stream\":false},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"name\":\"weather\",\"type\":\"array<object>\"},\"name\":\"weather\"}]},\"position\":{\"x\":1547,\"y\":236},\"selected\":true,\"width\":444,\"height\":318,\"positionAbsolute\":{\"x\":1153.672102486812,\"y\":304.07303261922704},\"dragging\":false},{\"data\":{\"nodeMeta\":{\"name\":\"天气查询\",\"logo\":\"https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/b17778bb/2024060721525411_237d61a1-7501-4475-f57-53cf0b9c2991*&$@$aecd96bf-f0b2-4d2d-ba2f-387ede9561c7.jpg?imageMogr2/crop/!256x256r/gravity/center\",\"description\":\"根据用户输入的城市名，获取该城市最近15天的天气数据\"},\"outputs\":{\"type\":\"object\",\"properties\":{\"Code\":{\"type\":\"string\",\"description\":\"返回状态\",\"enum\":[0,500]},\"Data\":{\"type\":\"object\",\"properties\":{\"city\":{\"type\":\"object\",\"properties\":{\"cityId\":{\"type\":\"integer\",\"description\":\"城市Id\"},\"counname\":{\"type\":\"string\",\"description\":\"返回的状态信息\"},\"name\":{\"type\":\"string\",\"description\":\"地区名称\"},\"pname\":{\"type\":\"string\",\"description\":\"省份\"}}},\"currentTime\":{\"type\":\"string\",\"description\":\"当前时间\"},\"forecast\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"conditionDay\":{\"type\":\"string\"},\"conditionIdDay\":{\"type\":\"string\"},\"conditionIdNight\":{\"type\":\"string\"},\"conditionNight\":{\"type\":\"string\"},\"moonphase\":{\"type\":\"string\"},\"moonrise\":{\"type\":\"string\"},\"moonset\":{\"type\":\"string\"},\"predictDate\":{\"type\":\"string\"},\"sunrise\":{\"type\":\"string\"},\"sunset\":{\"type\":\"string\"},\"tempDay\":{\"type\":\"string\"},\"tempNight\":{\"type\":\"string\"},\"updatetime\":{\"type\":\"string\"},\"windDirDay\":{\"type\":\"string\"},\"windDirNight\":{\"type\":\"string\"},\"windLevelDay\":{\"type\":\"string\"},\"windLevelNight\":{\"type\":\"string\"},\"windSpeedDay\":{\"type\":\"string\"},\"windSpeedNight\":{\"type\":\"string\"}}}}}},\"Msg\":{\"type\":\"string\",\"description\":\"返回的状态信息\"}}},\"pluginParam\":{\"toolId\":\"bBJ5DVLWQDYC_forecast15days\",\"pluginId\":\"bBJ5DVLWQDYC\"},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"10001\",\"name\":\"cityName\",\"type\":\"string\"},\"name\":\"CityName\",\"required\":true}]},\"id\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"type\":\"plugin\",\"position\":{\"x\":400,\"y\":146},\"sourcePosition\":\"right\",\"targetPosition\":\"left\",\"selected\":false,\"width\":447,\"height\":498,\"positionAbsolute\":{\"x\":41.48997739261472,\"y\":-175.2952524491335},\"dragging\":false},{\"data\":{\"nodeMeta\":{\"name\":\"代码\",\"logo\":\"\",\"description\":\"支持通过 Python 处理入参，并返回处理结果。\",\"subTitle\":\"\"},\"endParam\":{\"specifyContent\":false},\"outputs\":{\"type\":\"object\",\"properties\":{\"weather\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"conditionDay\":{\"type\":\"string\"},\"conditionIdDay\":{\"type\":\"string\"},\"conditionIdNight\":{\"type\":\"string\"},\"conditionNight\":{\"type\":\"string\"},\"moonphase\":{\"type\":\"string\"},\"moonrise\":{\"type\":\"string\"},\"moonset\":{\"type\":\"string\"},\"predictDate\":{\"type\":\"string\"},\"sunrise\":{\"type\":\"string\"},\"sunset\":{\"type\":\"string\"},\"tempDay\":{\"type\":\"string\"},\"tempNight\":{\"type\":\"string\"},\"updatetime\":{\"type\":\"string\"},\"windDirDay\":{\"type\":\"string\"},\"windDirNight\":{\"type\":\"string\"},\"windLevelDay\":{\"type\":\"string\"},\"windLevelNight\":{\"type\":\"string\"},\"windSpeedDay\":{\"type\":\"string\"},\"windSpeedNight\":{\"type\":\"string\"}}}}}},\"codeParam\":{\"language\":1,\"code\":\"async def main(args):\\n    weather_data = args['weather'] \\n    weather = weather_data[:3]\\n\\n    ret = {\\n        \\\"weather\\\":weather\\n    }\\n    print('result is: ', ret)\\n    return ret\"},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"name\":\"Data.forecast\",\"type\":\"array<object>\"},\"name\":\"weather\"}]},\"id\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"type\":\"code\",\"position\":{\"x\":947,\"y\":0},\"sourcePosition\":\"right\",\"targetPosition\":\"left\",\"selected\":false,\"width\":500,\"height\":790,\"positionAbsolute\":{\"x\":536.8033157498116,\"y\":84.52027128862096},\"dragging\":false}],\"edges\":[{\"source\":\"10001\",\"sourceHandle\":null,\"target\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-10001-60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}},{\"source\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"sourceHandle\":null,\"target\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-60001_8096dfa7-6f35-447d-df0-2589cdf38e93-20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}},{\"source\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"sourceHandle\":null,\"target\":\"90001\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-20001_cb8049c6-67dd-4115-0db7-461dfd67f05c-90001\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}}],\"viewport\":{\"x\":60.31818181818187,\"y\":324.6661796265011,\"zoom\":0.605908406008858}}"}, "assertions": {"status_code": 200, "response_body": {"status": 1}}}, {"stepname": "检查工作流", "callfunname": "workflow_check", "request": {"workflowId": "trans_0_response(workflowId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}}}}, {"stepname": "测试运行工作流", "callfunname": "workflow_testrun", "request": {"workflowId": "trans_0_response(workflowId)", "inputs": {"cityName": "武汉"}}, "assertions": {"status_code": 200, "response_body": {"error": {}, "executeId": "assert_str"}}}, {"stepname": "检查工作流运行状态", "callfunname": "workflow_process", "sleep": 30, "request": {"executeId": "trans_3_response(executeId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}}}}, {"stepname": "保存工作流", "callfunname": "workflow_save", "request": {"workflowId": "trans_0_response(workflowId)", "schema": "{\"nodes\":[{\"id\":\"10001\",\"type\":\"start\",\"data\":{\"nodeMeta\":{\"name\":\"开始\",\"logo\":\"\",\"description\":\"工作流的起点，设定开始工作流需要的信息\",\"subTitle\":\"\"},\"outputs\":{\"type\":\"object\",\"properties\":{\"cityName\":{\"type\":\"string\",\"description\":\"城市名称\"}},\"required\":[\"cityName\"]},\"inputs\":[]},\"position\":{\"x\":0,\"y\":105},\"selected\":false,\"width\":300,\"height\":580},{\"id\":\"90001\",\"type\":\"end\",\"data\":{\"nodeMeta\":{\"name\":\"结束\",\"logo\":\"\",\"description\":\"工作流的终点，返回工作流运行后的结果信息\",\"subTitle\":\"\"},\"endParam\":{\"specifyContent\":false,\"stream\":false},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"name\":\"weather\",\"type\":\"array<object>\"},\"name\":\"weather\"}]},\"position\":{\"x\":1547,\"y\":236},\"selected\":true,\"width\":444,\"height\":318,\"positionAbsolute\":{\"x\":1153.672102486812,\"y\":304.07303261922704},\"dragging\":false},{\"data\":{\"nodeMeta\":{\"name\":\"天气查询\",\"logo\":\"https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/b17778bb/2024060721525411_237d61a1-7501-4475-f57-53cf0b9c2991*&$@$aecd96bf-f0b2-4d2d-ba2f-387ede9561c7.jpg?imageMogr2/crop/!256x256r/gravity/center\",\"description\":\"根据用户输入的城市名，获取该城市最近15天的天气数据\"},\"outputs\":{\"type\":\"object\",\"properties\":{\"Code\":{\"type\":\"string\",\"description\":\"返回状态\",\"enum\":[0,500]},\"Data\":{\"type\":\"object\",\"properties\":{\"city\":{\"type\":\"object\",\"properties\":{\"cityId\":{\"type\":\"integer\",\"description\":\"城市Id\"},\"counname\":{\"type\":\"string\",\"description\":\"返回的状态信息\"},\"name\":{\"type\":\"string\",\"description\":\"地区名称\"},\"pname\":{\"type\":\"string\",\"description\":\"省份\"}}},\"currentTime\":{\"type\":\"string\",\"description\":\"当前时间\"},\"forecast\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"conditionDay\":{\"type\":\"string\"},\"conditionIdDay\":{\"type\":\"string\"},\"conditionIdNight\":{\"type\":\"string\"},\"conditionNight\":{\"type\":\"string\"},\"moonphase\":{\"type\":\"string\"},\"moonrise\":{\"type\":\"string\"},\"moonset\":{\"type\":\"string\"},\"predictDate\":{\"type\":\"string\"},\"sunrise\":{\"type\":\"string\"},\"sunset\":{\"type\":\"string\"},\"tempDay\":{\"type\":\"string\"},\"tempNight\":{\"type\":\"string\"},\"updatetime\":{\"type\":\"string\"},\"windDirDay\":{\"type\":\"string\"},\"windDirNight\":{\"type\":\"string\"},\"windLevelDay\":{\"type\":\"string\"},\"windLevelNight\":{\"type\":\"string\"},\"windSpeedDay\":{\"type\":\"string\"},\"windSpeedNight\":{\"type\":\"string\"}}}}}},\"Msg\":{\"type\":\"string\",\"description\":\"返回的状态信息\"}}},\"pluginParam\":{\"toolId\":\"bBJ5DVLWQDYC_forecast15days\",\"pluginId\":\"bBJ5DVLWQDYC\"},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"10001\",\"name\":\"cityName\",\"type\":\"string\"},\"name\":\"CityName\",\"required\":true}]},\"id\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"type\":\"plugin\",\"position\":{\"x\":400,\"y\":146},\"sourcePosition\":\"right\",\"targetPosition\":\"left\",\"selected\":false,\"width\":447,\"height\":498,\"positionAbsolute\":{\"x\":41.48997739261472,\"y\":-175.2952524491335},\"dragging\":false},{\"data\":{\"nodeMeta\":{\"name\":\"代码\",\"logo\":\"\",\"description\":\"支持通过 Python 处理入参，并返回处理结果。\",\"subTitle\":\"\"},\"endParam\":{\"specifyContent\":false},\"outputs\":{\"type\":\"object\",\"properties\":{\"weather\":{\"type\":\"array\",\"items\":{\"type\":\"object\",\"properties\":{\"conditionDay\":{\"type\":\"string\"},\"conditionIdDay\":{\"type\":\"string\"},\"conditionIdNight\":{\"type\":\"string\"},\"conditionNight\":{\"type\":\"string\"},\"moonphase\":{\"type\":\"string\"},\"moonrise\":{\"type\":\"string\"},\"moonset\":{\"type\":\"string\"},\"predictDate\":{\"type\":\"string\"},\"sunrise\":{\"type\":\"string\"},\"sunset\":{\"type\":\"string\"},\"tempDay\":{\"type\":\"string\"},\"tempNight\":{\"type\":\"string\"},\"updatetime\":{\"type\":\"string\"},\"windDirDay\":{\"type\":\"string\"},\"windDirNight\":{\"type\":\"string\"},\"windLevelDay\":{\"type\":\"string\"},\"windLevelNight\":{\"type\":\"string\"},\"windSpeedDay\":{\"type\":\"string\"},\"windSpeedNight\":{\"type\":\"string\"}}}}}},\"codeParam\":{\"language\":1,\"code\":\"async def main(args):\\n    weather_data = args['weather'] \\n    weather = weather_data[:3]\\n\\n    ret = {\\n        \\\"weather\\\":weather\\n    }\\n    print('result is: ', ret)\\n    return ret\"},\"inputs\":[{\"inputType\":\"reference\",\"reference\":{\"nodeId\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"name\":\"Data.forecast\",\"type\":\"array<object>\"},\"name\":\"weather\"}]},\"id\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"type\":\"code\",\"position\":{\"x\":947,\"y\":0},\"sourcePosition\":\"right\",\"targetPosition\":\"left\",\"selected\":false,\"width\":500,\"height\":790,\"positionAbsolute\":{\"x\":536.8033157498116,\"y\":84.52027128862096},\"dragging\":false}],\"edges\":[{\"source\":\"10001\",\"sourceHandle\":null,\"target\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-10001-60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}},{\"source\":\"60001_8096dfa7-6f35-447d-df0-2589cdf38e93\",\"sourceHandle\":null,\"target\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-60001_8096dfa7-6f35-447d-df0-2589cdf38e93-20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}},{\"source\":\"20001_cb8049c6-67dd-4115-0db7-461dfd67f05c\",\"sourceHandle\":null,\"target\":\"90001\",\"targetHandle\":null,\"type\":\"custom\",\"id\":\"reactflow__edge-20001_cb8049c6-67dd-4115-0db7-461dfd67f05c-90001\",\"animated\":false,\"selected\":false,\"markerEnd\":{\"type\":\"arrow\",\"width\":20,\"height\":20,\"color\":\"#A6A6A6\"},\"style\":{\"strokeWidth\":2,\"stroke\":\"#A6A6A6\"}}],\"viewport\":{\"x\":60.31818181818187,\"y\":324.6661796265011,\"zoom\":0.605908406008858}}"}, "assertions": {"status_code": 200, "response_body": {"status": 1}}}, {"stepname": "获取工作流标签", "callfunname": "workflow_tags", "request": {}, "assertions": {"status_code": 200, "response_body": {"categories": "assert_list"}}}, {"stepname": "发布工作流", "callfunname": "workflow_publish", "request": {"tag": "trans_6_response($.categories[0].category)", "workflowId": "trans_0_response(workflowId)"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "工作流详情", "callfunname": "workflow_detail", "request": {"workflowId": "trans_0_response(workflowId)"}, "assertions": {"status_code": 200, "response_body": {"workflowId": "trans_0_response(workflowId)", "name": "天气预报", "description": "- 根据城市名称或者地址查询最近3天的天气情况", "tag": "category1", "testPass": true, "isPublished": true}}}, {"stepname": "删除工作流", "callfunname": "workflow_delete", "request": {"workflowId": "trans_0_response(workflowId)"}, "assertions": {"status_code": 200, "response_body": {}}}]}}