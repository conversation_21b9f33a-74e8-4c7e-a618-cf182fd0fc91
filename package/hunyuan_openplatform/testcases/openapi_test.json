{"agent_chat_openapi_test_01": {"case_title": "openapi聊天测试", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送正常聊天消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "user", "content": [{"type": "text", "text": "你是谁"}]}]}, "assertions": {"status_code": 200, "response_body": {}}}]}, "agent_chat_openapi_test_02": {"case_title": "openapi聊天测试-请求消息不能为空", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": []}, "assertions": {"status_code": 400, "response_body": {}}}]}, "agent_chat_openapi_test_03": {"case_title": "openapi聊天测试-请求的第一个消息角色必须是user", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "assistant", "content": [{"type": "text", "text": "您好，请问有什么可以帮到您"}]}, {"role": "user", "content": [{"type": "text", "text": "哈哈哈哈哈哈哈哈哈"}]}]}, "assertions": {"status_code": 400, "response_body": {}}}]}, "agent_chat_openapi_test_04": {"case_title": "openapi聊天测试-请求的最后一个消息角色必须是user", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "user", "content": [{"type": "text", "text": "什么是快乐星球"}]}, {"role": "assistant", "content": [{"type": "text", "text": "快乐星球就是快乐呀"}]}]}, "assertions": {"status_code": 400, "response_body": {}}}]}, "agent_chat_openapi_test_05": {"case_title": "openapi聊天测试-请求的消息角色只能是user和assistant", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "test", "content": [{"type": "text", "text": "什么是快乐星球"}]}, {"role": "test2", "content": [{"type": "text", "text": "快乐星球就是快乐呀"}]}]}, "assertions": {"status_code": 400, "response_body": {}}}]}, "agent_chat_openapi_test_06": {"case_title": "openapi聊天测试-消息中user和assistant需要交替出现", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "user", "content": [{"type": "text", "text": "你是谁"}]}, {"role": "assistant", "content": [{"type": "text", "text": "你是谁"}]}, {"role": "assistant", "content": [{"type": "text", "text": "你是谁"}]}]}, "assertions": {"status_code": 400, "response_body": {}}}]}, "agent_chat_openapi_test_07": {"case_title": "openapi聊天测试-角色为user的消息内容不能为空", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "发送消息, user内容为空字符串", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "user", "content": [{"type": "text", "text": ""}]}]}, "assertions": {"status_code": 400, "response_body": {}}}, {"stepname": "发送消息, user内容为null", "callfunname": "openapi_chat", "request": {"assistant_id": "xFtUXH80UTPy", "user_id": "$userid", "stream": false, "messages": [{"role": "user", "content": [{"type": "text", "text": null}]}]}, "assertions": {"status_code": 400, "response_body": {}}}]}}