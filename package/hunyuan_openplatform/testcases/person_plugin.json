{"create_plugin_01": {"author": "<PERSON><PERSON><PERSON>", "case_title": "插件创建和发布-无鉴权-无文件", "steps": [{"stepname": "创建插件", "callfunname": "plugin_create", "request": {"authToken": "", "authParameterName": "", "authTokenType": "", "authType": "none", "mediaType": "", "logoUrl": "$logoUrl", "description": "根据关键词查询宝可梦信息", "name": "宝可梦查询(自动化)"}, "assertions": {"status_code": 200, "response_body": {"pluginId": "assert_str"}}}, {"stepname": "解析插件yaml描述文件", "callfunname": "plugin_parse_api", "request": {"apiDescription": "info:\n    description: 宝可梦信息大全\n    title: 宝可梦\n    version: v1\nopenapi: 3.0.1\npaths:\n    /play/pokedex/api/v1:\n        get:\n            operationId: search\n            parameters:\n                - description: 查询关键词\n                  in: query\n                  name: key_word\n                  required: true\n                  schema:\n                    description: 查询关键词\n                    type: string\n            responses:\n                \"200\":\n                    content:\n                        application/json:\n                            schema:\n                                properties:\n                                    pokemons:\n                                        items:\n                                            properties:\n                                                file_name:\n                                                    type: string\n                                                height:\n                                                    type: number\n                                                pokemon_name:\n                                                    type: string\n                                                pokemon_sub_name:\n                                                    type: string\n                                                pokemon_type_id:\n                                                    type: string\n                                                pokemon_type_name:\n                                                    type: string\n                                                weight:\n                                                    type: number\n                                                zukan_id:\n                                                    type: string\n                                                zukan_sub_id:\n                                                    type: number\n                                            type: object\n                                        type: array\n                                type: object\n                    description: new desc\n                default:\n                    description: \"\"\n            summary: 根据关键词查询宝可梦信息\nservers:\n    - url: https://www.pokemon.cn\n", "pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}, "tools": [{"id": "assert_str", "operationID": "search", "description": "根据关键词查询宝可梦信息", "sampleAgent": "", "path": "https://www.pokemon.cn/play/pokedex/api/v1", "request": {"type": "object", "required": ["key_word"], "properties": {"key_word": {"type": "string", "description": "查询关键词"}}}, "response": {"type": "object", "properties": {"pokemons": {"type": "array", "items": {"type": "object", "properties": {"file_name": {"type": "string"}, "height": {"type": "number"}, "pokemon_name": {"type": "string"}, "pokemon_sub_name": {"type": "string"}, "pokemon_type_id": {"type": "string"}, "pokemon_type_name": {"type": "string"}, "weight": {"type": "number"}, "zukan_id": {"type": "string"}, "zukan_sub_id": {"type": "number"}}}}}}, "testInfo": {"request": "", "response": "", "errorInfo": ""}}]}}}, {"stepname": "插件更新", "callfunname": "plugin_update", "request": {"pluginId": "trans_0_response(pluginId)", "error": {}, "name": "宝可梦查询(自动化)", "description": "根据关键词查询宝可梦信息", "logoUrl": "$logoUrl", "host": "", "mediaType": "", "authType": "none", "authTokenType": "", "authToken": "", "authParameterName": "", "apiDescription": "info:\n    description: 宝可梦信息大全\n    title: 宝可梦\n    version: v1\nopenapi: 3.0.1\npaths:\n    /play/pokedex/api/v1:\n        get:\n            operationId: search\n            parameters:\n                - description: 查询关键词\n                  in: query\n                  name: key_word\n                  required: true\n                  schema:\n                    description: 查询关键词\n                    type: string\n            responses:\n                \"200\":\n                    content:\n                        application/json:\n                            schema:\n                                properties:\n                                    pokemons:\n                                        items:\n                                            properties:\n                                                file_name:\n                                                    type: string\n                                                height:\n                                                    type: number\n                                                pokemon_name:\n                                                    type: string\n                                                pokemon_sub_name:\n                                                    type: string\n                                                pokemon_type_id:\n                                                    type: string\n                                                pokemon_type_name:\n                                                    type: string\n                                                weight:\n                                                    type: number\n                                                zukan_id:\n                                                    type: string\n                                                zukan_sub_id:\n                                                    type: number\n                                            type: object\n                                        type: array\n                                type: object\n                    description: new desc\n                default:\n                    description: \"\"\n            summary: 根据关键词查询宝可梦信息\nservers:\n    - url: https://www.pokemon.cn\n", "status": "draft", "createdAt": "2024-06-07 15:59", "updatedAt": "2024-06-07 15:59", "publishedAt": "", "tools": "trans_1_response(tools)", "publish": {"category": "", "visibleRange": "", "publishComment": ""}}, "assertions": {"status_code": 200, "response_body": {"pluginId": "assert_str"}}}, {"stepname": "插件校验", "callfunname": "plugin_api_test", "request": {"toolId": "trans_1_response($.tools[0].id)", "pluginId": "trans_0_response(pluginId)", "request": "{\"key_word\":\"小火龙\"}"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "插件发布", "callfunname": "plugin_publish", "request": {"category": "category1", "visibleRange": "private", "publishComment": "", "publishPlatform": ["yuanqistore", "yuanbaoapp"], "pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "已提交至审核流程"}}}}, {"stepname": "插件详情", "callfunname": "plugin_detail", "request": {"pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}, "pluginId": "assert_str", "name": "宝可梦查询(自动化)", "description": "根据关键词查询宝可梦信息", "logoUrl": "assert_str", "host": "", "mediaType": "", "authType": "none", "authTokenType": "", "authToken": "", "authParameterName": "", "apiDescription": "info:\n    description: 宝可梦信息大全\n    title: 宝可梦\n    version: v1\nopenapi: 3.0.1\npaths:\n    /play/pokedex/api/v1:\n        get:\n            operationId: search\n            parameters:\n                - description: 查询关键词\n                  in: query\n                  name: key_word\n                  required: true\n                  schema:\n                    description: 查询关键词\n                    type: string\n            responses:\n                \"200\":\n                    content:\n                        application/json:\n                            schema:\n                                properties:\n                                    pokemons:\n                                        items:\n                                            properties:\n                                                file_name:\n                                                    type: string\n                                                height:\n                                                    type: number\n                                                pokemon_name:\n                                                    type: string\n                                                pokemon_sub_name:\n                                                    type: string\n                                                pokemon_type_id:\n                                                    type: string\n                                                pokemon_type_name:\n                                                    type: string\n                                                weight:\n                                                    type: number\n                                                zukan_id:\n                                                    type: string\n                                                zukan_sub_id:\n                                                    type: number\n                                            type: object\n                                        type: array\n                                type: object\n                    description: new desc\n                default:\n                    description: \"\"\n            summary: 根据关键词查询宝可梦信息\nservers:\n    - url: https://www.pokemon.cn\n", "status": "draft", "publishedAt": "", "tools": "assert_list", "publish": {"visibleRange": "private", "publishComment": ""}}}}, {"stepname": "插件列表查询", "callfunname": "plugin_list", "request": {}, "assertions": {"status_code": 200, "response_body": {"list": [{"pluginId": "trans_0_response(pluginId)"}]}}}]}, "create_plugin_02": {"author": "<PERSON><PERSON><PERSON>", "case_title": "插件创建和发布-有鉴权-有文件", "steps": [{"stepname": "创建插件", "callfunname": "plugin_create", "request": {"authToken": "Basic NThxZnY1cXFRaHc2ZDhQQ0RabENDU0d6Y044amR2d1Y=", "authParameterName": "Authorization", "authTokenType": "header", "authType": "service_http", "mediaType": "jpg,png", "logoUrl": "$logoUrl", "description": "将用户上传的图片或者图片链接进行压缩并返回图片链接", "name": "图片压缩工具"}, "assertions": {"status_code": 200, "response_body": {"pluginId": "assert_str"}}}, {"stepname": "解析插件yaml描述文件", "callfunname": "plugin_parse_api", "request": {"apiDescription": "openapi: 3.0.0\ninfo:\n  title: Tinify API\n  version: 1.0.0\nservers:\n  - url: https://api.tinify.com\npaths:\n  /shrink:\n    post:\n      operationId: shrink_image\n      description: 传入图片url，压缩图片后返回图片链接\n      requestBody:\n        required: true\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                source:\n                  type: object\n                  description: 图片url\n                  properties:\n                    url:\n                      type: string\n                      format: uri\n                      description: 图片url\n                  required:\n                    - url\n      responses:\n        '201':\n          description: Successful response\n          content:\n            application/json:\n              schema:\n                type: object\n                properties:\n                  input:\n                    type: object\n                    description: 输入图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 输入图片尺寸\n                      type:\n                        type: string\n                    required:\n                      - size\n                      - type\n                  output:\n                    type: object\n                    description: 输出图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 图片大小\n                      type:\n                        type: string\n                      width:\n                        type: integer\n                        description: 宽\n                      height:\n                        type: integer\n                        description: 高\n                      ratio:\n                        type: number\n                      url:\n                        type: string\n                        format: uri\n                        description: 输出图片链接\n                    required:\n                      - size\n                      - type\n                      - width\n                      - height\n                      - ratio\n                      - url", "pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}, "tools": [{"id": "assert_str", "operationID": "shrink_image", "description": "传入图片url，压缩图片后返回图片链接", "sampleAgent": "", "path": "https://api.tinify.com/shrink", "request": {"type": "object", "properties": {"source": {"type": "object", "description": "图片url", "required": ["url"], "properties": {"url": {"type": "string", "description": "图片url"}}}}}, "response": {"type": "object", "properties": {"input": {"type": "object", "description": "输入图片参数", "required": ["size", "type"], "properties": {"size": {"type": "integer", "description": "输入图片尺寸"}, "type": {"type": "string"}}}, "output": {"type": "object", "description": "输出图片参数", "required": ["size", "type", "width", "height", "ratio", "url"], "properties": {"height": {"type": "integer", "description": "高"}, "ratio": {"type": "number"}, "size": {"type": "integer", "description": "图片大小"}, "type": {"type": "string"}, "url": {"type": "string", "description": "输出图片链接"}, "width": {"type": "integer", "description": "宽"}}}}}, "testInfo": {"request": "", "response": "", "errorInfo": ""}}]}}}, {"stepname": "插件更新", "callfunname": "plugin_update", "request": {"pluginId": "trans_0_response(pluginId)", "error": {}, "name": "图片压缩工具", "description": "将用户上传的图片或者图片链接进行压缩并返回图片链接", "logoUrl": "$logoUrl", "mediaType": "jpg,png", "authType": "service_http", "authTokenType": "header", "authToken": "Basic NThxZnY1cXFRaHc2ZDhQQ0RabENDU0d6Y044amR2d1Y=", "authParameterName": "Authorization", "apiDescription": "openapi: 3.0.0\ninfo:\n  title: Tinify API\n  version: 1.0.0\nservers:\n  - url: https://api.tinify.com\npaths:\n  /shrink:\n    post:\n      operationId: shrink_image\n      description: 传入图片url，压缩图片后返回图片链接\n      requestBody:\n        required: true\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                source:\n                  type: object\n                  description: 图片url\n                  properties:\n                    url:\n                      type: string\n                      format: uri\n                      description: 图片url\n                  required:\n                    - url\n      responses:\n        '201':\n          description: Successful response\n          content:\n            application/json:\n              schema:\n                type: object\n                properties:\n                  input:\n                    type: object\n                    description: 输入图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 输入图片尺寸\n                      type:\n                        type: string\n                    required:\n                      - size\n                      - type\n                  output:\n                    type: object\n                    description: 输出图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 图片大小\n                      type:\n                        type: string\n                      width:\n                        type: integer\n                        description: 宽\n                      height:\n                        type: integer\n                        description: 高\n                      ratio:\n                        type: number\n                      url:\n                        type: string\n                        format: uri\n                        description: 输出图片链接\n                    required:\n                      - size\n                      - type\n                      - width\n                      - height\n                      - ratio\n                      - url", "status": "draft", "createdAt": "2024-09-10 12:00", "updatedAt": "2024-09-10 12:00", "publishedAt": "", "tools": "trans_1_response(tools)", "publish": {"category": "", "visibleRange": "", "publishComment": ""}}, "assertions": {"status_code": 200, "response_body": {"pluginId": "assert_str"}}}, {"stepname": "插件校验", "callfunname": "plugin_api_test", "request": {"toolId": "trans_1_response($.tools[0].id)", "pluginId": "trans_0_response(pluginId)", "request": "{\"source\":{\"url\":\"https://tinypng.com/images/panda-happy.png\"}}"}, "assertions": {"status_code": 200, "response_body": {}}}, {"stepname": "插件发布", "callfunname": "plugin_publish", "request": {"category": "category1", "visibleRange": "private", "publishComment": "", "publishPlatform": ["yuanqistore", "yuanbaoapp"], "pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {"code": "0", "message": "已提交至审核流程"}}}}, {"stepname": "插件详情", "callfunname": "plugin_detail", "request": {"pluginId": "trans_0_response(pluginId)"}, "assertions": {"status_code": 200, "response_body": {"error": {}, "pluginId": "assert_str", "name": "图片压缩工具", "description": "将用户上传的图片或者图片链接进行压缩并返回图片链接", "logoUrl": "assert_str", "host": "", "mediaType": "jpg,png", "authType": "service_http", "authTokenType": "header", "authToken": "Basic NThxZnY1cXFRaHc2ZDhQQ0RabENDU0d6Y044amR2d1Y=", "authParameterName": "Authorization", "apiDescription": "openapi: 3.0.0\ninfo:\n  title: Tinify API\n  version: 1.0.0\nservers:\n  - url: https://api.tinify.com\npaths:\n  /shrink:\n    post:\n      operationId: shrink_image\n      description: 传入图片url，压缩图片后返回图片链接\n      requestBody:\n        required: true\n        content:\n          application/json:\n            schema:\n              type: object\n              properties:\n                source:\n                  type: object\n                  description: 图片url\n                  properties:\n                    url:\n                      type: string\n                      format: uri\n                      description: 图片url\n                  required:\n                    - url\n      responses:\n        '201':\n          description: Successful response\n          content:\n            application/json:\n              schema:\n                type: object\n                properties:\n                  input:\n                    type: object\n                    description: 输入图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 输入图片尺寸\n                      type:\n                        type: string\n                    required:\n                      - size\n                      - type\n                  output:\n                    type: object\n                    description: 输出图片参数\n                    properties:\n                      size:\n                        type: integer\n                        description: 图片大小\n                      type:\n                        type: string\n                      width:\n                        type: integer\n                        description: 宽\n                      height:\n                        type: integer\n                        description: 高\n                      ratio:\n                        type: number\n                      url:\n                        type: string\n                        format: uri\n                        description: 输出图片链接\n                    required:\n                      - size\n                      - type\n                      - width\n                      - height\n                      - ratio\n                      - url", "status": "draft", "publishedAt": "", "tools": "assert_list", "publish": {"visibleRange": "private", "publishComment": ""}}}}, {"stepname": "插件列表查询", "callfunname": "plugin_list", "request": {}, "assertions": {"status_code": 200, "response_body": {"list": [{"pluginId": "trans_0_response(pluginId)"}]}}}]}}