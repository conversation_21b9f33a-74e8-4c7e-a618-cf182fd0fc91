{"official_plugin_forecast15days": {"case_title": "官方插件测试-天气查询", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - 调用天气查询插件", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "深圳今天天气怎么样", "plugin": "Adaptive", "displayPrompt": "深圳今天天气怎么样", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "天气|温度"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "official_plugin_getExpressInfo": {"case_title": "官方插件测试-快递查询", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - 查询物流信息", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "查询物流信息：JD0160055992401", "plugin": "Adaptive", "displayPrompt": "查询物流信息：JD0160055992401", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "京东"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "official_plugin_imageChat": {"case_title": "官方插件测试-图片理解", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - 图片理解插件", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "理解图片的内容", "plugin": "Adaptive", "displayPrompt": "理解图片的内容", "displayPromptType": 1, "multimedia": [{"type": "image", "url": "$imgUrl", "fileName": "223603dQLk1.jpg", "size": 1304231, "width": 1920, "height": 1080}], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "山脉|天空|人物|飞鸟"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "official_plugin_generateImage": {"case_title": "官方插件测试-混元文生图", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - 混元文生图", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "画一幅画：蓝天白云", "plugin": "Adaptive", "displayPrompt": "画一幅画：蓝天白云", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "https"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "official_plugin_detection": {"case_title": "官方插件测试-OCR识别", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - OCR识别", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "识别图片中的文字", "plugin": "Adaptive", "displayPrompt": "识别图片中的文字", "displayPromptType": 1, "multimedia": [{"type": "image", "url": "https://hunyuan.tencent.com/api/resource/download?resourceId=7919d85a5cbd41fb60143d29bd7f90c4", "fileName": "logo.png", "size": 9683, "width": 299, "height": 299}], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "专项测试"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}, "official_plugin_browser": {"case_title": "官方插件测试-网页解析", "author": "<PERSON><PERSON><PERSON>", "steps": [{"stepname": "获取会话id", "callfunname": "get_conversations", "request": {"agentId": "$agentId_official_plugin", "index": 0, "limit": 10, "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"id": "assert_str"}}}, {"stepname": "智能体对话 - 网页解析插件", "callfunname": "api_chat", "stream": "True", "request": {"cid": "trans_0_response(id)", "model": "gpt_175B_0404", "prompt": "解析网页内容：https://new.qq.com/rain/a/20241008A02D5A00", "plugin": "Adaptive", "displayPrompt": "解析网页内容：https://new.qq.com/rain/a/20241008A02D5A00", "displayPromptType": 1, "multimedia": [], "agentId": "$agentId_official_plugin", "version": "v2", "source": "yuanqi"}, "assertions": {"status_code": 200, "response_body": {"content": "伊涅斯塔"}}}, {"stepname": "清除对话", "callfunname": "conversation_clear", "request": {"agentId": "$agentId_official_plugin"}, "assertions": {"status_code": 200, "response_body": {}}}]}}