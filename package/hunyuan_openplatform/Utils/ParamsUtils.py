#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/8
# <AUTHOR> jam<PERSON><PERSON>
# @File    : Params_Utils.py
# @Desc    : 测试用例数据处理
import ast
import json
import logging
import re
import os
import jsonpath
ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))


def replace_value(value, func):
    if isinstance(value, dict):
        return {k: replace_value(v, func) for k, v in value.items()}
    elif isinstance(value, list):
        return [replace_value(v, func) for v in value]
    else:
        return func(value)

def update_config(data, config_data):
    """替换用例中的配置项"""
    def replace_config_value(value):
        if isinstance(value, str) and value.startswith("$"):
            return config_data.get(value[1:], value)
        else:
            return value

    return replace_value(data, replace_config_value)

def get_jsonpath(json_path, json_data):
    """获取jsonpath"""
    return jsonpath.jsonpath(json_data, json_path)[0]


def update_trans(step_data, case_data):
    """替换参数引用"""
    def replace_trans_value(value):
        if isinstance(value, str) and value.startswith("trans"):
            value_list = re.split(r'[_\(\)]', value)
            # logging.info(f"value_list: {value_list}")
            trans_index = int(value_list[1])
            trans_type = value_list[2]
            trans_key = value_list[3]
            # logging.info(f"trans_index: {trans_index}, trans_type: {trans_type}, trans_key: {trans_key}")
            if trans_key.startswith("$"):
                value = jsonpath.jsonpath(case_data["steps"][trans_index][trans_type], trans_key)[0]
                # logging.info(f"value: {value}")
            else:
                value = case_data["steps"][trans_index][trans_type][trans_key]
            return value
        else:
            return value

    return replace_value(step_data, replace_trans_value)


def get_replace_value(index, key, casedata):
    """替换请求参数中的上下文引用"""
    for temp, step_data in enumerate(casedata["steps"]):
        if temp == index:
            # 替换request中的上下文引用
            if key in ("request_params", "request_body", "request"):
                return update_trans(step_data[key], casedata)
            # 替换assertion中的上下文引用
            if key == "assertions":
                return update_trans(step_data[key]["response_body"], casedata)
    return ''

class CaseData:
    """读取测试用例数据"""
    data_path = os.path.join(ROOT_DIR, 'testcases')

    def __init__(self):
        self.data = {}

    @property
    def case_data(self):
        case_list = []
        file_list = os.listdir(CaseData.data_path)
        for file in file_list:
            with open(os.path.join(CaseData.data_path, file), 'r', encoding='utf-8') as f:
                file_data = json.load(f)
                self.data.update(file_data)
        for case_data in self.data.values():
            case_list.append(case_data)
        return case_list

    @property
    def case_ids(self):
        ids = []
        for case_data in self.data.values():
            ids.append(case_data["case_title"])
        return ids


class ApiData:
    """读取接口配置数据"""
    data_path = os.path.join(ROOT_DIR, 'api_data')

    def __init__(self):
        self.data = {}

    @property
    def api_data(self):
        file_list = os.listdir(ApiData.data_path)
        for file in file_list:
            file_path = os.path.join(ApiData.data_path, file)
            with open(file_path, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
                self.data.update(file_data)
        return self.data

def data_analysis(response_body):
    """将流式输出结果中的data数据解析成字典"""
    data = []
    for step in response_body:
        if step.startswith('data: {'):
            step_data = {}
            temp_json = step.replace('data: ', '')
            try:
                json_data = json.loads(temp_json, strict=False)
            except json.decoder.JSONDecodeError:
                raise Exception('解析失败, 请检查流式输出数据格式或则断言数据中的json字符串格式是否正确')
            step_data["data"] = json_data
            data.append(step_data)
        else:
            data.append(step)
    logging.info('解析后的流式输出结果：{}'.format(data))
    return data

def chat_response(response_body):
    """提取出聊天接口中的文本信息"""
    response_body = data_analysis(response_body)
    text = []
    for line in response_body:
        if isinstance(line, dict) and line["data"]["type"] == "text":
            msg = line["data"].get("msg", '')
            text.append(msg)
    msg_str = ''.join(text)
    steps = response_body
    return {'content': msg_str, 'steps': steps}

def stream_data(response):
    """流式输出结果处理"""
    with response as resp:
        msg = []
        for line in resp.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                msg.append(line_str)
    return chat_response(msg)
#
#
if __name__ == '__main__':
    data = CaseData()
    print(data.case_data)




# # -*- coding: utf-8 -*-
# '''params_utils 参数解析工具类
# '''
# # 2021/3/4 10:41  awlxia
# # 导入settings配置文件
# import settings
# from testbase.conf import settings
# import copy
# import json
#
# try:
#     from testbase import logger
# except:
#     import logging as logger
# import os
# import sys
#
# sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "./")))
# sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../")))
# # 项目路径
# pathProject = os.path.dirname(os.path.dirname(__file__))[:-20]
#
# from luodiyeinterfacelib.config.Dynamic_Function import Dynamic_Function
# from settings import PARAMS_CONFIG
#
#
# def get_path(path_name, file="resources"):
#     path_name = os.path.join(file, path_name)
#     file_name = os.path.join(pathProject, path_name)
#     return file_name
#
#
# def get_json(file_name):
#     with open(file_name, 'r', encoding="utf-8") as f:
#         data = json.load(f)
#     return data
#
#
# def file_name_walk(file_dir):
#     for root, dirs, files in os.walk(file_dir):
#         # print("root", root)  # 当前目录路径
#         root = root.split(file_dir)[1].strip(r"\\")
#         # print("dirs", dirs)  # 当前路径下所有子目录
#         # print("files", files)  # 当前路径下所有非目录子文件
#         if files:
#             for file in files:
#                 if os.path.splitext(file)[1] == '.json':
#                     # print(file)
#                     path = os.path.join(root, file)
#                     path = path.replace("\\", "/")
#                     yield path
#                     # if path not in not_run_case:
#                     #     yield path
#                     # else:
#                     #     pass
#
#
# def find_single_file(file_dir):
#     """
#     查找项目下指定第二层文件(获得Params下的所有json文件)
#     """
#     for root, dirs, files in os.walk(file_dir):
#         for file in files:
#             if ".json" in file:
#                 yield file
#             else:
#                 pass
#
#
# # 递归
# def unest(data, key):
#     if key in data.keys():
#         return data.get(key)
#     else:
#         for dkey in data.keys():
#             if isinstance(data.get(dkey), dict):
#                 return unest(data.get(dkey), key)
#             else:
#                 continue
#
#
# # 获取data中的值
# def resolve(obj, attrspec):
#     for attr in attrspec.split("."):
#         try:
#             if isinstance(obj, list):
#                 obj = obj[0][attr]
#             else:
#                 obj = obj[attr]
#             # print("obj:",obj,", type(obj):",type(obj))
#             # print("attr:",attr,", type(attr):",type(attr))
#         except (TypeError, KeyError):
#             obj = getattr(obj, attr)
#     return obj
#
#
# # trans_ 前缀上下文替换替换
# def trans_to_value(key1, value1, value_map, casedata):
#     if isinstance(value1, str) and "trans_" in value1:  # "spuId": "trans_9(spuId.aaa)"
#         splits = value1.split("rans_")
#         splits[1] = value1[
#                     len(splits[0]) + 5:len(value1)]  # 兼容trans_1(data.order_id)上下文替换key带有下划线特殊字符失败问题
#         splits2 = splits[1].split("(")
#         i = splits2[0]  # 9
#         key_names = splits2[1].split(")")[0]  # spuId.aaa
#         for temp2, step2 in enumerate(casedata["steps"]):
#             if temp2 == int(i):
#                 pre_value = step2
#                 break
#         pre_response = pre_value["response"]
#         replace_value = resolve(json.loads(pre_response), key_names)
#         old_value = "trans_" + splits[1].split(")")[0] + ")"
#         value_map[key1] = value1.replace(old_value, str(replace_value))
#         value1 = value_map[key1]
#         # print("value_map[key1]:{}", value_map[key1])
#     elif isinstance(value1, list) and "trans_" in str(value1):  # 兼容请求参数为list格式
#         for temp, step in enumerate(value1):
#             step_str = str(step)
#             # print(step)
#             splits = step_str.split("rans_")
#             # print(splits)
#             splits[1] = step_str[len(splits[0]) + 5:len(step_str)]  # 兼容trans_1(data.order_id)上下文替换key带有下划线特殊字符失败问题
#             splits2 = splits[1].split("(")
#             # print("splits2:{}",splits2)
#             i = splits2[0]  # 9
#             key_names = splits2[1].split(")")[0]  # spuId.aaa
#             for temp2, step2 in enumerate(casedata["steps"]):
#                 if temp2 == int(i):
#                     pre_value = step2
#                     break
#             pre_response = pre_value["response"]
#             replace_value = resolve(json.loads(pre_response), key_names)
#             old_value = "trans_" + splits[1].split(")")[0] + ")"
#             step_str = step_str.replace(old_value, str(replace_value))
#             value1[temp] = eval(step_str)
#     return value1
#
#
# # settings中配置参数替换
# def setting_to_value(key1, value1, value_map):
#     if isinstance(value1, str) and "${" in value1 and "}" in value1:  # "username": "${username}"
#         count_keys = value1.count("${")
#         # print(count)
#         # if "method_fengyeSignature[${Appsecret},get,/fyec/v1/erp/listorder,f_id=gdt_${uid}&app_key=${Appkey}&startTime=20201211&endTime=method_currentFormatTime(%Y%m%d)]" == value1:
#         #     print("value1:{}",value1)
#         for i in range(count_keys):  # 兼容多个替换参数问题
#             splits = value1.split("{")
#             splits2 = splits[1].split("}")
#             keyName = splits2[0]
#             replace_value = PARAMS_CONFIG.get(keyName)
#             old_value = "${" + keyName + "}"
#             value_map[key1] = value1.replace(old_value, str(replace_value))
#             value1 = value_map[key1]
#             # print("i:",i,",value_map[key1]:",value_map[key1])
#     return value1
#
#
# # method动态加载，替换参数
# def method_to_value(key1, value1, value_map):
#     if isinstance(value1, str) and "method_" in value1:  # 使用参考 method_currentTime[%Y%m%d] ，参数可以不填
#         count_method = value1.count("method_")
#         # print("value1", value1)
#         # print("count_method", count_method)
#         for i in range(count_method):  # 兼容多个替换参数问题
#             split_method = value1.split("(")
#             if len(split_method) < 2:
#                 return
#             split_params = split_method[len(split_method) - 1].split(")")[0]
#             params = split_params.split(",")  # 新增支持参数配置 2021-06-10
#             # print("split_method:", split_method)
#             # print("params", params)
#             method_name = "method_" + split_method[len(split_method) - 2].split("method_")[1]
#             replace_value = getattr(Dynamic_Function(), method_name)(*params)
#             # print("replace_value", replace_value)
#             old_value = method_name + "(" + split_params + ")"
#             value_map[key1] = value1.replace(old_value, str(replace_value))
#             value1 = value_map[key1]
#             # print("value_map[key1]", value_map[key1])
#             # pass
#     return value1
#
#
# # assertions 上下文替换参数
# def assertions_to_value(key1, value1, value_map, casedata):
#     if isinstance(value1, str):  # "spuId": "trans_9(spuId.aaa)"
#         if "trans_" in value1:
#             splits = value1.split("rans_")
#             splits[1] = value1[
#                         len(splits[0]) + 5:len(value1)]  # 兼容trans_1(data.order_id)上下文替换key带有下划线特殊字符失败问题
#             splits2 = splits[1].split("(")
#             i = splits2[0]  # 9
#             key_names = splits2[1].split(")")[0]  # spuId.aaa
#             for temp2, step2 in enumerate(casedata["steps"]):
#                 if temp2 == int(i):
#                     pre_value = step2
#                     break
#             pre_response = pre_value["response"]
#             replace_value = resolve(json.loads(pre_response), key_names)
#             old_value = "trans_" + splits[1].split(")")[0] + ")"
#             value_map[key1] = value1.replace(old_value, str(replace_value))
#             value1 = value_map[key1]
#             # print("value_map[key1]:{}", value_map[key1])
#         elif "${" in value1 and "}" in value1:
#             # print('----------------str', value1)
#             setting_to_value(key1, value1, value_map)
#
#     elif isinstance(value1, list):  # 兼容请求参数为list格式
#         if "trans_" in str(value1):
#             for temp, step in enumerate(value1):
#                 step_str = str(step)
#                 # print(step)
#                 splits = step_str.split("rans_")
#                 # print(splits)
#                 splits[1] = step_str[len(splits[0]) + 5:len(step_str)]  # 兼容trans_1(data.order_id)上下文替换key带有下划线特殊字符失败问题
#                 splits2 = splits[1].split("(")
#                 # print("splits2:{}",splits2)
#                 i = splits2[0]  # 9
#                 key_names = splits2[1].split(")")[0]  # spuId.aaa
#                 for temp2, step2 in enumerate(casedata["steps"]):
#                     if temp2 == int(i):
#                         pre_value = step2
#                         break
#                 pre_response = pre_value["response"]
#                 replace_value = resolve(json.loads(pre_response), key_names)
#                 old_value = "trans_" + splits[1].split(")")[0] + ")"
#                 step_str = step_str.replace(old_value, str(replace_value))
#                 value1[temp] = eval(step_str)
#         elif "${" in str(value1) and "}" in str(value1):
#             # print('-----------------list',value1)
#             for temp, step in enumerate(value1):
#                 value2 = value1[temp]
#                 # print("-------------value2:",value2)
#                 assertions_to_value(None, value2, value2, casedata)
#
#     elif isinstance(value1, dict):  # 兼容请求参数为list格式
#         # print("key1:",key1)
#         # print("value1:",value1)
#         # print("value_map:",value_map)
#         if "trans_" in str(value1):
#             for key, value in value1.items():
#                 if type(value) is int:  # fix bug :TypeError: 'int' object is not iterable
#                     return
#                 for temp, step in enumerate(value):
#                     step_str = str(step)
#                     # print(step)
#                     splits = step_str.split("rans_")
#                     # print(splits)
#                     splits[1] = step_str[len(splits[0]) + 5:len(step_str)]  # 兼容trans_1(data.order_id)上下文替换key带有下划线特殊字符失败问题
#                     splits2 = splits[1].split("(")
#                     # print("splits2:{}",splits2)
#                     i = splits2[0]  # 9
#                     key_names = splits2[1].split(")")[0]  # spuId.aaa
#                     for temp2, step2 in enumerate(casedata["steps"]):
#                         if temp2 == int(i):
#                             pre_value = step2
#                             break
#                     pre_response = pre_value["response"]
#                     replace_value = resolve(json.loads(pre_response), key_names)
#                     old_value = "trans_" + splits[1].split(")")[0] + ")"
#                     step_str = step_str.replace(old_value, str(replace_value))
#                     value[temp] = eval(step_str)
#         elif "${" in str(value1) and "}" in str(value1):
#             # print('-----------------dict', value1)
#             for key2, value2 in value1.items():
#                 assertions_to_value(key2, value2, value1, casedata)
#                 # setting_to_value(key2, value2, value1)
#     return value1
#
#
# class params_utils(object):
#     def __init__(self, CASE=[]):
#         self.CASE = CASE
#
#     # 整合resources下的用例文件
#     def compressionResource(self, file="resources", path_name=""):
#         data_all = {}
#         num = 0
#         for path in file_name_walk(get_path(file=file, path_name=path_name)):
#             try:
#                 if not self.CASE or self.CASE == True:
#                     # print(f"开始整合用例文件：{path}")
#                     data = get_json(get_path(path_name=path, file=file))
#                     data_all.update(data)
#                     num += 1
#                 else:
#                     if path in self.CASE:
#                         # print(f"开始整合用例文件：{path}")
#                         data = get_json(get_path(path_name=path, file=file))
#                         data_all.update(data)
#                         num += 1
#             except Exception:
#                 logger.error(f"警告：没有找到用例文件：{path}")
#         # print(f"运行结束，本次整合json文件：{num}个")
#         return data_all
#
#     # 获取前置参数替换
#     def get_replace_value(self, index, key, casedata):
#
#         for temp, step in enumerate(casedata["steps"]):
#             if temp == index:
#                 value_map = step[key]
#                 # print(type(value))
#                 if key == "method":
#                     if "trans_" in value_map:
#                         splits = value_map.split("_")
#                         splits2 = splits[1].split("(")
#                         i = splits2[0]  # 9
#                         key_names = splits2[1].split(")")[0]  # spuId.aaa
#                         for temp2, step2 in enumerate(casedata["steps"]):
#                             if temp2 == int(i):
#                                 pre_value = step2
#                                 break
#                         pre_response = pre_value["response"]
#                         replace_value = resolve(json.loads(pre_response), key_names)
#                         old_value = "trans_" + splits[1].split(")")[0] + ")"
#                         value_map = value_map.replace(old_value, str(replace_value))
#                         step[key] = value_map
#                     if "${" in value_map and "}" in value_map:  # "username": "${username}"
#                         splits = value_map.split("{")
#                         splits2 = splits[1].split("}")
#                         keyName = splits2[0]
#                         replace_value = PARAMS_CONFIG.get(keyName)
#                         old_value = "${" + keyName + "}"
#                         value_map = value_map.replace(old_value, str(replace_value))
#                         step[key] = value_map
#                     return value_map
#
#                 for key1, value1 in value_map.items():
#                     value1 = trans_to_value(key1, value1, value_map, casedata)
#                     value1 = setting_to_value(key1, value1, value_map)
#                     value1 = method_to_value(key1, value1, value_map)
#                     value1 = assertions_to_value(key1, value1, value_map, casedata)



#


