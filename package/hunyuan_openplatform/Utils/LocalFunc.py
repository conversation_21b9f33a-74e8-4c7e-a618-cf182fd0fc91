#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/9/13
# <AUTHOR> jam<PERSON><PERSON>
# @File    : update_file.py
# @Desc    :
import logging
import os
import sys

from hunyuan_openplatform.Utils.HttpUtils import http_request
from hunyuan_openplatform.config import host_yuan<PERSON>, host_y<PERSON><PERSON>
from datetime import datetime
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from qcloud_cos.cos_exception import CosClientError, CosServiceError  # 安装sdk：pip install -U cos-python-sdk-v5


def goto_cos(secret_id, secret_key, token, region, bucketName, LocalFilePath, key):
    """上传文件到腾讯云cos"""
    # 正常情况日志级别使用 INFO，需要定位时可以修改为 DEBUG，此时 SDK 会打印和服务端的通信信息
    logging.basicConfig(level=logging.ERROR, stream=sys.stdout)


    # 1  设置用户属性, 包括 secret_id, secret_key, region 等。Appid 已在 CosConfig 中移除，请在参数 Bucket 中带上 Appid。Bucket 由BucketName-Appid 组成
    secret_id = secret_id
    secret_key = secret_key
    region = region     # 替换为用户的 region，已创建桶归属的 region 可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
                               # COS 支持的所有 region 列表参见 https://cloud.tencent.com/document/product/436/6224
    token = token               # 如果使用永久密钥不需要填入 token，如果使用临时密钥需要填入，临时密钥生成和使用指引参见 https://cloud.tencent.com/document/product/436/14048
    scheme = 'https'           # 指定使用 http/https 协议来访问 COS，默认为 https，可不填


    config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
    client = CosS3Client(config)


    # 使用高级接口上传一次，不重试，此时没有使用断点续传的功能
    response = client.upload_file(
        Bucket=bucketName,
        Key=key,
        LocalFilePath=LocalFilePath,
        EnableMD5=False,
        progress_callback=None
    )
    print(response)


    # 使用高级接口断点续传，失败重试时不会上传已成功的分块(这里重试10次)
    for i in range(0, 10):
        try:
            response = client.upload_file(
            Bucket=bucketName,
            Key=key,
            LocalFilePath=LocalFilePath)
            break
        except CosClientError or CosServiceError as e:
            print(e)


class LocalFunction:
    """本地方法"""
    def getUploadInfo(self, **kwargs):
        """获取图片上传的路径和临时密钥"""
        filename = kwargs.get('filename')
        ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))
        filepath = os.path.join(ROOT_DIR, f'files/{filename}')
        logging.info(f'文件路径：{filepath}')
        url = '/api/agent/resource/gen_upload_info'
        accessType = kwargs.get('accessType', "private")
        payload = {"fileNames":[filename],"accessType":accessType}
        res = http_request(url=url, method="POST", payload=payload, params={}, source="yuanqi")
        # print(res)
        key = res.json()["resourceInfos"][0]['location']  # 对象键
        secret_id = res.json()['encryptTmpSecretId']
        secret_key = res.json()['encryptTmpSecretKey']
        token = res.json()['encryptToken']
        region = res.json()['region']
        bucketName = res.json()['bucketName']
        goto_cos(secret_id=secret_id, secret_key=secret_key, token=token, region=region, bucketName=bucketName, LocalFilePath=filepath, key=key)
        logging.info(f"文件上传接口：{res.text}")
        return res


def resource_download(resourceId):
    url = host_yuanbao + f'/api/resource/download?resourceId={resourceId}'
    payload = {}
    res = http_request(url=url, method="POST", payload=payload, params={}, source="yuanqi")
    return res



#
if __name__ == '__main__':
    # ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))
    filename = 'Locust.docx'
    # filepath = os.path.join(ROOT_DIR, f'common/{filename}')
    # print(filename)
    res = getUploadInfo(filename=filename)
    print(res.json())