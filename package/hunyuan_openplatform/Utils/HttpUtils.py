#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/8
# <AUTHOR> jam<PERSON><PERSON>
# @File    : Http_Utils.py
# @Desc    :

import logging
import os
import sys
import time
import random

import requests
from hunyuan_openplatform.Utils.Auth import get_authorization
from hunyuan_openplatform.config import host_yuan<PERSON>, host_yuanbao, testcase_config


def _request(Host, url, method, payload, params, source, stream=False):
    """
    统一请求入口
    :param source:
    :param params:
    :param url: 完整的请求url，包含域名
    :param payload: GET请求传{}，POST请求传请求body即可
    :param method: 请求方式GET/POST
    :param stream: 是否是流式接口，默认False
    :return: 对于流式接口（主要是chat/repeat），返回的是处理后的内容，非流式接口返回的原始response对象
    """
    nonce = random.randint(1000000000, 9999999999)
    timestamp = int(time.time())
    authorization = get_authorization(Host, payload, nonce, method)
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json; charset=utf-8',
        'Host': Host,
        'X-TC-Timestamp': str(timestamp),
        'X-TC-Nonce': str(nonce),

    }
    url = "https://" + Host + url
    # if stream is True:
    #     with requests.post(url, headers=headers, json=payload, stream=True) as resp:
    #         msg = []
    #         for line in resp.iter_lines():
    #             if line:
    #                 msg.append(line)
    logging.info(f"请求参数：Request_URL: {url} || Request_params: {params} || Request_payload: {payload}")
    response = requests.request(method, url, headers=headers, json=payload, params=params, stream=stream)
    logging.info(f"X-Traceid: {response.headers['X-Traceid']}")
    logging.info(f'返回参数：Response_status:{response.status_code} || Response_body:{response.content.decode("utf-8")}')
    return response


def openApi_request(Host, url, method, payload, params):
    token = testcase_config["openapi_token_agentId"]
    headers = {
        'X-Source': 'openapi',
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
    url = "https://" + Host + url
    logging.info(f"请求参数：Request_URL: {url} || Request_params: {params} || Request_payload: {payload}")
    response = requests.request(method, url, headers=headers, json=payload, params=params)
    logging.info(f'返回参数：Response_status:{response.status_code} || Response_body:{response.content.decode("utf-8")}')
    return response


def http_request(url, method, payload, params, source, stream=False):
    if source == "yuanqi":
        Host = host_yuanqi
        response = _request(Host, url, method, payload, params, source, stream)
    elif source == "yuanbao":
        Host = host_yuanbao
        response = _request(Host, url, method, payload, params, source, stream)
    elif source == "openAPI":
        Host = host_yuanqi
        response = openApi_request(Host, url, method, payload, params)
    else:
        raise Exception("source参数错误")
    return response


if __name__ == "__main__":

    url = "/openapi/v1/agent/chat/completions"
    method = "POST"
    payload = {
        "assistant_id": "nUg94nX9yeQd",
        "user_id": "jamiezhen",
        "stream": True,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "巴黎奥运会最新资讯"
                    }
                ]
            }
        ]
    }
    params = {}
    source = "openapi"
    response = http_request(url, method, payload, params, source, stream=True)
    print(response.content.decode("utf-8"))
    # url = "/api/agent/store/plugin/list"
    # method = "GET"
    # params = {"category": "category13"}
    # payload = {}
    # source = "yuanqi"
    # response = http_request(url, method, payload, params, source)
    # print(response.json())
    # print(response.headers)

    # url = "/api/agent/personal/plugin/list"
    # method = "GET"
    # payload = {}
    # params = {'status': 'draft'}
    # source = "yuanqi"
    # # params = urlencode(params)
    # # url = f"{url}?{params}"
    # response = http_request(url, method, payload, params, source)
    # print(response.content.decode("utf-8"))

    # url = "/api/agent/personal/knowledge/list"
    # method = "GET"
    # payload = {}
    # params = {'limit': 20}
    # source = "yuanqi"
    # response = http_request(url, method, payload, params, source)
    # print(response.text)

    # url = "/api/agent/personal/plugin/detail"
    # method = "GET"
    # payload = {}
    # params = {'pluginId': 'zzQo1QPjOgBv'}
    # source = "yuanqi"
    # response = http_request(url, method, payload, params, source)
    # print(response.content.decode("utf-8"))

    # url = "/api/agent/personal/agent/list"
    # host = host_yuanqi
    # method = "GET"
    # params = {"pageNum": 1}
    # payload = {}
    # print(url, method, params)
    # response = http_request(url, method, payload, params, "yuanqi")
    # print(response)

