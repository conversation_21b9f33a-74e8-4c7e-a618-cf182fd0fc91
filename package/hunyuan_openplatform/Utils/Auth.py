#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/8
# <AUTHOR> jam<PERSON><PERSON>
# @File    : Auth.py
# @Desc    :

# -*- coding: utf-8 -*-
import hashlib
import hmac
import json
import time
from datetime import datetime
from hunyuan_openplatform.config import secret_id, secret_key


# 密钥参数
# 需要设置环境变量 TENCENTCLOUD_SECRET_ID，值为示例的 AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******
# secret_id = "AKIDz8krbsJ5yKBZQpn74WFkmLPx3*******"
# 需要设置环境变量 TENCENTCLOUD_SECRET_KEY，值为示例的 Gu5t9xGARNpq86cd98joQYCN3*******
# secret_key = "Gu5t9xGARNpq86cd98joQYCN3*******"
# host = "hunyuan.tencent.com"

# ************* 步骤 1: 计算派生签名秘钥
# 计算签名摘要函数


def sign(key, msg):
    return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()


def get_signature(secret_key):
    service = 'amai'
    timestamp = int(time.time())
    date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")
    secret_date = sign(("TC3" + secret_key).encode("utf-8"), date)
    secret_service = sign(secret_date, service)
    secret_signing = sign(secret_service, "tc3_request")
    return secret_signing


def get_authorization(host, payload, nonce, http_request_method, canonical_querystring=''):
    """签名函数"""
    # ************* 步骤 2：拼接规范请求串 *************
    algorithm = "TC3-HMAC-SHA256"
    service = 'amai'
    ct = "application/json; charset=utf-8"
    signed_headers = "content-type;host"
    canonical_headers = "content-type:%s\nhost:%s\n" % (ct, host)
    timestamp = int(time.time())
    date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")
    if payload != '':
        payload = json.dumps(payload)
    hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
    canonical_request = (http_request_method + "\n" +
                         "/" + "\n" +
                         canonical_querystring + "\n" +
                         canonical_headers + "\n" +
                         signed_headers + "\n" +
                         hashed_request_payload)
    # ************* 步骤 3：拼接待签名字符串 *************
    hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
    string_to_sign = (algorithm + "\n" +
                      str(timestamp) + "\n" +
                      str(nonce) + "\n" +
                      date + "/" + service + "/" + "tc3_request" + "\n" +
                      hashed_canonical_request)
    secret_signing = get_signature(secret_key)
    # ************* 步骤 4：计算签名 *************
    signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

    # ************* 步骤 5：拼接 Authorization *************
    authorization = (algorithm + " " +
                     "Credential=" + secret_id + "/" + date + "/" + service + "/" + "tc3_request" + ", " +
                     "SignedHeaders=" + signed_headers + ", " +
                     "Signature=" + signature)
    return authorization

