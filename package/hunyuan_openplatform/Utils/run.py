import os
import time


if __name__ == "__main__":
    ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))
    currentTime = time.strftime("%Y%m%d%H%M%S", time.localtime())
    report_name = 'result/混元助手接口测试报告' + currentTime + '.html'
    report_path = os.path.join(os.path.dirname(ROOT_DIR), report_name)
    test_case_path = os.path.join(ROOT_DIR, 'api_test/')
    os.system('pytest ' + test_case_path + ' --html=' + report_path + ' --self-contained-html')

