#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/6/2
# <AUTHOR> jam<PERSON><PERSON>
# @File    : AssertUtils.py
# @Desc    : 断言相关方法
import json
import logging
import re


def match_strings(pattern, string):
    """
    正则校验
    """
    regex = re.compile(pattern)
    if regex.match(string):
        return True
    else:
        return False


def assertFun(_type, value):
    """字段类型断言"""
    if _type == "assert_str":
        return isinstance(value, str)
    elif _type == "assert_list":
        return isinstance(value, list)
    elif _type == "assert_dict":
        return isinstance(value, dict)
    elif _type == "assert_int":
        return isinstance(value, int)
    elif _type == "assert_float":
        return isinstance(value, float)
    elif _type == "assert_bool":
        return isinstance(value, bool)
    elif _type == "assert_number":
        return isinstance(value, (int, float))


def assertRunner(expected_data, response_body):
    """
    json结果断言
    对预期结果中每个字段与实际返回进行比较断言，
    如果实际结果中某个字段为assert_开头，则对
    该字段值进行类型断言
    """
    for key, value in expected_data.items():
        # 如果是字典，且字典不为空，递归比较字典
        if isinstance(value, dict) and value != {}:
            try:
                assertRunner(value, response_body[key])
            except Exception as e:
                raise AssertionError(e)
        # 如果是列表，且列表不为空，判断列表元素类型
        elif isinstance(value, list) and len(value) > 0:
            assert not len(value) > len(response_body[key]), f"{key}字段列表长度，预期值大于实际列表长度: {len(value)} > {len(response_body[key])}"
            for index, child_value in enumerate(value):
                if isinstance(child_value, dict) and value != {}:
                    assertRunner(child_value, response_body[key][index])
                elif isinstance(child_value, list):
                    assert len(child_value) > len(response_body[key][index]), f"{key}[{index}]字段列表长度，预期值大于实际列表长度:: {len(child_value)} > {len(response_body[key][index])}"
                    for i, v in enumerate(child_value):
                        if isinstance(v, str) and v.startswith("assert_"):
                            assert assertFun(v, response_body[key][index][i]), f"{key}[{index}][{i}]的值类型不符合预期: {type(response_body[key][index][i])} != {v.split('_')[1]}"
                        else:
                            assert v == response_body[key][index][i], f"{key}[{index}][{i}]的值: '{response_body[key][index][i]} != {v}'"
                else:
                    if isinstance(child_value, str) and child_value.startswith("assert_"):
                        assert assertFun(child_value, response_body[key][index]), f"{key}[{index}]的值类型不符合预期: {type(response_body[key][index])} != {child_value.split('_')[1]}"
                    else:
                        assert child_value == response_body[key][index], f"{key}[{index}]的值: '{response_body[key][index]}' != '{child_value}'"
        else:
            assert key in response_body.keys(), f"{key}字段不在返回参数中"
            if isinstance(value, str) and value.startswith("assert_"):
                assert assertFun(value, response_body[key]), f"{key}的值类型不符合预期: {type(response_body[key])} != {value.split('_')[1]}"
            else:
                assert value == response_body[key], f"「{key}」字段的预期是：{value}, 实际返回是：{response_body[key]} "


def assert_stream(expected_data, response_body):
    """
    chat接口流式输出断言
    """
    textNum = 0
    for index, value in enumerate(response_body):

        if isinstance(value, dict):
            if value["data"]["type"] == "text" and "msg" in value["data"]:
                if "index" not in value["data"]:  # 排除文本消息
                    # logging.info(f"实际返回第{index}条数据：{value}， 此条消息为模型回答消息，跳过断言")
                    textNum += 1
                    continue
            else:
                logging.info(f"实际返回第{index}条数据：{value}， 预期结果中第{index-textNum}条数据：{expected_data[index-textNum]}")
                # if 1< index < 5:
                #     textNum += 1
                #     continue

                assertRunner(expected_data[index-textNum], value)
        if isinstance(value, str):
            logging.info(
                f"实际返回第{index}条数据：{value}， 预期结果中第{index - textNum}条数据：{expected_data[index - textNum]}")
            if index == 3 and isinstance(expected_data[index-textNum], dict):
                del expected_data[index-textNum]
            assert match_strings(expected_data[index-textNum], value), f"正则匹配不通过: 预期值：{expected_data[index-textNum]}， 实际返回值：{value}"




if __name__ == '__main__':
    # expected = {
    #     "agentId": "assert_str",
    #     "name": "哈哈哈-哦哦哦",
    #     "description": "充满活力和幽默感，用大笑和惊讶表情传递快乐，轻松愉快地与人交流。",
    #     "rule": "- 你是一个充满活力和幽默感的人，喜欢用大笑和惊讶的表情来表达自己的情感。\n- 你总是能用轻松愉快的方式与人交流，让人感到舒适和愉快。\n- 你会在回复之前用(哈哈哈)和(哦哦哦)等表情符号来表达自己的心情，增加对话的趣味性。",
    #     "logo": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/4bac2623c92f59dce09c3157b783ae19.png",
    #     "suggestionEnable": False,
    #     "notice": "嘿，朋友！看来你今天心情不错嘛，模仿着音乐的旋律打招呼呢！(哈哈哈)有什么好玩的事儿没？一起乐呵乐呵呀！",
    #     "samplePrompts": [
    #         "今天天气真好，阳光明媚，你心情也跟着变好了呢！",
    #         "听到你这么说，我都忍不住想跟着你一起唱首歌了！",
    #         "哈哈，你真是个开心果！我今天也遇到了件超有趣的事，想听吗？"
    #     ],
    #     "toolIds": None,
    #     "tools": None,
    #     "knowledgeIds": None,
    #     "workflowIds": None,
    #     "workflows": None,
    #     "author": "Jamie(镇咸杰)",
    #     "authorUserId": "jamiezhen",
    #     "category": "",
    #     "publishPlatform": [
    #         {"yuanbaoapp":[1, 2, 4]}
    #     ],
    #     "createdAt": "2024-06-11 17:50",
    #     "updatedAt": "2024-06-11 17:50",
    #     "publishedAt": "2024-06-11 17:50",
    #     "status": "published",
    #     "tone": {
    #         "toneId": "604162",
    #         "source": 3,
    #         "toneName": "温柔静静"
    #     },
    #     "digitalHumanId": "",
    #     "visibleRange": "private",
    #     "draft": None,
    #     "usage": 0,
    #     "convCount": 0,
    #     "collections": 0,
    #     "hasPublishedHistory": None,
    #     "model": {
    #         "manufacturer": "Hunyuan",
    #         "manufacturerUI": "混元",
    #         "modelName": "hunyuan",
    #         "modelNameUI": "腾讯混元大模型-32k",
    #         "logo": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/logo/hunyuan.png",
    #         "modelVersion": "",
    #         "maxTokens": 4096,
    #         "contextRounds": 16,
    #         "temperature": 0.5,
    #         "topP": 1
    #     },
    #     "mediaType": "",
    #     "error": {
    #     }
    # }
    #
    # request_body = {
    #     "agentId": "xTJqjXZdOcdq",
    #
    #     "description": "充满活力和幽默感，用大笑和惊讶表情传递快乐，轻松愉快地与人交流。",
    #     "rule": "- 你是一个充满活力和幽默感的人，喜欢用大笑和惊讶的表情来表达自己的情感。\n- 你总是能用轻松愉快的方式与人交流，让人感到舒适和愉快。\n- 你会在回复之前用(哈哈哈)和(哦哦哦)等表情符号来表达自己的心情，增加对话的趣味性。",
    #     "logo": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/4bac2623c92f59dce09c3157b783ae19.png",
    #     "suggestionEnable": False,
    #     "notice": "嘿，朋友！看来你今天心情不错嘛，模仿着音乐的旋律打招呼呢！(哈哈哈)有什么好玩的事儿没？一起乐呵乐呵呀！",
    #     "samplePrompts": [
    #         "今天天气真好，阳光明媚，你心情也跟着变好了呢！",
    #         "听到你这么说，我都忍不住想跟着你一起唱首歌了！"
    #     ],
    #     "toolIds": None,
    #     "tools": None,
    #     "knowledgeIds": None,
    #     "workflowIds": None,
    #     "workflows": None,
    #     "author": "Jamie(镇咸杰)",
    #     "authorUserId": "jamiezhen",
    #     "category": "",
    #     "publishPlatform": [
    #         {"yuanbaoapp":[1, 2, 4]}
    #     ],
    #     "createdAt": "2024-06-11 17:50",
    #     "updatedAt": "2024-06-11 17:50",
    #     "publishedAt": "2024-06-11 17:50",
    #     "status": "published",
    #     "tone": {
    #         "toneId": "604162",
    #         "source": 3,
    #         "toneName": "温柔静静"
    #     },
    #     "digitalHumanId": "",
    #     "visibleRange": "private",
    #     "draft": None,
    #     "usage": 0,
    #     "convCount": 0,
    #     "collections": 0,
    #     "hasPublishedHistory": None,
    #     "model": {
    #         "manufacturer": "Hunyuan",
    #         "manufacturerUI": "混元",
    #         "modelName": "hunyuan",
    #         "modelNameUI": "腾讯混元大模型-32k",
    #         "logo": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/hunyuan_open/agentlogo/logo/hunyuan.png",
    #         "modelVersion": "",
    #         "maxTokens": 4096,
    #         "contextRounds": 16,
    #         "temperature": 0.5,
    #         "topP": 1
    #     },
    #     "mediaType": "",
    #     "error": {}
    # }
    # assertRunner(expected, request_body)
    response = ['data: {"type":"text"}', 'event: speech_type', 'data: status', 'event: speech_type', 'data: text', 'data: {"type":"text","msg":"你好"}', 'data: {"type":"text","msg":"！"}', 'data: {"type":"text","msg":"有什么"}', 'data: {"type":"text","msg":"我可以"}', 'data: {"type":"text","msg":"帮助"}', 'data: {"type":"text","msg":"你的"}', 'data: {"type":"text","msg":"吗"}', 'data: {"type":"text","msg":"？"}', 'data: {"type":"usage","prompt_tokens":20,"completion_tokens":8,"total_tokens":28,"total_cost":1.8}', 'data: [plugin: ]', 'data: {"type":"meta","messageId":"d3f9a87a-c75c-4923-9af0-c1ea71089f92_6","index":6,"replyId":"d3f9a87a-c75c-4923-9af0-c1ea71089f92_5","replyIndex":5,"traceId":"b82758a1cd2ea332d8b696e01164adae","guideId":0,"unSupportRepeat":false}', 'data: [TRACEID:b82758a1cd2ea332d8b696e01164adae]', 'data: [DONE]']
    print(response)