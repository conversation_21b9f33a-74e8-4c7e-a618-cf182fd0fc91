#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/5/7
# <AUTHOR> jam<PERSON><PERSON>
# @File    : main_test.py
# @Desc    : 主用例

import json
import logging
import os
import sys
import time

import pytest
from urllib.parse import urlencode
ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.dirname(os.path.abspath(ROOT_DIR)))
from hunyuan_openplatform.config import testcase_config, chat_api, special_api
from hunyuan_openplatform.Utils.ParamsUtils import CaseData, ApiData, update_config, get_replace_value, stream_data, chat_response, data_analysis
from hunyuan_openplatform.Utils.HttpUtils import http_request
from hunyuan_openplatform.Utils.AssertUtils import *
from hunyuan_openplatform.Utils.LocalFunc import LocalFunction

local_function = LocalFunction()
data = CaseData()
api = ApiData()
case_data = data.case_data
api_data = api.api_data


@pytest.mark.parametrize('case_data', case_data, ids=data.case_ids)
def test_main(case_data):
    """测试用例"""
    # 替换配置文件中的参数
    test_main.__doc__ = case_data["case_title"]
    case_data = update_config(case_data, config_data=testcase_config)
    for index, step in enumerate(case_data["steps"]):
        logging.info((f"—————————————————————————————————————————————— 步骤 {index}: {step['stepname']} ——————————————————————————————————————————————"))
        callfunname = step["callfunname"]
        stream = step.get("stream", False)
        request = get_replace_value(index, "request", case_data)
        payload = {}
        params = {}
        expected = step["assertions"]["response_body"]

        # 延时
        if "sleep" in step:
            time.sleep(step["sleep"])
        # 调用本地方法
        if step.get("local_func"):
            response = getattr(local_function, callfunname)(**request)
        # 调用接口请求
        else:
            source = api_data[callfunname].get("source", "yuanqi")
            url = api_data[callfunname]["url"]
            method = api_data[callfunname]["method"]

            if method == "POST":
                payload = request
                if url in chat_api:
                    url = f"{url}/{request['cid']}"
                    del request["cid"]
            else:
                # if url in special_api:  # 部分get接口的请求参数要放到payload中，特殊处理一下
                #     payload = request
                # else:
                params = request
            # 发起请求
            response = http_request(
                url=url,
                method=method,
                payload=payload,
                params=params,
                source=source
            )

        # 断言状态码
        assert response.status_code == step["assertions"]["status_code"]
        # 流式接口断言
        if stream:
            response_body = stream_data(response)
            logging.info(f"智能体对话内容: {response_body['content']}")
            if "content" in expected:
                assert re.search(expected["content"], response_body["content"])
            if "steps" in expected:
                expected_steps = data_analysis(expected["steps"])
                assert_stream(expected_steps, response_body["steps"])
        else:   # 非流式接口断言
            try:
                response_body = response.json()
            except json.decoder.JSONDecodeError:
                response_body = {"content": response.text}
            expect_response_body = get_replace_value(index, "assertions", case_data)
            assertRunner(expect_response_body, response_body)

        # 储存全局变量-该步骤的响应
        case_data["steps"][index]["response"] = response_body


