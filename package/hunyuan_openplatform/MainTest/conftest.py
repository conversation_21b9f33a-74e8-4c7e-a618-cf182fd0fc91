#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/6/5
# <AUTHOR> jam<PERSON><PERSON>
# @File    : conftest.py
# @Desc    :
import logging
import os

import pytest
import datetime
from py.xml import html
import sys
import requests

ROOT_DIR = os.path.dirname(os.path.abspath(os.path.dirname(os.path.abspath(__file__))))
print(ROOT_DIR)
sys.path.append(os.path.dirname(os.path.abspath(ROOT_DIR)))
from package.hunyuan_openplatform.config import host_yuanqi


def pytest_html_report_title(report):
    report.title = "腾讯元器接口测试报告"


def pytest_configure(config):
    print("配置文件：", config.__dict__)
    config.option.metadata = {"Start Time": datetime.datetime.now()}
    # config.option.metadata.pop('Packages')
    # config.option.metadata.pop('Plugins')


@pytest.hookimpl(tryfirst=True)
def pytest_sessionfinish(session):
    print(session.config)
    session.config.option.metadata["执行环境"] = host_yuanqi


def pytest_html_results_table_header(cells):
    """修改结果表的表头"""
    cells.insert(1, html.th("Description", class_="shortable desc", col="desc"))
    cells.pop()


def pytest_html_results_table_row(report, cells):
    if hasattr(report, 'description'):
        cells.insert(1, html.th(report.description))
        cells.pop()
    else:
        print(f"Report object does not have 'description' attribute: {report}")
    # cells.insert(1, html.th(report.description))



@pytest.mark.hookwrapper
def pytest_runtest_makereport(item):
    """获取测试结果并修改编码"""
    outcome = yield
    report = outcome.get_result()
    if item.function.__doc__ is None:
        report.description = str(item.function.__name__)  # __doc__是每一个函数的内置属性，默认是这个函数里面的注释
    else:
        report.description = str(item.function.__doc__)
    report.nodeid = report.nodeid.encode("unicode_escape").decode("utf-8")