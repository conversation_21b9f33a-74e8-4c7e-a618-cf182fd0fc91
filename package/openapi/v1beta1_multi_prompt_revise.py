import requests
from datetime import datetime


def v1beta1_multi_prompt_revise(domain, api_key, topic, text, style, n):
    """文转多prompt"""
    url = f'{domain}/openapi/v1beta1/multi/prompt/revise'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-type': 'application/json',
        'env': 'image_intention_test2'
    }
    payload = {}

    if topic is not None:
        payload['topic'] = topic
    if text is not None:
        payload['text'] = text
    if style is not None:
        payload['style'] = style
    if n is not None:
        payload['n'] = n

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created_at = None
        status_code = None
        resp_json = None
        id = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                id = resp_json['id']
            if 'created_at' in resp_json:
                created_at = resp_json['created_at']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    id = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            print(url)
            print(f"{'='*10}resp_json{'='*10}")
            print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created_at': created_at,
        'id': id,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'resp': resp
    }
