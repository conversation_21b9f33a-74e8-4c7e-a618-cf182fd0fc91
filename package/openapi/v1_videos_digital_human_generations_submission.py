import requests
import json
from datetime import datetime


def v1_videos_digital_human_generations_submission(
        domain, api_key, cookie=None, model='hunyuan-video-digital-human', 
        version=None, image=None, image_url=None, audio=None, audio_url=None):
    url = f'{domain}/openapi/v1/videos/digital_human/generations/submission'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload_variables = [
        'model','version','image',
        'image_url', 'audio', 'audio_url'
        ]

    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}


    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print(f'status_code{status_code},resp:{resp.text}')
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'task_id': task_id,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }