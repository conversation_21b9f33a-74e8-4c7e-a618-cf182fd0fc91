import requests
import sseclient
import json
from datetime import datetime

def v1_assistant_thread_runs(domain,api_key,thread_id,additional_messages=None,model=None,stream=True,user=None,cookie=None):
    """发起对话"""
    url = f'{domain}/v1/threads/{thread_id}/runs'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload_variables = ['additional_messages','model','stream','user']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        run_id = None
        resp_thread_id = None
        message_id = None
        assistant_id = None
        error = None
        resp_text = None
        complete_msg = None
        print(f"url:{url}")
        print(f"payload:{payload}")
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=120) as resp:
            status_code = resp.status_code
            if status_code != 200:
                error = resp.json()['error']
            print(f"status code:{status_code}")
            if stream:
                all_event_data=[]
                msg=[]
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    if event.data != '':
                        all_event_data.append(event.data)
                        try:
                            line_json = json.loads(event.data)
                            object = line_json.get('object')
                            if object == 'thread.message.delta':
                                msg.append(line_json['delta']['content'][0]['text']['value'])
                            if object == 'thread.run.step' and line_json['status'] == 'completed':
                                message_id = line_json['step_details']['message_creation']['message_id']
                                run_id = line_json['run_id']
                                assistant_id = line_json['assistant_id']
                                resp_thread_id = line_json['thread_id']
                            if object == 'thread.run' and line_json['status'] == 'completed':
                                complete_msg = line_json
                        except json.decoder.JSONDecodeError:
                            pass
                message_content = ''.join(msg)
                resp_json = all_event_data
                print(resp_json)
                print(f"message_content:{message_content}")
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'json': resp_json,
        'status_code': status_code,
        'run_id': run_id,
        'thread_id': resp_thread_id,
        'message_id': message_id,
        'assistant_id': assistant_id,
        'message_content': message_content,
        'error': error,
        'resp_text': resp_text,
        'complete_msg': complete_msg,
    }
