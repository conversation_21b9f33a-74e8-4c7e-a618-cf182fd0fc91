import requests
import json
from datetime import datetime


def v1_files(domain, api_key, cookie=None, user=None, purpose=None, offset=None, limit=None):
    url = f'{domain}/openapi/v1/files'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload = {}

    if user is not None:
        payload['user'] = user
    if purpose is not None:
        payload['purpose'] = purpose
    if offset is not None:
        payload['offset'] = offset
    if limit is not None:
        payload['limit'] = limit

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        object = None
        total = None
        files = []
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'object' in resp_json:
                object = resp_json['object']
            if 'total' in resp_json:
                total = resp_json['total']
            if 'data' in resp_json and resp_json['data'] is not None :
                for file in resp_json['data']:
                    files.append(file)
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'id': qid,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'object': object,
        'total': total,
        'files': files
    }