from datetime import datetime
import logging

import curlify
import requests
import sseclient
import json
import time


def v1beta3_images_chat_intention(
        domain,
        messages,
        api_key=None,
        authorization=None,
        **kwargs
):
    print("~" * 30 + "v1beta3/images/chat/intention" + "~" * 30)
    url = f'{domain}/openapi/v1beta3/images/chat/intention'

    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-type': 'application/json',
        'env': 'image_style_transfer'
    }

    payload_variables = [
        'moderation', 'n', 'style', 'user'
    ]
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    payload['messages'] = messages
    print(f"请求:{payload}")

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        id = None
        created = None
        intention = None
        content = None
        finish_reason = None
        finish_content = None

        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            print("=" * 30 + "req detail" + "=" * 30)
            print(f"url: {resp.request.url}")
            print(f"status_code: {resp.status_code}")
            print(f"header: {resp.request.headers}")
            print(f"payload: {resp.request.body}")
            print("=" * 30 + "resp detail" + "=" * 30)
            print(f"header: {resp.headers}")
            status_code = resp.status_code
            print(f"status_code: {status_code}")
            resp_json = None
            try:
                resp_json = resp.json()
                print(curlify.to_curl(resp.request))
                if 'id' in resp_json:
                    id = resp_json['id']
                if 'created' in resp_json:
                    created = resp_json['created']
                if 'data' in resp_json:
                    data = resp_json['data']
                    if 'intention' in data:
                        intention = data['intention']
                    if 'content' in data:
                        content = data['content']
                if 'finish_reason' in resp_json:
                    finish_reason = resp_json['finish_reason']
                if 'finish_content' in resp_json:
                    finish_content = resp_json['finish_content']
            except requests.exceptions.JSONDecodeError:
                print("返回:" + resp.text)

        if not end_time:
            end_time = datetime.now()

    return {
        'time_consumption': (end_time - start_time).total_seconds(),
        'status_code': status_code,
        'created': created,
        'json': resp_json,
        'id': id,
        'intention': intention,
        'content': content,
        'finish_reason': finish_reason,
        'finish_content': finish_content
    }