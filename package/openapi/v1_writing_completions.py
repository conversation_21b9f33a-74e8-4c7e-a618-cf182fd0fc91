import requests
import sseclient
import json
from datetime import datetime

def v1_writing_completions(domain, api_key, model, prompt, genre, topic=None, references=None, outline_prompt=None, stream=None, search_info=None,cookie=None):
    """
    查看消息列表
    :param model: 模型名称；目前固定为 hunyuan-writing-tob
    :param prompt: 写作prompt；如果 genre 为 article_outline_to_text、paper_outline_to_text 等基于大纲生成的场景，prompt传入大纲内容，详见下述genre指令介绍
    :param genre: 写作指令
    :param topic: 主题
    :param references: 参考文档
    :param outline_prompt: 大纲prompt；如果 genre 为 article_outline_to_text、paper_outline_to_text 等基于大纲生成的场景，outline_prompt传入生成大纲时的prompt，详见下述genre指令介绍
    :param stream: 是否以流式接口的形式返回数据，默认false
    :param search_info: 默认是false，在值为true且命中搜索时，接口会返回search_info以及相关引用脚标
    :param cookie: cookie
    :return:
    """
    url = f'{domain}/openapi/v1/writing/completions'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie,
        'x-route-env': 'aiwrite'
    }
    payload_variables = ['model', 'prompt', 'genre', 'topic', 'references', 'outline_prompt', 'stream', 'search_info', 'cookie']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        message_content = None
        status_code = None
        resp_json = None
        cid = None
        finish_reason = None
        usage = None
        model = model
        moderation_level = None
        moderation_result = None
        print(f"url:{url}")
        print(f"payload:{payload}")
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            # print(f"status_code: {resp.status_code}")
            status_code = resp.status_code
            # print(f"resp_headers:{resp.headers}")
            if stream:
                all_event_data=[]
                msg=[]
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    if event.data != '':
                        all_event_data.append(event.data)
                        try:
                            line_json = json.loads(event.data)
                            print(line_json)
                            if 'choices' in line_json:
                                if 'content' in line_json['choices'][0]['delta']:
                                    msg.append(line_json['choices'][0]['delta']['content'])
                                if 'finish_reason' in line_json['choices'][0]:
                                    current_finish_reason = line_json['choices'][0]['finish_reason']
                                    if current_finish_reason is not None:
                                        finish_reason = current_finish_reason
                                    moderation_result = line_json['choices'][0]['moderation_result']
                                    usage = line_json['usage']
                                    model = line_json['model']
                                    created = line_json['created']
                            if 'id' in line_json:
                                cid = line_json['id']
                        except json.decoder.JSONDecodeError:
                            pass
                message_content = ''.join(msg)
                resp_json = all_event_data
                # print(f"message_content:{message_content}")
            else:
                # print("resp_text:"+resp.text)
                try:
                    resp_json = resp.json()
                    print(resp_json)
                    if 'id' in resp_json:
                        cid = resp_json['id']
                    if 'created' in resp_json:
                        created = resp_json['created']
                    if 'choices' in resp_json:
                        finish_reason = resp_json['choices'][0]['finish_reason']
                        message_content = resp_json['choices'][0]['message']['content']
                        moderation_result = resp_json['choices'][0]['moderation_result']
                        print(f"message_content:{message_content}")
                    if 'usage' in resp_json:
                        usage = resp_json['usage']
                    if 'model' in resp_json:
                        model = resp_json['model']
                except requests.exceptions.JSONDecodeError:
                    pass
        if not end_time:
            end_time= datetime.now()
    return {
        'id': cid,
        'created': created,
        'time_consumption': (end_time-start_time).total_seconds(),
        'finish_reason': finish_reason,
        'json': resp_json,
        'status_code': status_code,
        'message_content': message_content,
        'usage': usage,
        'model': model,
        'moderation_result': moderation_result,
    }
