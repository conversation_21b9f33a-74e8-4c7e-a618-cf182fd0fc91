import requests
import json
from datetime import datetime


def v1_files_deletions(domain, api_key, cookie=None, file_id=None, user=None):
    url = f'{domain}/openapi/v1/files/deletions'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload = {}

    if file_id is not None:
        payload['file_id'] = file_id
    if user is not None:
        payload['user'] = user

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        deleted = None
        object = None

        print(f"payload:{payload}")
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'deleted' in resp_json:
                deleted = resp_json['deleted']
            if 'object' in resp_json:
                object = resp_json['object']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'id': qid,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'object': object,
        'deleted': deleted
    }