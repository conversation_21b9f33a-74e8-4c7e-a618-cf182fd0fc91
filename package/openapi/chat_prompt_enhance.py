import requests
from datetime import datetime


def openapi_chat_prompt_enhance(domain, api_key, messages, model, cookie=None, authorization=None):
    url = f'{domain}/openapi/chat/prompt/enhance'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json'
        }
    payload = {
        'model': model,
        # 'version': None,
        # 'stream': False,
        # 'enhance': False,
        # 'enhancements': None,
        # 'force_enhancement': None,
        'messages': messages,
        # 'temperature': None,
        # 'top_p': None,
        # 'top_k': None,
        # 'random_seed': None
    }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        finish_reason = None
        enhance_prompt = None
        status_code = None
        resp_json = None
        id = None
        print(f'messages:{messages}')
        with requests.post(url, headers=headers, json=payload, verify=False) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            print(resp_json)
            if 'id' in resp_json:
                id = resp_json['id']
            if 'enhance_prompt' in resp_json:
                enhance_prompt = resp_json['enhance_prompt']
        if not end_time:
            end_time= datetime.now()
        # print(resp_json)
        # print(status_code)
        # assert status_code == 200
        # assert isinstance(resp_json['created'], int)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'id': id,
        'enhance_prompt': enhance_prompt,
        'status_code': status_code,
        'json': resp_json
    }