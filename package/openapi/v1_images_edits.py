import requests
from datetime import datetime


def v1_images_edits(domain, api_key, model='hunyuan-image', version=None, prompt=None,
                    image=None, image_url=None, mask=None, mask_url='https://hunyuan-base-prod-1258344703.cos-internal.ap-guangzhou.tencentcos.cn/openapi/default/297d75f0184917afb5ad6bfb263673ec.jpg?q-sign-algorithm=sha1&q-ak=AKID0qSq0xJRL7h3A4nIYJFrFOJ1VlnbIm26&q-sign-time=1718111602;1718198002&q-key-time=1718111602;1718198002&q-header-list=host&q-url-param-list=&q-signature=40f7a386d20daebab207364774652f42330f2578',
                    size=None, n=None, seed=None, footnote=None, moderation=None, negative_prompt=None,
                    cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/edits'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    
    payload_variables = [
        'model', 'version', 'prompt','negative_prompt',
        'image', 'image_url',
        'mask', 'mask_url',
        'size', 'n', 'seed', 'footnote', 'moderation'
        ]
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        qid = None
        urls = []
        err_message = None
        created = None
        status = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            status_code = resp.status_code
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                for data in resp_json['data']:
                    urls.append(data['url'])
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
        if not end_time:
            end_time= datetime.now()
        
        print(resp_json)
        print(status_code)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'urls': urls,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }
