import requests
from datetime import datetime

def v1_document_mindmap(domain,api_key,model,file_id,user=None,cookie=None):
    """针对文档返回脑图相关内容"""
    url = f'{domain}/openapi/v1/document/mind_map'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload = {}
    if model is not None:
        payload['model'] = model
    if file_id is not None:
        payload['file_id'] = file_id
    if user is not None:
        payload['user'] = user

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        id = None
        created = None
        data = None
        print(f"url:{url}")
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print(f"req: {resp.request.body}")
            print(f"status code:{status_code}")
            try:
                resp_json = resp.json()
                print(resp_json)
                if 'id' in resp_json:
                    id = resp_json['id']
                if 'created' in resp_json:
                    created = resp_json['created']
                if 'data' in resp_json:
                    data = resp_json['data']
            except:
                print(resp.text)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'json': resp_json,
        'status_code': status_code,
        'id': id,
        'created': created,
        'data': data,
    }
