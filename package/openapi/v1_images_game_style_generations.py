from datetime import datetime
import logging
import requests
import sseclient
import json
import time

def v1_images_game_style_generations(domain, api_key=None, cookie=None, authorization=None, **kwargs):
    print("\n" + "~" * 30 + " v1/images/game_style/generations " + "~" * 30 + "\n")
    url = f'{domain}/openapi/v1/images/game_style/generations'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        # 'env': 'image_intention_test3'
        }
    payload_variables = [
        'prompt', 'model', 'version', 'image', 'image_url', 'n', 'size', 'footnote', 'seed', 'style']
    default_kwargs = {}
    kwargs = { **default_kwargs, **kwargs }
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}

    print("="*30 + "req detail" + "="*30)
    print(f"url: {url}")
    print("payload: ")
    print(json.dumps(payload, indent=4, ensure_ascii=False))


    end_time = None
    start_time = datetime.now()
    created = None
    status_code = None
    resp_json = None
    id = None
    data = None
    error = None
    err_code = None
    err_message = None
    with requests.post(url, headers=headers, json=payload, verify=False,timeout=900) as resp:

        print('\n' + "=" * 30 + "resp detail" + "=" * 30)

        print(f"status_code: {resp.status_code}")
        print("headers:")
        print(resp.headers)
        status_code = resp.status_code
        try:
            resp_json = resp.json()
            print('resp.json:')
            print(json.dumps(resp_json, indent=4, ensure_ascii=False))

            if 'id' in resp_json:
                id = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                data = resp_json['data']
            if 'error' in resp_json:
                error = resp_json['error']
                if 'code' in error and err_code != '':
                    err_code = int(error['code'])
                if 'message' in error:
                    err_message = error['message']
                if not id and 'id' in error:
                    id = error['id']
        except Exception as e:
            print(e)
            print("resp.text:")
            print(resp.text)
    if not end_time:
        end_time= datetime.now()


    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'status_code': status_code,
        'created': created,
        'json': resp_json,
        'id': id,
        'data': data,
        'error': error,
        'err_code': err_code,
        'err_message': err_message
    }