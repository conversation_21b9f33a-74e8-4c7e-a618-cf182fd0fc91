from datetime import datetime
import logging
import requests
import sseclient
import json
import time

def v1_videos_cartoon_task(
        domain,
        api_key=None,
        cookie=None,
        authorization=None,
        **kwargs
        ):
    # print("\n" + "~" * 30 + " v1/videos/cartoon/task " + "~" * 30 + "\n")
    url = f'{domain}/openapi/v1/videos/cartoon/task'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        # "env":"image_intention_test3"
        # 'x-route-env': 'irag',
        # 'env':'image_generate'
        }
    payload_variables = [
        'task_id']
    default_kwargs = {}
    kwargs = { **default_kwargs, **kwargs }
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}

    # print("="*30 + "req detail" + "="*30)
    # print(f"url: {url}")
    # print("payload: ")
    # print(json.dumps(payload, indent=4))

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        status_code = None
        resp_json = None
        cid = None
        err_code = None
        err_message = None
        status = None
        videos = None
        with requests.post(url, headers=headers, json=payload, verify=False,timeout=900) as resp:

            # print('\n' + "=" * 30 + "resp detail" + "=" * 30)

            # print(f"status_code: {resp.status_code}")
            # print("headers:")
            # print(resp.headers)
            status_code = resp.status_code
            try:
                resp_json = resp.json()
                print(resp_json)
                # print('resp.json:')
                # print(json.dumps(resp_json, indent=4))

                if 'id' in resp_json:
                    cid = resp_json['id']
                if 'created' in resp_json:
                    created = resp_json['created']
                if 'error' in resp_json:
                    error = resp_json['error']
                    if 'code' in error:
                        err_code = error['code']
                    if 'message' in error:
                        err_message = error['message']

                if 'status' in resp_json:
                    status = resp_json['status']
                if 'videos' in resp_json:
                    videos = resp_json['videos']

            except requests.exceptions.JSONDecodeError:
                # print("resp.text:")
                print(resp.text)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'status_code': status_code,
        'created': created,
        'json': resp_json,
        'id': cid,
        'status': status,
        'videos': videos,
        'err_code': err_code,
        'err_message': err_message
    }