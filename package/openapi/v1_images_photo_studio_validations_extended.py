import requests
from datetime import datetime


def v1_images_photo_studio_validations_extended(domain, api_key, image_url=None, frontal_image_url=None, cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/photo_studio/validations/extended'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    # print(type(image))
    payload = {
        "training_file": {
            "image_url": image_url,
            "frontal_image_url": frontal_image_url
            }
        }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        status=None
        score=None
        resp_json = None
        qid = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            print(resp.text)
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'result' in resp_json:
                result = resp_json['result']
                if 'status' in result:
                    status = result['status']
                    print(f"status:{status}")
                if 'score' in result:
                    score = result['score']
                    print(f"score:{score}")
        if not end_time:
            end_time= datetime.now()
        
        # print(resp_json)
        # print(status_code)
    return {
        'status': status,
        'status_code': status_code,
        'score': score,
        'json': resp_json,
        'id': qid
    }