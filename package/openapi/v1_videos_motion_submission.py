import requests
import json
from datetime import datetime


def v1_videos_motion_submission(domain, api_key, cookie=None, version=None, image=None, image_url=None, n=None, duration=None, resolution=None, mask_type=None, seed=None, moderation=None, layers=None):
    url = f'{domain}/openapi/v1/videos/motion/submission'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': 'hunyuan-video-motion',
    }

    if version is not None:
        payload['version'] = version
    if image is not None:
        payload['image'] = image
    if image_url is not None:
        payload['image_url'] = image_url
    if n is not None:
        payload['n'] = n
    if duration is not None:
        payload['duration'] = duration
    if resolution is not None:
        payload['resolution'] = resolution
    if mask_type is not None:
        payload['mask_type'] = mask_type
    if seed is not None:
        payload['seed'] = seed
    if moderation is not None:
        payload['moderation'] = moderation
    if layers is not None:
        payload['layers'] = layers

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'task_id': task_id,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }