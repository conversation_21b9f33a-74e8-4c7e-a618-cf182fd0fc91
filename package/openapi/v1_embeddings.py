import requests
from datetime import datetime


def embeddings(domain, api_key, model='hunyuan-embedding-public', cookie=None, authorization=None, input_txt=None):
    url = f'{domain}/openapi/v1/embeddings'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': model,
        'input': input_txt
    }

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        finish_reason = None
        message_content = None
        status_code = None
        resp_json = None
        id = None
        # print("messages:{}".format(messages))
        # from requests import Request,Session
        # s=Session()
        # req = Request('POST',url, headers=headers, json=payload)
        # prepared = req.prepare()
        # def pretty_print_POST(req):
        #     """
        #     At this point it is completely built and ready
        #     to be fired; it is "prepared".

        #     However pay attention at the formatting used in 
        #     this function because it is programmed to be pretty 
        #     printed and may differ from the actual request.
        #     """
        #     print('{}\n{}\r\n{}\r\n\r\n{}'.format(
        #         '-----------START-----------',
        #         req.method + ' ' + req.url,
        #         '\r\n'.join('{}: {}'.format(k, v) for k, v in req.headers.items()),
        #         req.body,
        #     ))

        # pretty_print_POST(prepared)
        with requests.post(url, headers=headers, json=payload, verify=False) as resp:
            print(f'resp.status_code: {resp.status_code}')
            print(f'resp.text: {resp.text}')
            status_code = resp.status_code
            resp_json = resp.json()
            print(f'resp_json: {resp_json}')
            if resp.status_code == 200:
                assert 'id' in resp_json
                assert 'model' in resp_json
                assert 'data' in resp_json
                assert 'object' in resp_json
                assert 'usage' in resp_json
                id = resp_json['id']
        if not end_time:
            end_time= datetime.now()
        # assert isinstance(resp_json['created'], int)
        
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'finish_reason': finish_reason,
        'message_content': message_content,
        'status_code': status_code,
        'json': resp_json,
        # 'id': resp_json['id'],
        # 'model': resp_json['model'],
        # 'object': resp_json['object'],
        # 'data_0_embedding': resp_json['data'][0]['id'],
        # 'data_0_index': resp_json['data'][0]['index'],
        # 'data_0_object': resp_json['data'][0]['object'],
        # 'usage_prompt_tokens': resp_json['usage']['prompt_tokens'],
        # 'usage_total_tokens': resp_json['usage']['total_tokens']
    }