import requests
import json
from datetime import datetime


def v1_videos_stylizations_submission(domain, api_key, prompt, cookie=None, model=None, version=None, video_url=None, video=None, 
                                      style=None, n=None, duration=None, footnote=None, seed=None, moderation=None):
    url = f'{domain}/openapi/v1/videos/stylizations/submission'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': 'hunyuan-video',
    }

    if prompt is not None:
        payload['prompt'] = prompt
    if version is not None:
        payload['version'] = version
    if video_url is not None:
        payload['video_url'] = video_url
    if video is not None:
        payload['video'] = video
    if style is not None:
        payload['style'] = style
    if n is not None:
        payload['n'] = n
    if duration is not None:
        payload['duration'] = duration
    if footnote is not None:
        payload['footnote'] = footnote
    if seed is not None:
        payload['seed'] = seed
    if moderation is not None:
        payload['moderation'] = moderation

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'task_id': task_id,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }