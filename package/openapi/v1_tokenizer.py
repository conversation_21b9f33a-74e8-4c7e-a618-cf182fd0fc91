import requests
from datetime import datetime


def v1_tokenizer(domain, api_key, prompt, model, cookie):
    url = '{domain}/openapi/v1/tokenizer'.format(domain=domain)
    headers = {
        'Authorization': 'Bearer {}'.format(api_key),
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': model,
        # 'version': None,
        # 'stream': False,
        # 'enhance': False,
        # 'enhancements': None,
        # 'force_enhancement': None,
        'prompt': prompt,
        # 'temperature': None,
        # 'top_p': None,
        # 'top_k': None,
        # 'random_seed': None
    }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        token_count = None
        character_count = None
        resp_json = None
        texts = None
        with requests.post(url, headers=headers, json=payload, verify=False) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            print(resp_json)
            if 'token_count' in resp_json:
                token_count = resp_json['token_count']
            if 'character_count' in resp_json:
                character_count = resp_json['character_count']
            if 'texts' in resp_json:
                texts = resp_json['texts']
        if not end_time:
            end_time= datetime.now()
        # print(resp_json)
        # print(status_code)
        # assert status_code == 200
        # assert isinstance(resp_json['created'], int)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'token_count': token_count,
        'character_count': character_count,
        'texts': texts,
        'status_code': status_code
    }