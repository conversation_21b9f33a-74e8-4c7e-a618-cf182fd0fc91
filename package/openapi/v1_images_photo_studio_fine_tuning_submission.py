import requests
from datetime import datetime


def v1_images_photo_studio_fine_tuning_submission(domain, api_key, extended_image_urls=None, frontal_image_url=None, cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/photo_studio/fine_tuning/submission'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    # print(type(image))
    payload = {
        "training_file": {
            "frontal_image_url": frontal_image_url,
            "extended_image_urls": extended_image_urls
            }
        }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        status=None
        created=None
        task_id=None
        resp_json = None
        qid = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            print(resp.text)
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
        if not end_time:
            end_time= datetime.now()
        
        # print(resp_json)
        # print(status_code)
    return {
        'status': status,
        'status_code': status_code,
        'created': created,
        'task_id': task_id,
        'json': resp_json,
        'id': qid
    }