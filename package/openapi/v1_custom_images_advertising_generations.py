import requests
import json
from datetime import datetime


def v1_custom_images_advertising_generations(domain, api_key, model, prompt, cookie=None, **kwargs):
    """
    小说图生图
    文档：https://iwiki.woa.com/p/4010715535#小说图生图
    """
    print("\n" + "~" * 30 + " v1/custom/images/advertising/generations " + "~" * 30 + "\n")
    url = f'{domain}/openapi/v1/custom/images/advertising/generations'
    headers = {
        # 'x-route-env': '20241225-merge-ad-triton-t2i-i2i',
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload_variables = [
        'image_url', 'image', 'negative_prompt', 'seed', 'n', 'width', 'height', 'guidance_scale', 'i_scale', 't_scale'
    ]

    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}

    assert 'image_url' in payload or 'image' in payload

    payload['model'] = model
    payload['prompt'] = prompt

    print("="*30 + "req detail" + "="*30)
    print(f"url: {url}")
    print("payload: ")
    print(json.dumps(payload, indent=4))

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created_at = None
        status_code = None
        resp_json = None
        id = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=600) as resp:
            status_code = resp.status_code
            print('\n' + "=" * 30 + "resp detail" + "=" * 30)

            print(f"status_code: {resp.status_code}")
            print("headers:")
            print(resp.headers)
            try:
                resp_json = resp.json()
                print('resp.json:')
                print(json.dumps(resp_json, indent=4))
                if 'id' in resp_json:
                    id = resp_json['id']
                if 'created_at' in resp_json:
                    created_at = resp_json['created_at']
                if 'error' in resp_json:
                    if 'id' in resp_json['error']:
                        id = resp_json['error']['id']
                    if 'message' in resp_json['error']:
                        err_message = resp_json['error']['message']
            except Exception as e:
                print("resp.text:")
                print(resp.text)
        if not end_time:
            end_time = datetime.now()
    return {
        'time_consumption': (end_time - start_time).total_seconds(),
        'created_at': created_at,
        'id': id,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'resp': resp
    }

