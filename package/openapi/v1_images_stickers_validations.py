import requests
from datetime import datetime


def v1_images_stickers_validations(domain, api_key, image=None, cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/stickers/validations'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    # print(type(image))
    payload = {
        'image': image
    }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        status=None
        resp_json = None
        qid = None
        # from requests import Request,Session
        # s=Session()
        # req = Request('POST',url, headers=headers, json=payload)
        # prepared = req.prepare()
        # def pretty_print_POST(req):
        #     print('{}\n{}\r\n{}\r\n\r\n{}'.format(
        #         '-----------START-----------',
        #         req.method + ' ' + req.url,
        #         '\r\n'.join('{}: {}'.format(k, v) for k, v in req.headers.items()),
        #         req.body,
        #     ))

        # pretty_print_POST(prepared)
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            print(resp.text)
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'status' in resp_json:
                status = resp_json['status']
                print(f"status:{status}")
        if not end_time:
            end_time= datetime.now()
        
        # print(resp_json)
        # print(status_code)
    return {
        'status': status,
        'status_code': status_code,
        'json': resp_json,
        'id': qid
    }