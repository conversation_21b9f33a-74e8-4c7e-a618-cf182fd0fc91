import requests
from datetime import datetime


def v1_images_canny(domain, api_key, prompt=None, canny=None, model=None, footnote=None, moderation=None, authorization=None, cookie=None):
    url = f'{domain}/openapi/v1/images/canny'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload_variables = [
        'prompt','canny','model',
        'footnote', 'moderation'
        ]
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        qid = None
        urls = []
        err_message = None
        created = None
        status = None
        # from requests import Request,Session
        # s=Session()
        # req = Request('POST',url, headers=headers, json=payload)
        # prepared = req.prepare()
        # def pretty_print_POST(req):
        #     print('{}\n{}\r\n{}\r\n\r\n{}'.format(
        #         '-----------START-----------',
        #         req.method + ' ' + req.url,
        #         '\r\n'.join('{}: {}'.format(k, v) for k, v in req.headers.items()),
        #         req.body,
        #     ))

        # pretty_print_POST(prepared)
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                for data in resp_json['data']:
                    urls.append(data['url'])
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
        if not end_time:
            end_time= datetime.now()
        
        print(resp_json)
        print(status_code)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'urls': urls,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }