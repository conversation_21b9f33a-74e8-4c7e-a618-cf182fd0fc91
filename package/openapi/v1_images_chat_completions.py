from datetime import datetime
import logging
import requests
import sseclient
import json
import time

def v1_images_chat_completions(
        domain,
        api_key=None,
        cookie=None,
        authorization=None, stream=False,
        **kwargs
        ):
    """发起聊天"""
    url = f'{domain}/openapi/v1/images/chat/completions'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie,
        # Todo 测试临时添加
        'env': 'image_generate'
        }
    payload_variables = [
        'model', 'messages', 'version', 'footnote', 'moderation', 'seed', 'size', 'style',
        # 图生图新增参数
        'workflow_id'
        ]
    default_kwargs = {'model': 'hunyuan-image'}
    kwargs = { **default_kwargs, **kwargs }
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    print(f"请求payload：{payload}")
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        finish_reason = None
        message_content = None
        resp_seed = None
        image_prompt = None
        created = None
        status_code = None
        resp_json = None
        cid = None
        moderation_level = None
        # print(f"messages:{messages}")
        # start_perf_counter = time.perf_counter()
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            # print(f"status_code: {resp.status_code}")
            status_code = resp.status_code
            # print(f"返回:{resp.headers}")
            # if stream:
            #     all_event_data=[]
            #     msg=[]
            #     client = sseclient.SSEClient(resp)
            #     for event in client.events():
            #         if event.data != '':
            #             all_event_data.append(event.data)
            #             try:
            #                 line_json = json.loads(event.data)
            #                 if 'choices' in line_json:
            #                     if 'content' in line_json['choices'][0]['delta']:
            #                         msg.append(line_json['choices'][0]['delta']['content'])
            #                     if 'finish_reason' in line_json['choices'][0]:
            #                         current_finish_reason = line_json['choices'][0]['finish_reason']
            #                         if current_finish_reason is not None:
            #                             finish_reason = current_finish_reason
            #                 if 'id' in line_json:
            #                     cid = line_json['id']
            #                 if 'moderation_level' in line_json:
            #                     moderation_level = line_json['moderation_level']
            #             except json.decoder.JSONDecodeError:
            #                 pass
            #     message_content = ''.join(msg)
            #     resp_json = all_event_data
            #     print(f"message_content:{message_content}")
            # else:
            # print("返回:"+resp.text)
            try:
                resp_json = resp.json()
                print(f'返回体{resp_json}')
                if 'id' in resp_json:
                    cid = resp_json['id']
                if 'data' in resp_json:
                    url = resp_json['data'][0]['url']
                    image_prompt = resp_json['data'][0]['image_prompt']
                    resp_seed = resp_json['data'][0]['seed']
                    # print(f"image_prompt:{image_prompt}")
                if 'created' in resp_json:
                    created = resp_json['created']
            except requests.exceptions.JSONDecodeError:
                pass
        if not end_time:
            end_time= datetime.now()
        # print(resp_json)
        # print(status_code)
        # print(resp.headers)
        if status_code != 200:
            logging.error('请求失败，状态码：%s，响应文本：%s', status_code, resp.text)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'url': url,
        'status_code': status_code,
        'json': resp_json,
        'id': cid,
        'image_prompt': image_prompt,
        'seed': resp_seed
    }
