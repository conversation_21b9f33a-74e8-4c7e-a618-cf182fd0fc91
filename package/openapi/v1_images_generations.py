import logging

import requests
import json
from datetime import datetime


def v1_images_generations(domain, api_key=None, cookie=None, **kwargs):
    url = f'{domain}/openapi/v1/images/generations'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie,
        # Todo 测试临时添加
        'env':'image_generate'
        }
    payload_variables = [
        'prompt','n','size',
        'footnote', 'moderation', 'clip_skip', 'seed', 'style', 'version', 'revise', 'metadata', 'model',
        # 新增图生图相关参数
        'image', 'image_url', 'workflow_id'
        ]
    metadatad_variables = [
        'Label', 'ContentProducer', 'ProduceID', 'Propagator', 'PropagateID'
        ]
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    metadata = {var: val for var, val in kwargs.items() if var in metadatad_variables and val is not None}
    if metadata:
        payload['metadata'] = json.dumps(metadata)
    # payload['style'] = 'ad_game'
    # if version is not None:
    # payload['version'] = 'v1.9'
    # print(f"请求{payload}")
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        urls = []
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=600) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            print(f"response: {resp_json}")
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                for data in resp_json['data']:
                    urls.append(data['url'])
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'urls': urls,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }
