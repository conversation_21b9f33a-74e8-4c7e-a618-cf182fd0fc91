import requests
import sseclient
import json
from datetime import datetime

def v1_document_translations(domain,api_key,model,index,file_id,stream=None,user=None,cookie=None):
    """针对文档返回翻译内容"""
    url = f'{domain}/openapi/v1/document/translations'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload = {}
    if model is not None:
        payload['model'] = model
    if file_id is not None:
        payload['file_id'] = file_id
    if index is not None:
        payload['index'] = index
    if user is not None:
        payload['user'] = user
    if stream is not None:
        payload['stream'] = stream

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        finish_reason = None
        message_content = None
        translation_info = None
        usage = None
        print(f"url:{url}")
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print(f"request body:{resp.request.body}")
            status_code = resp.status_code
            print(f"返回:{resp.headers}")
            if stream:
                all_event_data=[]
                msg=[]
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    print(event.data)
                    if event.data != '':
                        all_event_data.append(event.data)
                        try:
                            line_json = json.loads(event.data)
                            if 'choices' in line_json:
                                if 'content' in line_json['choices'][0]['delta']:
                                    msg.append(line_json['choices'][0]['delta']['content'])
                                if 'finish_reason' in line_json['choices'][0]:
                                    current_finish_reason = line_json['choices'][0]['finish_reason']
                                    if current_finish_reason is not None:
                                        finish_reason = current_finish_reason
                                    if 'usage' in line_json:
                                        usage = line_json['usage']
                                    if 'translation' in line_json:
                                        translation_info = line_json['translation']
                        except json.decoder.JSONDecodeError:
                            pass
                message_content = ''.join(msg)
                resp_json = all_event_data
                print(f"message_content:{message_content}")
            else:
                try:
                    resp_json = resp.json()
                    print(f'返回体:{resp_json}')
                    if 'translation' in resp_json:
                        translation_info = resp_json['translation']
                    if 'choices' in resp_json:
                        finish_reason = resp_json['choices'][0]['finish_reason']
                        message_content = resp_json['choices'][0]['message']['content']
                        print(f"message_content:{message_content}")
                    if 'usage' in resp_json:
                        usage = resp_json['usage']
                except requests.exceptions.JSONDecodeError:
                    print("返回体:"+resp.text)
                    pass
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'json': resp_json,
        'status_code': status_code,
        'translation_info': translation_info,
        'finish_reason': finish_reason,
        'usage': usage,
        'message_content': message_content
    }
