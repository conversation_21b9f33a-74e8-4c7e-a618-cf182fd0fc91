import requests
from datetime import datetime

def v1_assistant_create_thread(domain, api_key, user=None, cookie=None):
    """创建会话"""
    url = f'{domain}/v1/threads'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload_variables = ['user']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        print(f"url:{url}")
        print(f"payload:{payload}")
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            try:
                resp_json = resp.json()
                print(resp_json)
            except:
                print(resp.text)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'json': resp_json,
        'status_code': status_code,
    }
