import requests
import json
from datetime import datetime
import base64

def v1_images_animation_generations_submission(domain, api_key, pose, cookie=None, **kwargs):
    """
    功能：基于输入图片进行动图生成
    文档：https://iwiki.woa.com/p/4010715535#表情动图生成任务提交
    :param image: 输入的参考图图片文件内容，base64 编码，大小不超过 6M,支持 jpg/jpeg/png 格式
    :param pose: 动作序列ID：，可选动作序列见下方 “动作序列模板表”
    """

    print("～" * 30 + "images/animation/generations/submission" + "～" * 30)
    url = f'{domain}/openapi/v1/images/animation/generations/submission'
    headers = {
        # 'x-route-env': '20241227-animation-new21post',
        # 'env':'image_generate',
        # 'x-route-env': '20250120-chunjiepose',
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie,
    }

    payload_variables = [
        'image', 'image_url', 'model', 'version', 'n', 'footnote', 'resolution', 'text', 'haircut'
    ]

    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    payload['pose'] = pose

    assert 'image' in payload or 'image_url' in payload
    print(payload)
    print(headers)
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        task_id = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print('='*30+'status_code'+'='*30)
            print(resp.status_code)
            print('='*30+'resp.text'+'='*30)
            print(resp.text)
            print('='*30+'resp.content'+'='*30)
            print(resp.content)
            print('='*30+'resp.request.body'+'='*30)
            print(resp.request.body)
            resp_json = resp.json()
            print('='*30+'resp.json()'+'='*30)
            print(resp_json)
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time = datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message,
        'task_id': task_id
    }
