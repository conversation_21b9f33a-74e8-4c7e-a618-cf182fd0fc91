import curlify
import requests
from datetime import datetime


def v1_images_retrieval(domain, api_key, word=None, size=None, request_id=None, model='hunyuan-image-retrieval',
                        offset=None, limit=None, style=None, scene=None):
    """搜图"""
    url = f'{domain}/openapi/v1/images/retrieval'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-type': 'application/json',
    }
    payload_variables = ['word', 'size', 'request_id', 'model', 'offset', 'limit', 'style', 'scene']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created_at = None
        status_code = None
        resp_json = None
        id = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            print(curlify.to_curl(resp.request))
            if 'id' in resp_json:
                id = resp_json['id']
            if 'created_at' in resp_json:
                created_at = resp_json['created_at']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    id = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            print(url)
            print(f"{'='*10}resp_json{'='*10}")
            print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created_at': created_at,
        'id': id,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'resp': resp
    }
