import requests
from datetime import datetime

def v1_assistant_thread_message_view(domain,api_key,thread_id,run_id=None,limit=None,order=None,user=None,cookie=None):
    """查看消息列表"""
    url = f'{domain}/v1/threads/{thread_id}/messages'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    params_variables = ['run_id','limit','order','user']
    params = {var: val for var, val in locals().items() if var in params_variables and val is not None}
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        resp_json = None
        print(f"url:{url}")
        with requests.get(url, headers=headers, params=params, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print(f"status code:{status_code}")
            try:
                resp_json = resp.json()
                print(resp_json)
            except:
                print(resp.text)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'json': resp_json,
        'status_code': status_code,
    }
