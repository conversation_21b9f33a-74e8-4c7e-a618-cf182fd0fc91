import curlify
import requests
import json
from datetime import datetime


def v1_images_face_merge_image_validations(domain, api_key, image, model, cookie=None):
    url = f'{domain}/openapi/v1/images/face_merge/image_validations'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload_variables = [
        'image',
        'model',
        ]
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    print(headers)

    end_time = None
    start_time = datetime.now()
    status = None
    face_rects = []
    status_code = None
    resp_json = None
    qid = None
    err_message = None
    with requests.post(url, headers=headers, json=payload, verify=False, timeout=600) as resp:
        status_code = resp.status_code
        resp_json = resp.json()
        # print(curlify.to_curl(resp.request))
        print(resp_json)
        if 'id' in resp_json:
            qid = resp_json['id']
        if 'status' in resp_json:
            status = resp_json['status']
        if 'error' in resp_json:
            if 'id' in resp_json['error']:
                qid = resp_json['error']['id']
            if 'message' in resp_json['error']:
                err_message = resp_json['error']['message']
        # print(resp_json)
    if not end_time:
        end_time = datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'status': status,
        'face_rects': face_rects,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }
