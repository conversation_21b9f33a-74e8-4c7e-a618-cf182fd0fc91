import requests
import json
from datetime import datetime


def v1_videos_generations_submission(domain, api_key, prompt, cookie=None, image=None, image_url=None, version=None, n=None, aspect_radio=None, resolution=None, duration=None, footnote=None, style=None, seed=None, moderation=None):
# def v1_images_generations(domain, api_key, prompt, n, size, moderation, cookie=None, footnote=None, clip_skip=None, seed=None, style='regular',version='v1.7-ad'):
    url = '{domain}/openapi/v1/videos/generations'.format(domain=domain)
    headers = {
        'Authorization': 'Bearer {}'.format(api_key),
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': 'hunyuan-video',
    }

    if version is not None:
        payload['version'] = version
    if prompt is not None:
        payload['prompt'] = prompt
    if image is not None:
        payload['image'] = image
    if image_url is not None:
        payload['image_url'] = image_url
    if n is not None:
        payload['n'] = n
    if aspect_radio is not None:
        payload['aspect_radio'] = aspect_radio
    if resolution is not None:
        payload['resolution'] = resolution
    if duration is not None:
        payload['duration'] = duration
    if footnote is not None:
        payload['footnote'] = footnote
    if style is not None:
        payload['style'] = style
    if seed is not None:
        payload['seed'] = seed
    if moderation is not None:
        payload['moderation'] = moderation

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        urls = []
        status_code = None
        resp_json = None
        id = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                id = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                for data in resp_json['data']:
                    urls.append(data['url'])
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    id = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'urls': urls,
        'status_code': status_code,
        'id': id,
        'json': resp_json,
        'err_message': err_message
    }