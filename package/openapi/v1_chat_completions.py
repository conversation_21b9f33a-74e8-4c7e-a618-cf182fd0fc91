from datetime import datetime
import logging
import requests
import sseclient
import json
import time

def v1_chat_completions(
        domain,
        api_key=None,
        cookie=None,
        authorization=None,
        **kwargs
        ):
    """
    发起聊天
    :param domain: API的域名
    :param api_key: API密钥，用于生成授权头
    :param cookie: 可选的cookie信息
    :param authorization: 可选的授权头，如果提供则优先使用
    :param **kwargs: 发起聊天接口的参数词典
    :return: 返回一个词典，返回内容待调整，例如：
        {
            'time_consumption': 13,
            'finish_reason': 'sensitive',
            'message_content': '今天天气不错',
            'status_code': 200,
            'json': {},
            'id': 'xxxx-xxx',
            'moderation_level': '1'
        }
    """
    default_kwargs = {'model': 'hunyuan', 'enable_enhancement': True}
    kwargs = { **default_kwargs, **kwargs }
    stream = kwargs.get('stream', None)
    url = f'{domain}/openapi/v1/chat/completions'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload_variables = [
        'model', 'version', 'stream', 'messages',
        'temperature', 'top_p', 'user',
        'tools', 'tool_choice',
        'stream_moderation', 'enable_enhancement',
        'enhance', 'enhancements', 'force_enhancement', 'force_search_enhancement', 'search_info','citation',
        'strict_moderation','search_blacklist',
        'random_seed', 'presence_penalty', 'debug_flag', 'content_filter', 'moderation', # 内部参数
        'seed', 'processes' ,'search_scene', 'citation', 'max_tokens', 'stop', 'client_ip', 'enable_speed_search', 'enable_deep_search', # 待覆盖司内参数
        'top_k','translation_target', 'enable_deep_read', 'enable_multimedia' # 待覆盖内部参数
        ]
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        finish_reason = None
        message_content = None
        status_code = None
        resp_json = None
        cid = None
        moderation_level = None
        replaces = None
        # print(f"messages:{messages}")
        # print(f"payload: {payload}")
        # start_perf_counter = time.perf_counter()
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print(f"request body:{resp.request.body}")
            status_code = resp.status_code
            print(f"返回:{resp.headers}")
            if stream:
                all_event_data=[]
                msg=[]
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    print(event.data)
                    if event.data != '':
                        all_event_data.append(event.data)
                        try:
                            line_json = json.loads(event.data)
                            if 'choices' in line_json:
                                if 'content' in line_json['choices'][0]['delta']:
                                    msg.append(line_json['choices'][0]['delta']['content'])
                                if 'finish_reason' in line_json['choices'][0]:
                                    current_finish_reason = line_json['choices'][0]['finish_reason']
                                    if current_finish_reason is not None:
                                        finish_reason = current_finish_reason
                                if 'moderation_level' in line_json['choices'][0]:
                                    moderation_level = line_json['choices'][0]['moderation_level']
                            if 'id' in line_json:
                                cid = line_json['id']
                            # if 'moderation_level' in line_json:
                            #     moderation_level = line_json['moderation_level']
                        except json.decoder.JSONDecodeError:
                            pass
                message_content = ''.join(msg)
                resp_json = all_event_data
                print(f"message_content:{message_content}")
            else:
                print("返回:"+resp.text)
                try:
                    resp_json = resp.json()
                    print(f'返回体{resp_json}')
                    if 'id' in resp_json:
                        cid = resp_json['id']
                    if 'choices' in resp_json:
                        finish_reason = resp_json['choices'][0]['finish_reason']
                        message_content = resp_json['choices'][0]['message']['content']
                        if 'moderation_level' in resp_json['choices'][0]:
                            moderation_level = resp_json['choices'][0]['moderation_level']
                        print(f"message_content:{message_content}")
                    if 'replaces' in resp_json:
                        replaces = resp_json['replaces']
                except requests.exceptions.JSONDecodeError:
                    pass
        if not end_time:
            end_time= datetime.now()
        # print(resp_json)
        # print(status_code)
        # print(resp.headers)
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'finish_reason': finish_reason,
        'message_content': message_content,
        'status_code': status_code,
        'json': resp_json,
        'id': cid,
        'moderation_level': moderation_level,
        'replaces': replaces
    }
