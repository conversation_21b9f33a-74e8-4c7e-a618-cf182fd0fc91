import requests
from datetime import datetime


def v1_images_photo_studio_generations(domain, api_key, model="design_id_hnsiufbnam:schema_id_uwquehqiws", n=None, style=None, cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/photo_studio/generations'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    # print(type(image))
    payload_variables = ['model','n','style']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    print(payload)
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        status=None
        created=None
        urls=[]
        resp_json = None
        qid = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            print(resp.text)
            resp_json = resp.json()
            print(f'返回体{resp_json}')
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'data' in resp_json:
                data_list = resp_json['data']
                for data in data_list:
                    urls.append(data['url'])
        if not end_time:
            end_time= datetime.now()
        
        # print(resp_json)
        # print(status_code)
    return {
        'status': status,
        'status_code': status_code,
        'created': created,
        'urls': urls,
        'json': resp_json,
        'id': qid
    }
