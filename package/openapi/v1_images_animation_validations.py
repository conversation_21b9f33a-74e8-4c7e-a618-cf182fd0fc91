import requests
import json
from datetime import datetime


def v1_images_animation_validations(domain, api_key, image, cookie=None):
    """
    功能：检测用于用户输入的图片文件是否符合要求
    文档：https://iwiki.woa.com/p/4012723187
    :param image: string 图片文件内容, base64 编码,大小不超过 6M ,支持 jpg/jpeg 格式
    :return id: 此次请求的id
    :return status: 图片检测结果,0 为检测成功，其他均为检测失败; 检测失败示例：1：图像分辨率过低2：人脸占比过小3：图像中存在多人4、人脸不完整或面部遮挡或没有检测到人脸；
    """
    url = f'{domain}/openapi/v1/images/animation/validations'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie,
        }
    payload = {
        'image': image
    }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        status = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print(url)
            # print(resp.request.headers)
            print(resp.status_code)
            print(resp.request.body)
            print('='*30+'status_code'+'='*30)
            print(resp.status_code)
            print('='*30+'resp.text'+'='*30)
            print(resp.text)
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'status' in resp_json:
                status = resp_json['status']
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message,
        'status': status
    }