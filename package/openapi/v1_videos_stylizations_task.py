import requests
import json
from datetime import datetime


def v1_videos_stylizations_task(domain, api_key, task_id, cookie=None):
    url = f'{domain}/openapi/v1/videos/stylizations/task'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'task_id': task_id
    }

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        urls = []
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'status' in resp_json:
                status = resp_json['status']
            if 'videos' in resp_json:
                for video in resp_json['videos']:
                    urls.append(video['url'])
            if 'error' in resp_json and resp_json['error'] is not None:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'status': status,
        'urls': urls,
        'err_message': err_message
    }