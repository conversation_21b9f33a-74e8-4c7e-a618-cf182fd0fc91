import requests
import json
from datetime import datetime


def v1_images_face_merge_one_face_generations_submission(domain, api_key, model, version, n, template_image, images, face_rect, style, ip_weight, footnote, cookie=None, ):
# def v1_images_generations(domain, api_key, prompt, n, size, moderation, cookie=None, footnote=None, clip_skip=None, seed=None, style='regular',version='v1.7-ad'):
    url = f'{domain}/openapi/v1/images/face_merge/one_face/generations/submission'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload_variables = [
        'model', 'version', 'n', 'template_image', 'images', 'face_rect', 'style', 'ip_weight', 'footnote'
        ]
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}
    # payload['style'] = 'ad_game'
    # if version is not None:
    # payload['version'] = 'v1.9'
    # print(payload)
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=600) as resp:
            status_code = resp.status_code
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
                # for data in resp_json['face_rects']:
                #     face_rects.append(data)
            if 'error' in resp_json:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'task_id': task_id,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }
