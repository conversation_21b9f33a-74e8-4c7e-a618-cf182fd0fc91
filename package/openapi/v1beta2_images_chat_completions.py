from datetime import datetime
import logging
import requests
import sseclient
import json
import time
def v1beta2_images_chat_completions(
        domain,
        api_key=None,
        cookie=None, stream=False,
        authorization=None,
        **kwargs
        ):
    """发起聊天"""
    # domain = 'http://trpc.amai.route.path.test.polaris:8000'
    print("~" * 30 + "v1beta2/images/chat/completions" + "~" * 30)
    url = f'{domain}/openapi/v1beta2/images/chat/completions'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        # 'x-route-env': 'irag',
        'env':'image_generate'
        }
    payload_variables = [
        'version', 'footnote', 'moderation', 'input_moderation', 'n', 'style', 'size', 'intention_data', 'workflow_id',
        'user', 'seed', 'model_id', 'messages']
    default_kwargs = {'model_id': 'hunyuan-image', 'version': 'v1.9.3'}
    kwargs = { **default_kwargs, **kwargs }
    payload = {var: val for var, val in kwargs.items() if var in payload_variables and val is not None}
    print(f"请求:{payload}")
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        finish_reason = None
        image = None
        created = None
        status_code = None
        resp_json = None
        cid = None
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            print("="*30 + "req detail" + "="*30)
            print(f"url: {resp.request.url}")
            print(f"status_code: {resp.status_code}")
            print(f"header: {resp.request.headers}")
            print(f"payload: {resp.request.body}")
            print("="*30 + "resp detail" + "="*30)
            print(f"header: {resp.headers}")
            status_code = resp.status_code
            print(f"返回:{resp.headers}")
            print("="*30 + "curl detail" + "="*30)
            try:
                resp_json = resp.json()
                print(f'返回:{resp_json}')
                if 'id' in resp_json:
                    cid = resp_json.get('id')
                if 'created' in resp_json:
                    created = resp_json['created']
                if 'choices' in resp_json:
                    choice = resp_json['choices'][0]
                    if 'finish_reason' in choice:
                        finish_reason = choice['finish_reason']
                    if 'message' in choice:
                        message = choice['message']
                        if 'content' in message:
                            for content in message['content']:
                                if content['type'] == 'image_url':
                                    image = content['image_url']
                                    break
            except requests.exceptions.JSONDecodeError:
                print("返回:"+resp.text)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'status_code': status_code,
        'created': created,
        'json': resp_json,
        'id': cid,
        'image': image,
        'finish_reason': finish_reason
    }
