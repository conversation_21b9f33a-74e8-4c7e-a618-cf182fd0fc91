import requests
from datetime import datetime


def v1_images_photo_studio_fine_tuning_task(domain, api_key, task_id=None, cookie=None, authorization=None):
    url = f'{domain}/openapi/v1/images/photo_studio/fine_tuning/task'
    if not authorization:
        authorization = f'Bearer {api_key}'
    headers = {
        'Authorization': authorization,
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    # print(type(image))
    payload = {
        "task_id": task_id
        }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        status_code = None
        status=None
        created=None
        status=None
        resp_json = None
        qid = None
        code=None
        message=None
        fine_tuned_model=None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            print("返回:"+resp.text)
            print(status_code)
            status_code = resp.status_code
            # print(resp.text)
            resp_json = resp.json()
            # print(f'返回体{resp_json}')
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'status' in resp_json:
                status = resp_json['status']
            if 'error' in resp_json:
                error = resp_json['error']
                if 'code' in error:
                    code = error['code']
                if 'message' in error:
                    message = error['message']
            if 'fine_tuned_model' in resp_json:
                fine_tuned_model = resp_json['fine_tuned_model']
        if not end_time:
            end_time= datetime.now()
        
        # print(resp_json)
        # print(status_code)
    return {
        'status': status,
        'status_code': status_code,
        'created': created,
        'code': code,
        'message': message,
        'fine_tuned_model': fine_tuned_model,
        'json': resp_json,
        'id': qid
    }