import requests
import sseclient
import json
import time
from datetime import datetime

def v1_translations(domain,api_key,model,stream,text,source=None,target=None,field=None,references=None,cookie=None):
    """文本翻译"""
    url = f'{domain}/openapi/v1/translations'
    headers = {
        # 'x-route-env': 'translate',
        # 'env': 'image_generate',
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
    }
    payload_variables = ['model','stream','text','source','target','field','references','cookie']
    payload = {var: val for var, val in locals().items() if var in payload_variables and val is not None}

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        message_content = None
        status_code = None
        resp_json = None
        cid = None
        finish_reason = None
        usage = None
        model = model
        print(f"url:{url}")
        print(f"payload:{payload}")
        with requests.post(url, headers=headers, json=payload, verify=False, stream=stream, timeout=900) as resp:
            print(f"status_code: {resp.status_code}")
            status_code = resp.status_code
            print(f"resp_headers:{resp.headers}")
            if stream:
                all_event_data=[]
                msg=[]
                client = sseclient.SSEClient(resp)
                for event in client.events():
                    if event.data != '':
                        all_event_data.append(event.data)
                        try:
                            line_json = json.loads(event.data)
                            if 'choices' in line_json:
                                if 'content' in line_json['choices'][0]['delta']:
                                    msg.append(line_json['choices'][0]['delta']['content'])
                                if 'finish_reason' in line_json['choices'][0]:
                                    current_finish_reason = line_json['choices'][0]['finish_reason']
                                    if current_finish_reason is not None:
                                        finish_reason = current_finish_reason
                                    usage = line_json['usage']
                                    model = line_json['model']
                            if 'id' in line_json:
                                cid = line_json['id']
                        except json.decoder.JSONDecodeError:
                            pass
                message_content = ''.join(msg)
                resp_json = all_event_data
                print(f"message_content:{message_content}")
            else:
                print("resp_text:"+resp.text)
                try:
                    resp_json = resp.json()
                    if 'id' in resp_json:
                        cid = resp_json['id']
                    if 'choices' in resp_json:
                        finish_reason = resp_json['choices'][0]['finish_reason']
                        message_content = resp_json['choices'][0]['message']['content']
                        print(f"message_content:{message_content}")
                    if 'usage' in resp_json:
                        usage = resp_json['usage']
                    if 'model' in resp_json:
                        model = resp_json['model']
                except requests.exceptions.JSONDecodeError:
                    pass
        if not end_time:
            end_time= datetime.now()
        print(f'resp_json:{resp_json}')
    return {
        'id': cid,
        'time_consumption': (end_time-start_time).total_seconds(),
        'finish_reason': finish_reason,
        'json': resp_json,
        'status_code': status_code,
        'message_content': message_content,
        'usage': usage,
        'model': model
    }
