import requests
import json
from datetime import datetime


def v1_images_animation_generations_task(domain, api_key, task_id, cookie=None):
    """
    功能：查询动图生成任务
    文档：https://iwiki.woa.com/p/4012723187
    :param task_id: 任务的task_id
    :return status: 任务状态，queued, running, succeeded, failed, cancelled, unknown
    """

    print("～" * 30 + "images/animation/generations/task" + "～" * 30)
    url = f'{domain}/openapi/v1/images/animation/generations/task'
    headers = {
        # 'x-route-env': '20241227-animation-new21post',
        # 'env': 'image_generate',
        # 'x-route-env': '20250120-chunjiepose',
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie,
    }
    payload = {
        'task_id': task_id
    }
    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        status_code = None
        resp_json = None
        qid = None
        error = None
        status = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            print(url)
            # print(resp.request.headers)
            print(resp.status_code)
            print(resp.request.body)
            print('='*30+'status_code'+'='*30)
            print(resp.status_code)
            print('='*30+'resp.text'+'='*30)
            print(resp.text)
            resp_json = resp.json()
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'status' in resp_json:
                status = resp_json['status']
            if 'error' in resp_json:
                error = resp_json['error']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'error': error,
        'status': status
    }