import requests
import json
from datetime import datetime
import base64


def v1_files_uploads(domain, api_key, cookie=None, file=None, purpose=None, user=None):
    url = f'{domain}/openapi/v1/files/uploads'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Cookie': cookie
    }
    data = {}
    files = {'file': file}

    if purpose is not None:
        data['purpose'] = purpose
    if user is not None:
        data['user'] = user

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created_at = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        bytes = None
        filename = None
        resp_object = None
        file_type = None
        with requests.post(url, headers=headers, data=data, files=files, verify=False, timeout=60) as resp:
            status_code = resp.status_code
            try:
                resp_json = resp.json()
                if 'id' in resp_json:
                    qid = resp_json['id']
                if 'bytes' in resp_json:
                    bytes = resp_json['bytes']
                if 'created_at' in resp_json:
                    created_at = resp_json['created_at']
                if 'filename' in resp_json:
                    filename = resp_json['filename']
                if 'purpose' in resp_json:
                    purpose = resp_json['purpose']
                if 'object' in resp_json:
                    resp_object = resp_json['object']
                if 'filetype' in resp_json:
                    file_type = resp_json['filetype']
                if 'error' in resp_json:
                    if 'id' in resp_json['error']:
                        qid = resp_json['error']['id']
                    if 'message' in resp_json['error']:
                        err_message = resp_json['error']['message']
                print(f"resp_json: {resp_json}")
            except json.decoder.JSONDecodeError:
                print(f"resp.text: {resp.text}")
                raise
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created_at': created_at,
        'id': qid,
        'json': resp_json,
        'status_code': status_code,
        'err_message': err_message,
        'bytes': bytes,
        'filename': filename,
        'purpose': purpose,
        'object': resp_object,
        'file_type': file_type
    }
