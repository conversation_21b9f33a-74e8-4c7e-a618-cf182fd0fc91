import requests
import json
from datetime import datetime



def v1_videos_generations_submission(domain, api_key, model, prompt, cookie=None, version=None, negative_prompt=None, n=None, aspect_radio=None, resolution=None, duration=None, footnote=None):
    url = f'{domain}/openapi/v1/videos/generations/submission'
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json',
        'Cookie': cookie
        }
    payload = {
        'model': model
    }

    if prompt is not None:
        payload['prompt'] = prompt
    if version is not None:
        payload['version'] = version
    if negative_prompt is not None:
        payload['negative_prompt'] = negative_prompt
    if n is not None:
        payload['n'] = n
    if aspect_radio is not None:
        payload['aspect_radio'] = aspect_radio
    if resolution is not None:
        payload['resolution'] = resolution
    if duration is not None:
        payload['duration'] = duration
    if footnote is not None:
        payload['footnote'] = footnote

    for _ in range(1):
        end_time = None
        start_time = datetime.now()
        created = None
        task_id = None
        status_code = None
        resp_json = None
        qid = None
        err_message = None
        with requests.post(url, headers=headers, json=payload, verify=False, timeout=60) as resp:
            print(f"status_code: {resp.status_code}")
            # resp_logger(resp, __name__)
            status_code = resp.status_code
            resp_json = resp.json()
            print(resp_json)
            if 'id' in resp_json:
                qid = resp_json['id']
            if 'created' in resp_json:
                created = resp_json['created']
            if 'task_id' in resp_json:
                task_id = resp_json['task_id']
            if resp_json.get('error') is not None:
                if 'id' in resp_json['error']:
                    qid = resp_json['error']['id']
                if 'message' in resp_json['error']:
                    err_message = resp_json['error']['message']
            # print(resp_json)
        if not end_time:
            end_time= datetime.now()
    return {
        'time_consumption': (end_time-start_time).total_seconds(),
        'created': created,
        'task_id': task_id,
        'status_code': status_code,
        'id': qid,
        'json': resp_json,
        'err_message': err_message
    }