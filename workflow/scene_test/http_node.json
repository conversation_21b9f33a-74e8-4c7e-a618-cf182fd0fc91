{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["node1", "node2"], "input": null, "output": {"properties": {"fbx_url": {"type": "string"}}}}, {"node_id": "node1", "node_type": "HTTP", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.model", "name": "fbx_url", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "fbx_url", "type": "string"}}], "output": {"properties": {"data.detect_result": {"type": "integer"}, "data": {"type": "string"}, "statuscode": {"type": "integer"}}}, "http": {"polaris_name": "trpc.amai.strategy.3d.process", "polaris_namespace": "Test", "url": "/openapi/v1beta1/3d/human/detect", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}], "method": "", "timeout": 0, "body": [{"id": "body.model", "name": "fbx_url", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "fbx_url", "type": "string"}}], "body_type": "", "query": null, "retry_times": 0}}, {"node_id": "node2", "node_type": "HTTP", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.model", "name": "fbx_url", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "fbx_url", "type": "string"}}], "output": {"properties": {"data.detect_result": {"type": "integer"}, "data": {"type": "string"}, "statuscode": {"type": "integer"}}}, "http": {"url": "http://trpc.amai.strategy.3d.process.test.polaris:8000/openapi/v1beta1/3d/human/detect", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}], "method": "", "timeout": 0, "body": [{"id": "body.model", "name": "fbx_url", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "fbx_url", "type": "string"}}], "body_type": "", "query": null, "retry_times": 0}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "detect_result1", "required": true, "input_type": "reference", "reference": {"node_id": "node1", "name": "data.detect_result", "type": "int"}}, {"name": "detect_result_data1", "required": true, "input_type": "reference", "reference": {"node_id": "node1", "name": "data", "type": "object"}}, {"name": "detect_result2", "required": true, "input_type": "reference", "reference": {"node_id": "node2", "name": "data.detect_result", "type": "int"}}, {"name": "detect_result_data2", "required": true, "input_type": "reference", "reference": {"node_id": "node2", "name": "data", "type": "object"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}