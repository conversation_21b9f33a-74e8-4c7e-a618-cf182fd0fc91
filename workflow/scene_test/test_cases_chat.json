[{"test_name": "对话流-非流式输出-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_llm_node", "description": "llm_node", "workflow_file": "workflow/scene_test/llm_node.json", "is_async": false, "parameters": {"sleep_time": 1, "prompt": "1+1=?"}}, "expected_execute_status": 2}, {"test_name": "对话流-流式输出-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_llm_node_streaming", "description": "llm_node_streaming", "workflow_file": "workflow/scene_test/llm_node_streaming.json", "is_async": false, "parameters": {"sleep_time": 1, "prompt": "1+1=?"}}, "expected_execute_status": 2}, {"test_name": "对话流-变量输出-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_llm_node_variable", "description": "llm_node_variable", "workflow_file": "workflow/scene_test/llm_node_variable.json", "is_async": false, "parameters": {"sleep_time": 1, "prompt": "介绍下deepseek"}}, "expected_execute_status": 2}]