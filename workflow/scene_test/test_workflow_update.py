'''
Author       : winsonyang 
Date         : 2025-03-11 16:39:31
LastEditors  : winsonyang 
LastEditTime : 2025-04-07 20:02:22
FilePath     : /aigc-api-test/workflow/scene_test/test_workflow_update.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
import json
from time import sleep
import pytest # type: ignore
from workflow.api.v1_workflow_update import v1_workflow_update
from workflow.api.v1_workflow_create import v1_workflow_create
from workflow.api.v1_workflow_detail import v1_workflow_detail
from workflow.utils.test_utils import load_test_cases, get_test_case_ids

# 加载测试用例并提取ID
test_cases = load_test_cases("workflow/scene_test/test_cases_update_workflow.json")

@pytest.mark.parametrize("test_case", test_cases, ids=get_test_case_ids(test_cases))
def test_create_and_query_and_invoke_workflow(test_case, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    测试创建workflow后查询新创建的workflow，并断言查询出来的workflow跟创建的是否一致
    """
    test_params = test_case["test_params"]
    workflow_file = test_params.get("workflow_file")
    update_workflow_file = test_case.get("update_workflow_file")
    
    # 确保 test_params 包含 workflow 或 workflow_file
    if workflow_file is None :
        pytest.fail("Either workflow or workflow_file must be provided in test case")
    
    # 更新test_params中的route_env和base_url
    test_params["route_env"] = workflow_route_env
    test_params["base_url"] = workflow_base_url
    

    
    try:
        # 调用API创建workflow
        create_response = v1_workflow_create(workspace=test_params["workspace"],description=test_params["description"],name=test_params["name"],workflow_file=workflow_file, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
        create_body = create_response["body"]
        
        # 断言响应包含必要字段
        assert "code" in create_body
        assert create_body["code"] == 0  # 验证返回码为0表示成功
        assert "data" in create_body
        assert "workflow_id" in create_body["data"]  # workflow_id在data字段中
        assert isinstance(create_body["data"]["workflow_id"], str)  # 验证workflow_id是字符串
        assert len(create_body["data"]["workflow_id"]) > 0  # 验证workflow_id不为空
        
        # 获取创建的workflow_id
        workflow_id = create_body["data"]["workflow_id"]
        
        # 打印创建的workflow信息
        print(f'Workflow created successfully! Workflow ID: {workflow_id}')
        
        # 调用API查询workflow
        detail_response = v1_workflow_detail(workflow_id, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
        detail_body = detail_response["body"]
        
        # 断言响应包含必要字段
        assert "code" in detail_body
        assert detail_body["code"] == 0  # 验证返回码为0表示成功
        assert "data" in detail_body
        assert "workflow_id" in detail_body["data"]  # workflow_id在data字段中
        
        # 获取创建和查询的workflow数据
        if workflow_file:
            with open(workflow_file, 'r') as f:
                created_workflow = json.load(f)
        else:
            pytest.fail("workflow_file must be provided in test case")
        queried_workflow = detail_body["data"]["workflow_dsl"]
        
        # 断言查询出来的workflow与创建的是否一致
        assert created_workflow["nodes"] == queried_workflow["nodes"] , "Created and queried workflows are not identical"
        
        # 打印查询的workflow信息
        print(f'Workflow queried successfully! Workflow ID: {detail_body["data"]["workflow_id"]}')

        # 获取创建和查询的workflow数据
        if update_workflow_file:
            with open(update_workflow_file, 'r') as f:
                update_workflow = json.load(f)
        else:
            pytest.fail("update_workflow_file must be provided in test case")

        # print(update_workflow)

        # 调用API更新workflow
        update_response = v1_workflow_update(workflow_id, workflow=update_workflow, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
        update_body = update_response["body"]
        # print(update_response)

        assert update_body["code"] == 0  # 验证返回码为0表示成功
      
        # 调用API查询更新后的workflow
        # 循环15秒，等待workflow更新完成  
        i = 0 
        while i < 15:
            sleep(1)
            detail_response = v1_workflow_detail(workflow_id, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
            detail_body = detail_response["body"]
            print(detail_response["body"])
            print("/n")
            i += 1

        # 断言响应包含必要字段
        assert "code" in detail_body
        assert detail_body["code"] == 0  # 验证返回码为0表示成功
        assert "data" in detail_body
        assert "workflow_id" in detail_body["data"]  # workflow_id在data字段中
        # 断言查询出来的workflow与更新的是否一致
        assert update_workflow["nodes"] == detail_body["data"]["workflow_dsl"]["nodes"] , "Updated and queried workflows are not identical"
        
        
        
    except Exception as e:
        error_message = f"Failed to create, query, or invoke workflow: {str(e)}"
        if create_response and "headers" in create_response:
            print(f'Create Response X-Trace-Id: {create_response["headers"].get("X-Trace-Id")}')
            error_message += f"\nCreate Response: {create_response}"
        if detail_response and "headers" in detail_response:
            print(f'Detail Response X-Trace-Id: {detail_response["headers"].get("X-Trace-Id")}')
            error_message += f"\nDetail Response: {detail_response}"
        pytest.fail(error_message)
