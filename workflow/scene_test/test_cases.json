[{"test_name": "编码节点-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_code_node", "description": "code_node", "workflow_file": "workflow/scene_test/code_node.json", "is_async": false, "parameters": {"test1": "test1", "test2": "test2"}}, "expected_execute_status": 2, "expected_output": {"content": ["hun<PERSON>", "yuanqi"]}, "test_type": "sync"}, {"test_name": "分支节点-同时满足多个条件", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_multi_match", "description": "branch_node_multi_match", "workflow_file": "workflow/scene_test/branch_node_multi_match.json", "is_async": false, "parameters": {"sleep_time": 1, "value": "test_value"}}, "expected_execute_status": 2, "expected_output": {"branch1_output": "branch1_executed", "branch2_output": "", "default_output": ""}, "test_type": "sync"}, {"test_name": "分支节点-OR条件操作符-条件1满足", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_or_operator", "description": "branch_node_or_operator", "workflow_file": "workflow/scene_test/branch_node_or_operator.json", "is_async": false, "parameters": {"sleep_time": 1, "condition1": "true", "condition2": "false"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "condition_met", "default_output": ""}, "test_type": "sync"}, {"test_name": "分支节点-OR条件操作符-条件都不满足", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_or_operator_default", "description": "branch_node_or_operator_default", "workflow_file": "workflow/scene_test/branch_node_or_operator.json", "is_async": false, "parameters": {"sleep_time": 1, "condition1": "false", "condition2": "false"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "", "default_output": "default_executed"}, "test_type": "sync"}, {"test_name": "分支节点-OR条件操作符-条件都满足", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_or_operator_both", "description": "branch_node_or_operator_both", "workflow_file": "workflow/scene_test/branch_node_or_operator.json", "is_async": false, "parameters": {"sleep_time": 1, "condition1": "true", "condition2": "true"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "condition_met", "default_output": ""}, "test_type": "sync"}, {"test_name": "分支节点-多层嵌套条件-D和A和B都为true", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_nested_condition_1", "description": "branch_node_nested_condition_1", "workflow_file": "workflow/scene_test/branch_node_nested_condition.json", "is_async": false, "parameters": {"sleep_time": 1, "condA": "true", "condB": "true", "condC": "false", "condD": "true"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "condition_met", "default_output": ""}, "test_type": "sync"}, {"test_name": "分支节点-多层嵌套条件-D和C为true", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_nested_condition_2", "description": "branch_node_nested_condition_2", "workflow_file": "workflow/scene_test/branch_node_nested_condition.json", "is_async": false, "parameters": {"sleep_time": 1, "condA": "false", "condB": "true", "condC": "true", "condD": "true"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "condition_met", "default_output": ""}, "test_type": "sync"}, {"test_name": "分支节点-多层嵌套条件-D为false", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_nested_condition_3", "description": "branch_node_nested_condition_3", "workflow_file": "workflow/scene_test/branch_node_nested_condition.json", "is_async": false, "parameters": {"sleep_time": 1, "condA": "true", "condB": "true", "condC": "false", "condD": "false"}}, "expected_execute_status": 2, "expected_output": {"condition_output": "", "default_output": "default_executed"}, "test_type": "sync"}, {"test_name": "分支节点-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_node", "description": "branch_node", "workflow_file": "workflow/scene_test/branch_node.json", "is_async": false, "parameters": {"sleep_time": 1, "branch": "node3"}}, "expected_execute_status": 2, "expected_output": {"node2_val": "", "node3_val": "output_node_3", "node4_val": ""}, "test_type": "sync"}, {"test_name": "分支节点-只有一个分支", "test_params": {"workspace": "test_workspace", "name": "test_workflow_branch_node_solo", "description": "branch_node", "workflow_file": "workflow/scene_test/branch_node_solo.json", "is_async": false, "parameters": {"sleep_time": 1, "branch": "node3"}}, "expected_execute_status": 2, "expected_output": {"node2_val": "", "node4_val": "output_node_4"}, "test_type": "sync"}, {"test_name": "http节点-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_http_node", "description": "http_node", "workflow_file": "workflow/scene_test/http_node.json", "is_async": false, "parameters": {"fbx_url": "https://hunyuan-base-prod-1258344703.cos.ap-guangzhou.myqcloud.com/text2image/public/3d/3dAnimation/Aj.fbx"}}, "expected_execute_status": 2, "expected_output": {"detect_result1": 0, "detect_result2": 0, "detect_result_data1": null, "detect_result_data2": null}, "test_type": "sync"}, {"test_name": "参数提取-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_extract_node", "description": "extract_node", "workflow_file": "workflow/scene_test/extract_node.json", "is_async": false, "parameters": {"userPrompt": "我需要 10 杯星巴克，非常紧急"}}, "expected_execute_status": 2, "expected_output": {"key1": "星巴克", "key2": 10, "key3": true}, "test_type": "sync"}, {"test_name": "意图节点-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_intent_node", "description": "intent_node", "workflow_file": "workflow/scene_test/intent_node.json", "is_async": false, "parameters": {"userPrompt": "取消昨天的订单"}}, "expected_execute_status": 2, "expected_output": {"node1_val": "", "node2_val": "取消", "node3_val": ""}, "test_type": "sync"}, {"test_name": "意图节点-没有匹配到意图，走默认", "test_params": {"workspace": "test_workspace", "name": "test_workflow_intent_node", "description": "intent_node_default", "route_env": "release-20250314", "base_url": "https://test.hunyuan.woa.com", "workflow_file": "workflow/scene_test/intent_node.json", "is_async": false, "parameters": {"userPrompt": "ssss带点儿啊额外人情味儿"}}, "expected_execute_status": 2, "expected_output": {"node1_val": "", "node2_val": "", "node3_val": "其他"}, "test_type": "sync"}, {"test_name": "variable节点-设置变量（常量&引用）", "test_params": {"workspace": "test_workspace", "name": "test_workflow_variable_node_set", "description": "variable_node", "workflow_file": "workflow/scene_test/variable_node_set.json", "is_async": false, "parameters": {"setdata": "setvar2"}}, "expected_execute_status": 2, "expected_output": {"var1": "", "var2": ""}, "expected_variables": {"data": {"var1": "var1setsuccess", "var2": "setvar2"}, "modify_map": {"var1": true, "var2": true}}, "test_type": "sync"}, {"test_name": "variable节点-del变量（常量&引用）", "test_params": {"workspace": "test_workspace", "name": "test_workflow_variable_node_del", "description": "variable_node", "workflow_file": "workflow/scene_test/variable_node_del.json", "is_async": false, "parameters": {"setdata": "setvar2"}}, "expected_execute_status": 2, "expected_output": {"var1": "", "var2": ""}, "expected_variables": {"data": {"var1": "", "var2": ""}, "modify_map": {"var1": true, "var2": true}}, "test_type": "sync"}, {"test_name": "循环节点-数组循环-数组", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_array", "description": "iter_node_array", "workflow_file": "workflow/scene_test/iter_node_array.json", "is_async": false, "parameters": {"input": [{"name": "a123"}, {"name": "a456"}, {"name": "a789"}, {"name": "atest"}], "input2": [{"name": "b123"}, {"name": "b456"}, {"name": "b789"}, {"name": "btest"}]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1a123b123", "temp_val_1a456b456", "temp_val_1a789b789", "temp_val_1atestbtest"]}, "test_type": "sync"}, {"test_name": "循环节点-条件", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_condition", "description": "iter_node_condition", "workflow_file": "workflow/scene_test/iter_node_condition.json", "is_async": false, "parameters": {}, "debug": true}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1temp1temp2abcd", "temp_val_1temp1temp2temp_val_1temp1temp2abcd"]}, "test_type": "sync"}, {"test_name": "循环节点-固定次数", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_literal", "description": "iter_node_literal", "workflow_file": "workflow/scene_test/iter_node_literal.json", "is_async": false, "parameters": {}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1temp1temp2abcd", "temp_val_1temp1temp2temp_val_1temp1temp2abcd", "temp_val_1temp1temp2temp_val_1temp1temp2temp_val_1temp1temp2abcd"]}, "test_type": "sync"}, {"test_name": "循环节点-数组循环-长度不一致", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_array_unequal", "description": "iter_node_array_unequal", "workflow_file": "workflow/scene_test/iter_node_array_unequal.json", "is_async": false, "parameters": {"input": [{"name": "a123"}, {"name": "a456"}], "input2": [{"name": "b123"}, {"name": "b456"}, {"name": "b789"}, {"name": "btest"}]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1[0]a123b123", "temp_val_1[1]a456b456"]}, "test_type": "sync"}, {"test_name": "循环节点-数组循环-空数组", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_array_empty", "description": "iter_node_array_empty", "workflow_file": "workflow/scene_test/iter_node_array_empty.json", "is_async": false, "parameters": {"input": []}}, "expected_execute_status": 2, "expected_output": {"output": []}, "test_type": "sync"}, {"test_name": "循环节点-数组循环-单元素", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_array_single", "description": "iter_node_array_single", "workflow_file": "workflow/scene_test/iter_node_array_single.json", "is_async": false, "parameters": {"input": [{"name": "single_element"}]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1[0]single_element"]}, "test_type": "sync"}, {"test_name": "HTTP节点-不同请求方法", "test_params": {"workspace": "test_workspace", "name": "test_workflow_http_methods", "description": "http_node_methods", "workflow_file": "workflow/scene_test/http_node_methods.json", "is_async": false, "parameters": {"request_body": "{\"test\": \"data\"}"}}, "expected_execute_status": 2, "expected_output": {"get_status": 200, "post_status": 200, "put_status": 200, "delete_status": 200}, "test_type": "sync"}, {"test_name": "HTTP节点-错误处理", "test_params": {"workspace": "test_workspace", "name": "test_workflow_http_error_handling", "description": "http_node_error_handling", "workflow_file": "workflow/scene_test/http_node_error_handling.json", "is_async": false, "parameters": {}}, "expected_execute_status": 2, "expected_output": {"not_found_status": 404, "server_error_status": 500, "bad_request_status": 400}, "expect_execution_failure": true, "test_type": "sync"}, {"test_name": "HTTP节点-不同内容类型", "test_params": {"workspace": "test_workspace", "name": "test_workflow_http_content_types", "description": "http_node_content_types", "workflow_file": "workflow/scene_test/http_node_content_types.json", "is_async": false, "parameters": {"json_data": "{\"name\": \"test\", \"value\": 123}", "form_data": "name=test&value=123", "text_data": "This is a plain text test"}}, "expected_execute_status": 2, "expected_output": {"json_status": 200, "form_status": 200, "text_status": 200}, "test_type": "sync"}, {"test_name": "HTTP节点-动态URL", "test_params": {"workspace": "test_workspace", "name": "test_workflow_http_dynamic_url", "description": "http_node_dynamic_url", "workflow_file": "workflow/scene_test/http_node_dynamic_url.json", "is_async": false, "parameters": {"base_url": "https://httpbin.org", "endpoint": "get", "param_name": "test", "param_value": "dynamic_url_test"}}, "expected_execute_status": 2, "expected_output": {"status_code": 200}, "test_type": "sync"}, {"test_name": "循环节点-数组循环-复杂数据结构", "test_params": {"workspace": "test_workspace", "name": "test_workflow_iter_node_complex_structure", "description": "iter_node_complex_structure", "workflow_file": "workflow/scene_test/iter_node_complex_structure.json", "is_async": false, "parameters": {"complex_items": [{"id": 1001, "name": "项目Alpha", "active": true, "metadata": {"creator": "张三", "department": "研发部", "priority": "高", "created_at": "2023-05-15"}, "tags": ["AI", "机器学习", "数据分析"], "statistics": {"views": 1250, "downloads": 368, "ratings": [4, 5, 3, 5, 4]}, "sub_components": [{"component_id": "C001", "component_name": "数据预处理模块", "status": "已完成"}, {"component_id": "C002", "component_name": "模型训练模块", "status": "进行中"}]}, {"id": 1002, "name": "项目Beta", "active": false, "metadata": {"creator": "李四", "department": "产品部", "priority": "中", "created_at": "2023-06-20"}, "tags": ["Web", "前端", "UI设计"], "statistics": {"views": 830, "downloads": 215, "ratings": [3, 4, 4, 2]}, "sub_components": [{"component_id": "C003", "component_name": "用户界面", "status": "已完成"}, {"component_id": "C004", "component_name": "后端API", "status": "已完成"}, {"component_id": "C005", "component_name": "数据库设计", "status": "已完成"}]}, {"id": 1003, "name": "项目Gamma", "active": true, "metadata": {"creator": "王五", "department": "测试部", "priority": "低", "created_at": "2023-07-10"}, "tags": ["测试自动化", "质量保证"], "statistics": {"views": 420, "downloads": 95, "ratings": [5, 5, 4]}, "sub_components": []}]}}, "expected_execute_status": 2, "expected_output": {"item_result": [{"project_id": 1001, "project_name": "项目Alpha", "is_active": true, "creator_info": "张三 (研发部)"}, {"project_id": 1002, "project_name": "项目Beta", "is_active": false, "creator_info": "李四 (产品部)"}, {"project_id": 1003}]}, "test_type": "sync"}, {"test_name": "并行节点-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node", "description": "parallel_node", "workflow_file": "workflow/scene_test/parallel_node.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"], "input2": ["val1", "val2", "val3"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2", "temp_val_1_2_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-小批次处理-batch_size=1", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_batch_1", "description": "parallel_node_batch_1", "workflow_file": "workflow/scene_test/parallel_node_batch_1.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"], "input2": ["val1", "val2", "val3"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2", "temp_val_1_2_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-大批次处理-batch_size=5", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_batch_5", "description": "parallel_node_batch_5", "workflow_file": "workflow/scene_test/parallel_node_batch_5.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"], "input2": ["val1", "val2", "val3"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2", "temp_val_1_2_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-动态批次调整", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_dynamic_batch", "description": "parallel_node_dynamic_batch", "workflow_file": "workflow/scene_test/parallel_node_dynamic_batch.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3", "item4", "item5"], "input2": ["val1", "val2", "val3", "val4", "val5"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2", "temp_val_1_2_temp1temp2", "temp_val_1_3_temp1temp2", "temp_val_1_4_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-超大数组测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_large_array", "description": "parallel_node_large_array", "workflow_file": "workflow/scene_test/parallel_node_large_array.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3", "item4", "item5", "item6", "item7", "item8", "item9", "item10", "item11", "item12", "item13", "item14", "item15", "item16", "item17", "item18", "item19", "item20", "item21", "item22", "item23", "item24", "item25", "item26", "item27", "item28", "item29", "item30", "item31", "item32", "item33", "item34", "item35", "item36", "item37", "item38", "item39", "item40", "item41", "item42", "item43", "item44", "item45", "item46", "item47", "item48", "item49", "item50"], "input2": ["val1", "val2", "val3", "val4", "val5", "val6", "val7", "val8", "val9", "val10", "val11", "val12", "val13", "val14", "val15", "val16", "val17", "val18", "val19", "val20", "val21", "val22", "val23", "val24", "val25", "val26", "val27", "val28", "val29", "val30", "val31", "val32", "val33", "val34", "val35", "val36", "val37", "val38", "val39", "val40", "val41", "val42", "val43", "val44", "val45", "val46", "val47", "val48", "val49", "val50"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2", "temp_val_1_2_temp1temp2", "temp_val_1_3_temp1temp2", "temp_val_1_4_temp1temp2", "temp_val_1_5_temp1temp2", "temp_val_1_6_temp1temp2", "temp_val_1_7_temp1temp2", "temp_val_1_8_temp1temp2", "temp_val_1_9_temp1temp2", "temp_val_1_10_temp1temp2", "temp_val_1_11_temp1temp2", "temp_val_1_12_temp1temp2", "temp_val_1_13_temp1temp2", "temp_val_1_14_temp1temp2", "temp_val_1_15_temp1temp2", "temp_val_1_16_temp1temp2", "temp_val_1_17_temp1temp2", "temp_val_1_18_temp1temp2", "temp_val_1_19_temp1temp2", "temp_val_1_20_temp1temp2", "temp_val_1_21_temp1temp2", "temp_val_1_22_temp1temp2", "temp_val_1_23_temp1temp2", "temp_val_1_24_temp1temp2", "temp_val_1_25_temp1temp2", "temp_val_1_26_temp1temp2", "temp_val_1_27_temp1temp2", "temp_val_1_28_temp1temp2", "temp_val_1_29_temp1temp2", "temp_val_1_30_temp1temp2", "temp_val_1_31_temp1temp2", "temp_val_1_32_temp1temp2", "temp_val_1_33_temp1temp2", "temp_val_1_34_temp1temp2", "temp_val_1_35_temp1temp2", "temp_val_1_36_temp1temp2", "temp_val_1_37_temp1temp2", "temp_val_1_38_temp1temp2", "temp_val_1_39_temp1temp2", "temp_val_1_40_temp1temp2", "temp_val_1_41_temp1temp2", "temp_val_1_42_temp1temp2", "temp_val_1_43_temp1temp2", "temp_val_1_44_temp1temp2", "temp_val_1_45_temp1temp2", "temp_val_1_46_temp1temp2", "temp_val_1_47_temp1temp2", "temp_val_1_48_temp1temp2", "temp_val_1_49_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-数组长度不一致", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_array_length_mismatch", "description": "parallel_node_array_length_mismatch", "workflow_file": "workflow/scene_test/parallel_node_array_length_mismatch.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3", "item4", "item5"], "input2": ["val1", "val2"]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_temp1temp2", "temp_val_1_1_temp1temp2"]}, "test_type": "sync"}, {"test_name": "并行节点-数组类型不一致", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_array_type_mismatch", "description": "parallel_node_array_type_mismatch", "workflow_file": "workflow/scene_test/parallel_node_array_type_mismatch.json", "is_async": false, "parameters": {"input": ["string", 123, true, null, {"key": "value"}], "input2": [42.5, false, "text", [], {"num": 999}]}}, "expected_execute_status": 2, "expected_output": {"output": ["temp_val_1_0_string|42.5", "temp_val_1_1_123|false", "temp_val_1_2_true|text", "temp_val_1_3_|[]", "temp_val_1_4_{\"key\":\"value\"}|{\"num\":999}"]}, "test_type": "sync"}, {"test_name": "并行节点-缺失数组参数", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_missing_array", "description": "parallel_node_missing_array", "workflow_file": "workflow/scene_test/parallel_node_missing_array.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"]}}, "expected_execute_status": 3, "expect_execution_failure": true, "test_type": "sync"}, {"test_name": "并行节点-子工作流异常处理", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_sub_workflow_exception", "description": "parallel_node_sub_workflow_exception", "workflow_file": "workflow/scene_test/parallel_node_sub_workflow_exception.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"], "input2": ["val1", "val2", "val3"]}}, "expected_execute_status": 3, "expect_execution_failure": true, "test_type": "sync"}, {"test_name": "并行节点-子工作流超时测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_sub_workflow_timeout", "description": "parallel_node_sub_workflow_timeout", "workflow_file": "workflow/scene_test/parallel_node_sub_workflow_timeout.json", "is_async": false, "parameters": {"input": ["item1", "item2", "item3"], "input2": ["val1", "val2", "val3"]}}, "expected_execute_status": 3, "expect_execution_failure": true, "test_type": "sync"}, {"test_name": "并行节点-空数组处理测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_empty_arrays", "description": "parallel_node_empty_arrays", "workflow_file": "workflow/scene_test/parallel_node_empty_arrays.json", "is_async": false, "parameters": {"input": [], "input2": []}}, "expected_execute_status": 2, "expected_output": {"output": []}, "test_type": "sync"}, {"test_name": "并行节点-单元素数组测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_single_element", "description": "parallel_node_single_element", "workflow_file": "workflow/scene_test/parallel_node_single_element.json", "is_async": false, "parameters": {"input": ["only_one"]}}, "expected_execute_status": 2, "expected_output": {"output": ["single_element_test_0_single_element_only_one"]}, "test_type": "sync"}, {"test_name": "并行节点-batch_size为零测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_batch_zero", "description": "parallel_node_batch_zero", "workflow_file": "workflow/scene_test/parallel_node_batch_zero.json", "is_async": false, "parameters": {"input": ["item1", "item2"], "input2": ["val1", "val2"]}}, "expected_execute_status": 2, "expected_output": {"output": ["batch_zero_test_0_batch_zero_item1", "batch_zero_test_1_batch_zero_item2"]}, "test_type": "sync"}, {"test_name": "并行节点-复杂子工作流测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_complex_subflow", "description": "parallel_node_complex_subflow", "workflow_file": "workflow/scene_test/parallel_node_complex_subflow.json", "is_async": false, "parameters": {"input": ["normal", "special_item", "another", "special_data"]}}, "expected_execute_status": 2, "expected_output": {"output": ["complex_subflow_test_complex_even_processed_normal_0", "complex_subflow_test_complex_special_processed_special_item_1", "complex_subflow_test_complex_even_processed_another_2", "complex_subflow_test_complex_special_processed_special_data_3"]}, "test_type": "sync"}, {"test_name": "异步工作流-查询-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_async_query_node", "description": "async_query_node", "workflow_file": "workflow/scene_test/async_query_node.json", "is_async": true, "parameters": {"data": "response_data", "run_time": 2}}, "expected_execute_status": 1, "expected_output": {"content": "response_data"}, "test_type": "async_query"}, {"test_name": "并行节点-子流程包含异步节点-query", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_with_async_query", "description": "parallel_node_with_async_query", "workflow_file": "workflow/scene_test/parallel_node_with_async_query.json", "is_async": true, "parameters": {"task_list": ["task_data_1", "task_data_2", "task_data_3"]}}, "expected_execute_status": 1, "expected_output": {"data": ["task_0_task_data_1", "task_1_task_data_2", "task_2_task_data_3"]}, "test_type": "async_query"}, {"test_name": "异步工作流-回调-正常场景", "test_params": {"workspace": "test_workspace", "name": "test_workflow_async_callback_node", "description": "async_callback_node", "workflow_file": "workflow/scene_test/async_callback_node.json", "is_async": true, "parameters": {"task_id": "1"}, "callback_task_ids": "1"}, "expected_execute_status": 1, "expected_output": {"content": "response_data"}, "test_type": "async_callback"}, {"test_name": "并行节点-子流程包含异步节点-callback", "test_params": {"workspace": "test_workspace", "name": "test_workflow_parallel_node_with_async_callback", "description": "parallel_node_with_async_callback", "workflow_file": "workflow/scene_test/parallel_node_with_async_callback.json", "is_async": true, "parameters": {"task_id": ["2", "3"]}, "callback_task_ids": ["2", "3"]}, "expected_execute_status": 1, "expected_output": {"res": ["2 task done", "3 task done"]}, "test_type": "async_callback"}, {"test_name": "知识库节点-基础功能测试", "test_params": {"workspace": "test_workspace", "name": "test_workflow_knowledge_node_basic", "description": "knowledge_node_basic", "workflow_file": "workflow/scene_test/knowledge_node_basic.json", "is_async": false, "parameters": {"userPrompt": "日志查询"}}, "expected_execute_status": 2, "expected_output": {"knowledge_result": ""}, "test_type": "sync"}]