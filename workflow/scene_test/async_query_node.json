{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["node3"], "input": null, "output": {"properties": {"data": {"type": "string"}, "run_time": {"type": "integer"}}}}, {"node_id": "node3", "node_type": "ASYNC", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.data", "name": "data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "data", "type": "string"}}, {"id": "body.run_time", "name": "run_time", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "run_time", "type": "integer"}}], "output": {"properties": {"data": {"type": "string"}}}, "async": {"async_type": "QUERY", "timeout": 100, "submit_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com/query", "method": "POST", "headers": [], "query": null, "body": [], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "task_id_json_path": "task_id"}, "query_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com/query", "method": "POST", "headers": [], "query": null, "body": [], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "end_config": {"response_status_jsonpath": "code", "final_state_list": ["0", "1"], "failed_state_list": ["0"]}, "query_interval_time": 1}}}, {"node_id": "END", "node_type": "MESSAGE", "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}, "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "content", "name": "content", "required": true, "input_type": "reference", "reference": {"node_id": "node3", "name": "data", "type": "string"}}]}]}