{"type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": ""}, "next": ["IO_001"], "input": null, "output": {"type": "object", "properties": {"chatHistory": {"type": "string", "description": "历史对话记录，最多30轮"}, "fileUrls": {"type": "array<object>", "description": "包含用户当前轮次上传的文件列表"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "IO_001", "node_type": "IO", "node_meta": {"name": "io节点", "description": "1. 向用户输出消息（包括变量、各节点输出），可自行编排输出内容。2. 接收用户输入，用户输入存储在固定变量UserInput中"}, "next": ["END"], "input": [{"id": "body.prompt", "name": "prompt", "input_type": "literal", "literal": "请输入一个数字："}], "io": {"question": [{"id": "body.prompt", "name": "prompt", "input_type": "literal", "literal": "请输入一个数字：", "literal_type": "string"}], "stream": true}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": ""}, "next": null, "input": [{"name": "response", "input_type": "reference", "reference": {"node_id": "IO_001", "name": "UserResponse", "type": "string"}}], "message": {"streaming_output": true, "message_type": "STRING", "string_format": [{"name": "response", "input_type": "reference", "reference": {"node_id": "IO_001", "name": "UserResponse", "type": "string"}}]}}]}