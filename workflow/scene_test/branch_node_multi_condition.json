{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始节点", "description": "测试同时满足多个分支条件的场景"}, "next": ["node1"], "input": null, "output": {"properties": {"branch_value": {"type": "string"}, "sleep_time": {"type": "integer"}}}}, {"node_id": "node1", "node_type": "BRANCH", "node_meta": {"name": "分支节点", "description": "包含多个可能同时满足的条件"}, "next": [], "input": [{"id": "branch_value", "name": "branch_value", "input_type": "reference", "reference": {"node_id": "START", "name": "branch_value", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "", "if": [{"left": {"id": "branch_value", "name": "branch_value", "input_type": "reference", "reference": {"node_id": "START", "name": "branch_value", "type": "string"}}, "right": {"id": "condition1", "name": "condition1", "input_type": "literal", "literal": "multi_match"}, "op": "eq"}]}, "next": ["path1"]}, {"expr": "", "param": {"operator": "", "if": [{"left": {"id": "branch_value", "name": "branch_value", "input_type": "reference", "reference": {"node_id": "START", "name": "branch_value", "type": "string"}}, "right": {"id": "condition2", "name": "condition2", "input_type": "literal", "literal": "multi_match"}, "op": "eq"}]}, "next": ["path2"]}], "default_next": ["default_path"]}}, {"node_id": "path1", "node_type": "CODE", "node_meta": {"name": "路径1", "description": "第一个条件匹配时执行"}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}], "code": {"code": "async def main(args):\n    import time\n    sleep_time = args.get('sleep_time', 1)\n    time.sleep(sleep_time)\n    print('执行路径1')\n    return {\n        \"path\": \"path1\",\n        \"message\": \"这是路径1的处理结果\"\n    }"}, "output": {"properties": {"path": {"type": "string"}, "message": {"type": "string"}}}}, {"node_id": "path2", "node_type": "CODE", "node_meta": {"name": "路径2", "description": "第二个条件匹配时执行"}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}], "code": {"code": "async def main(args):\n    import time\n    sleep_time = args.get('sleep_time', 1)\n    time.sleep(sleep_time)\n    print('执行路径2')\n    return {\n        \"path\": \"path2\",\n        \"message\": \"这是路径2的处理结果\"\n    }"}, "output": {"properties": {"path": {"type": "string"}, "message": {"type": "string"}}}}, {"node_id": "default_path", "node_type": "CODE", "node_meta": {"name": "默认路径", "description": "没有条件匹配时执行"}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}], "code": {"code": "async def main(args):\n    import time\n    sleep_time = args.get('sleep_time', 1)\n    time.sleep(sleep_time)\n    print('执行默认路径')\n    return {\n        \"path\": \"default\",\n        \"message\": \"这是默认路径的处理结果\"\n    }"}, "output": {"properties": {"path": {"type": "string"}, "message": {"type": "string"}}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束节点", "description": "输出最终结果"}, "next": [], "input": [{"id": "path1_path", "name": "path1_path", "input_type": "reference", "reference": {"node_id": "path1", "name": "path", "type": "string"}}, {"id": "path1_message", "name": "path1_message", "input_type": "reference", "reference": {"node_id": "path1", "name": "message", "type": "string"}}, {"id": "path2_path", "name": "path2_path", "input_type": "reference", "reference": {"node_id": "path2", "name": "path", "type": "string"}}, {"id": "path2_message", "name": "path2_message", "input_type": "reference", "reference": {"node_id": "path2", "name": "message", "type": "string"}}, {"id": "default_path", "name": "default_path", "input_type": "reference", "reference": {"node_id": "default_path", "name": "path", "type": "string"}}, {"id": "default_message", "name": "default_message", "input_type": "reference", "reference": {"node_id": "default_path", "name": "message", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}