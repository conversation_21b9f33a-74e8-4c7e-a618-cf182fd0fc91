{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["prepare_node"], "input": null, "output": {"properties": {"param_value": {"type": "string"}}}}, {"node_id": "prepare_node", "node_type": "EVAL", "node_meta": {"name": "准备查询参数", "description": "准备HTTP请求的查询参数"}, "next": ["http_node"], "input": [{"id": "param_value", "name": "param_value", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "param_value", "type": "string"}}], "output": {"properties": {"query_param": {"type": "string"}}}, "code": {"code": "func main(args map[string]interface{}) (map[string]interface{}, error) {\n    paramValue := args[\"param_value\"].(string)\n    \n    // 构建查询参数\n    queryParam := \"test=\" + paramValue\n    \n    return map[string]interface{}{\n        \"query_param\": queryParam,\n    }, nil\n}"}}, {"node_id": "http_node", "node_type": "HTTP", "node_meta": {"name": "动态URL请求", "description": "使用动态查询参数的HTTP请求"}, "next": ["END"], "input": [{"id": "query.test", "name": "query_param", "required": true, "input_type": "reference", "reference": {"node_id": "prepare_node", "name": "query_param", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/get", "method": "GET", "headers": [{"id": "headers.Accept", "name": "Accept", "required": true, "input_type": "literal", "literal": "application/json"}], "query": [{"id": "query.test", "name": "query_param", "required": true, "input_type": "reference", "reference": {"node_id": "prepare_node", "name": "query_param", "type": "string"}}], "body_type": "json", "retry_times": 0, "timeout": 5000}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "status_code", "required": true, "input_type": "reference", "reference": {"node_id": "http_node", "name": "statusCode", "type": "integer"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}