{"type": "CHATFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": "工作流起始节点"}, "next": ["IO_001"], "input": null, "output": {"type": "object", "properties": {"chatHistory": {"type": "string", "description": "历史对话记录"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "IO_001", "node_type": "IO", "node_meta": {"name": "获取用户输入", "description": "请求用户输入一个数字"}, "next": ["END"], "input": [], "output": {"type": "object", "properties": {"UserResponse": {"type": "string", "description": "用户输入的响应"}}}, "io": {"question": [{"id": "prompt", "name": "prompt", "input_type": "literal", "literal": "请输入一个数字：", "literal_type": "string"}], "stream": true}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": "输出最终结果"}, "next": null, "input": [{"name": "response", "input_type": "reference", "reference": {"node_id": "IO_001", "name": "UserResponse", "type": "string"}}], "message": {"streaming_output": true, "message_type": "STRING", "string_format": [{"name": "response", "input_type": "reference", "reference": {"node_id": "IO_001", "name": "UserResponse", "type": "string"}}]}}]}