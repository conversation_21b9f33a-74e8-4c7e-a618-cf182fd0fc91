'''
Author       : winsonyang 
Date         : 2025-01-20 18:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-07-08 18:14:18
FilePath     : /aigc-api-test/workflow/scene_test/test_workflow_chat_with_user_input.py
Description  : 测试工作流聊天完成与用户输入交互场景

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
import json
import time
import pytest
from typing import Dict, Any, List
from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime

from workflow.api.v1_workflow_create import v1_workflow_create
from workflow.api.v1_workflow_publish import v1_workflow_publish
from workflow.api.v1_workflow_chat_completions import v1_workflow_chat_completions
from workflow.api.v1_workflow_submit_user_outputs import v1_workflow_submit_user_outputs
from workflow.utils.test_utils import load_test_cases, get_test_case_ids
from workflow.utils.structured_logger import workflow_logger


class WorkflowState(Enum):
    """工作流状态枚举"""
    WORKFLOW_STARTED = "workflow_started"
    WAITING_USER_INPUT = "waiting_user_input"
    USER_INPUT_SUBMITTED = "user_input_submitted"
    PROCESSING = "processing"
    STREAMING_CONTENT = "streaming_content"
    WORKFLOW_COMPLETED = "workflow_completed"
    WORKFLOW_ERROR = "workflow_error"


@dataclass
class StateEvent:
    """状态事件"""
    state: WorkflowState
    timestamp: datetime
    data: Dict[str, Any] = field(default_factory=dict)
    message: str = ""


class WorkflowStateCollector:
    """工作流状态收集器"""

    def __init__(self):
        self.states: List[StateEvent] = []
        self.current_state: WorkflowState = None

    def add_state(self, state: WorkflowState, data: Dict[str, Any] = None, message: str = ""):
        """添加状态事件"""
        event = StateEvent(
            state=state,
            timestamp=datetime.now(),
            data=data or {},
            message=message
        )
        self.states.append(event)
        self.current_state = state

        workflow_logger.info(f"🔄 状态变更: {state.value}", {
            "previous_state": self.current_state.value if len(self.states) > 1 else None,
            "new_state": state.value,
            "message": message,
            "data": data
        })

    def get_state_sequence(self) -> List[WorkflowState]:
        """获取状态序列"""
        return [event.state for event in self.states]

    def get_states_by_type(self, state: WorkflowState) -> List[StateEvent]:
        """获取指定类型的状态事件"""
        return [event for event in self.states if event.state == state]

    def validate_state_sequence(self, expected_sequence: List[WorkflowState]) -> bool:
        """验证状态序列"""
        actual_sequence = self.get_state_sequence()
        return actual_sequence == expected_sequence

    def get_state_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        return {
            "total_states": len(self.states),
            "state_sequence": [state.value for state in self.get_state_sequence()],
            "current_state": self.current_state.value if self.current_state else None,
            "user_input_count": len(self.get_states_by_type(WorkflowState.WAITING_USER_INPUT)),
            "processing_count": len(self.get_states_by_type(WorkflowState.PROCESSING))
        }


# 加载测试用例并提取ID
test_cases = load_test_cases("workflow/scene_test/test_cases_IO.json")


def extract_execute_id_from_stream(event_data: Dict[str, Any]) -> str:
    """从流式响应中提取execute_id"""
    if "execute_id" in event_data:
        return event_data["execute_id"]
    
    # 尝试从choices中提取
    if "choices" in event_data:
        for choice in event_data["choices"]:
            if "execute_id" in choice:
                return choice["execute_id"]
            if "delta" in choice and "execute_id" in choice["delta"]:
                return choice["delta"]["execute_id"]
    
    return None


def is_waiting_for_user_input(event_data: Dict[str, Any]) -> bool:
    """判断是否在等待用户输入"""
    workflow_logger.debug(f"Checking event for user_replies: {event_data}")
    if "choices" in event_data:
        for choice in event_data["choices"]:
            # 检查finish_reason是否为user_replies
            if "finish_reason" in choice:
                workflow_logger.debug(f"Found finish_reason: {choice['finish_reason']}")
                if choice["finish_reason"] == "user_replies":
                    return True
            # 检查delta中的finish_reason
            if "delta" in choice and "finish_reason" in choice["delta"]:
                workflow_logger.debug(f"Found delta finish_reason: {choice['delta']['finish_reason']}")
                if choice["delta"]["finish_reason"] == "user_replies":
                    return True
    return False


def extract_user_reply_ids(event_data: Dict[str, Any]) -> List[str]:
    """从事件数据中提取user_reply_id列表"""
    user_reply_ids = []
    if "choices" in event_data:
        for choice in event_data["choices"]:
            if "delta" in choice and "user_replies" in choice["delta"]:
                for user_reply in choice["delta"]["user_replies"]:
                    if "id" in user_reply:
                        user_reply_ids.append(user_reply["id"])
    return user_reply_ids


def run_workflow_with_user_inputs(
    workflow_id: str,
    messages: List[Dict[str, str]],
    user_inputs_sequence: List[Dict[str, Any]],
    route_env: str,
    base_url: str,
    auth_token: str,
    max_iterations: int = 10,
    state_collector: WorkflowStateCollector = None
) -> tuple[Dict[str, Any], str, WorkflowStateCollector]:
    """
    运行工作流并处理用户输入
    
    Args:
        workflow_id: 工作流ID
        messages: 初始消息
        user_repliess_sequence: 用户输入序列
        route_env: 路由环境
        base_url: 基础URL
        auth_token: 认证令牌
        max_iterations: 最大迭代次数
        
    Returns:
        最终的响应结果
    """
    execute_id = None
    current_input_index = 0
    iteration_count = 0
    final_response = None
    all_content = ""  # 收集所有流式内容
    current_user_reply_ids = []  # 存储当前的user_reply_ids

    # 初始化状态收集器
    if state_collector is None:
        state_collector = WorkflowStateCollector()

    # 记录工作流开始状态
    state_collector.add_state(
        WorkflowState.WORKFLOW_STARTED,
        {
            "workflow_id": workflow_id,
            "messages": messages,
            "user_inputs_count": len(user_inputs_sequence)
        },
        "工作流开始执行"
    )
    
    # 首次调用chat completions
    workflow_logger.info("开始执行工作流", {
        "workflow_id": workflow_id,
        "initial_messages": messages
    })
    
    for event_data, headers in v1_workflow_chat_completions(
        workflow_id=workflow_id,
        messages=messages,
        stream=True,
        route_env=route_env,
        base_url=base_url,
        auth_token=auth_token
    ):
        # 收集流式内容
        if "choices" in event_data:
            for choice in event_data["choices"]:
                if "delta" in choice and "content" in choice["delta"]:
                    all_content += choice["delta"]["content"]

        # 调试：打印事件数据
        workflow_logger.info("收到事件数据", {
            "event_data": event_data,
            "has_choices": "choices" in event_data,
            "finish_reason": event_data.get("choices", [{}])[0].get("finish_reason") if event_data.get("choices") else None
        })

        # 提取execute_id
        if not execute_id:
            execute_id = extract_execute_id_from_stream(event_data)
            if execute_id:
                workflow_logger.info("获取到execute_id", {"execute_id": execute_id})
        
        # 检查是否需要用户输入
        if is_waiting_for_user_input(event_data):
            # 提取user_reply_ids
            user_reply_ids = extract_user_reply_ids(event_data)

            # 验证user_reply_ids的有效性
            assert len(user_reply_ids) > 0, f"未获取到有效的user_reply_ids: {event_data}"
            for reply_id in user_reply_ids:
                assert reply_id, f"user_reply_id为空: {user_reply_ids}"

            # 验证当前输入索引在有效范围内
            assert current_input_index < len(user_inputs_sequence), \
                f"用户输入索引超出范围: {current_input_index} >= {len(user_inputs_sequence)}"

            # 更新当前的user_reply_ids（这是关键修复）
            current_user_reply_ids = user_reply_ids

            # 记录等待用户输入状态
            state_collector.add_state(
                WorkflowState.WAITING_USER_INPUT,
                {
                    "execute_id": execute_id,
                    "current_input_index": current_input_index,
                    "user_reply_ids": user_reply_ids,
                    "remaining_inputs": len(user_inputs_sequence) - current_input_index
                },
                f"等待第{current_input_index + 1}个用户输入"
            )

            workflow_logger.info("工作流等待用户输入", {
                "execute_id": execute_id,
                "current_input_index": current_input_index,
                "user_reply_ids": user_reply_ids,
                "remaining_inputs": len(user_inputs_sequence) - current_input_index,
                "validation_passed": True
            })
            break
        
        # 检查是否完成
        if "choices" in event_data:
            for choice in event_data["choices"]:
                if "finish_reason" in choice and choice["finish_reason"] == "stop":
                    # 记录工作流完成状态
                    state_collector.add_state(
                        WorkflowState.WORKFLOW_COMPLETED,
                        {
                            "execute_id": execute_id,
                            "final_response": event_data,
                            "content_length": len(all_content)
                        },
                        "工作流执行完成"
                    )
                    workflow_logger.info("工作流执行完成")
                    return event_data, all_content, state_collector
        
        final_response = event_data
    
    # 处理用户输入循环
    while current_input_index < len(user_inputs_sequence) and iteration_count < max_iterations:
        iteration_count += 1
        
        if not execute_id:
            raise ValueError("未能获取execute_id")
        
        # 获取当前用户输入
        current_user_input = user_inputs_sequence[current_input_index]

        # 使用当前的user_reply_ids（修复：不再使用全局变量）
        if not current_user_reply_ids:
            raise ValueError("未能获取user_reply_id")

        # 构造user_outputs数组格式
        formatted_user_outputs = []
        for user_reply_id in current_user_reply_ids:
            # 从current_user_input中获取对应的输出值
            # 假设current_user_input是{"UserResponse": "value"}格式
            output_value = current_user_input.get("UserResponse", "")

            # 验证用户输入不为空
            assert output_value, f"第{current_input_index + 1}个用户输入为空: {current_user_input}"

            formatted_user_outputs.append({
                "user_reply_id": user_reply_id,
                "output": output_value
            })

        # 验证formatted_user_outputs的格式
        assert len(formatted_user_outputs) > 0, "格式化的用户输出为空"
        for output in formatted_user_outputs:
            assert "user_reply_id" in output, "用户输出缺少user_reply_id字段"
            assert "output" in output, "用户输出缺少output字段"
            assert output["output"], "用户输出的output字段为空"

        # 记录用户输入提交状态
        state_collector.add_state(
            WorkflowState.USER_INPUT_SUBMITTED,
            {
                "execute_id": execute_id,
                "input_index": current_input_index,
                "user_input": current_user_input,
                "formatted_outputs": formatted_user_outputs
            },
            f"提交第{current_input_index + 1}个用户输入"
        )

        workflow_logger.info("提交用户输入", {
            "execute_id": execute_id,
            "input_index": current_input_index,
            "original_user_input": current_user_input,
            "formatted_user_outputs": formatted_user_outputs,
            "validation_passed": True
        })

        # 提交用户输入
        wait_for_input = False
        for event_data, headers in v1_workflow_submit_user_outputs(
            workflow_id=workflow_id,
            execute_id=execute_id,
            user_outputs=formatted_user_outputs,
            stream=True,
            route_env=route_env,
            base_url=base_url,
            auth_token=auth_token
        ):
            # 收集流式内容
            if "choices" in event_data:
                for choice in event_data["choices"]:
                    if "delta" in choice and "content" in choice["delta"]:
                        content_chunk = choice["delta"]["content"]
                        all_content += content_chunk
                        # 记录流式内容状态
                        state_collector.add_state(
                            WorkflowState.STREAMING_CONTENT,
                            {
                                "content_chunk": content_chunk,
                                "total_content_length": len(all_content),
                                "execute_id": execute_id
                            },
                            f"接收流式内容: {len(content_chunk)} 字符"
                        )

            # 检查是否需要更多用户输入
            if is_waiting_for_user_input(event_data):
                wait_for_input = True
                current_input_index += 1

                # 更新user_reply_ids（关键修复：每次都获取最新的）
                current_user_reply_ids = extract_user_reply_ids(event_data)

                # 记录继续等待用户输入状态
                state_collector.add_state(
                    WorkflowState.WAITING_USER_INPUT,
                    {
                        "execute_id": execute_id,
                        "next_input_index": current_input_index,
                        "remaining_inputs": len(user_inputs_sequence) - current_input_index,
                        "user_reply_ids": current_user_reply_ids
                    },
                    f"继续等待第{current_input_index + 1}个用户输入"
                )
                workflow_logger.info("工作流继续等待用户输入", {
                    "next_input_index": current_input_index,
                    "user_reply_ids": current_user_reply_ids
                })
                break

            # 检查是否完成
            if "choices" in event_data:
                for choice in event_data["choices"]:
                    if "finish_reason" in choice and choice["finish_reason"] == "stop":
                        # 记录工作流完成状态
                        state_collector.add_state(
                            WorkflowState.WORKFLOW_COMPLETED,
                            {
                                "execute_id": execute_id,
                                "final_response": event_data,
                                "content_length": len(all_content)
                            },
                            "工作流执行完成"
                        )
                        workflow_logger.info("工作流执行完成")
                        return event_data, all_content, state_collector

            final_response = event_data
        
        # 如果不再等待输入，说明工作流已完成
        if not wait_for_input:
            break
    
    if iteration_count >= max_iterations:
        workflow_logger.warning("达到最大迭代次数", {
            "max_iterations": max_iterations,
            "current_input_index": current_input_index
        })
    
    return final_response, all_content, state_collector


@pytest.mark.parametrize("test_case", test_cases, ids=get_test_case_ids(test_cases))
def test_workflow_chat_with_user_input(test_case, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    测试工作流聊天完成与用户输入交互场景
    
    测试步骤：
    1. 读取测试用例设置的工作流DSL，创建工作流
    2. 调用发布接口，发布工作流，记录工作流version
    3. 调用/openapi/v1/workflow/chat/completions/id接口，运行工作流
    4. 调用/openapi/v1/workflow/submit_user_outputs接口，继续工作流，直到返回结果：choices.finish_reason: "stop"
    """
    test_params = test_case["test_params"]
    workflow_file = test_params.get("workflow_file")
    workflow = test_params.get("workflow")

    # 确保 test_params 包含 workflow 或 workflow_file
    if workflow_file is None and workflow is None:
        pytest.fail("Either workflow or workflow_file must be provided in test case")

    # 验证测试用例的完整性
    user_inputs_sequence = test_params.get("user_inputs_sequence", [])
    assert len(user_inputs_sequence) > 0, f"测试用例 '{test_case['test_name']}' 缺少用户输入序列"

    # 验证每个用户输入的格式
    for i, user_input in enumerate(user_inputs_sequence):
        assert isinstance(user_input, dict), f"第{i+1}个用户输入不是字典格式: {user_input}"
        assert "UserResponse" in user_input, f"第{i+1}个用户输入缺少UserResponse字段: {user_input}"
        assert user_input["UserResponse"], f"第{i+1}个用户输入的UserResponse为空: {user_input}"

    # 验证expected_output的存在
    if "expected_output" in test_case:
        expected_output = test_case["expected_output"]
        workflow_logger.info("测试用例验证通过", {
            "test_name": test_case["test_name"],
            "user_inputs_count": len(user_inputs_sequence),
            "has_expected_output": True,
            "expected_keys": list(expected_output.keys())
        })
    else:
        workflow_logger.warning("测试用例缺少expected_output", {
            "test_name": test_case["test_name"]
        })
    
    try:
        # 步骤1: 创建工作流
        workflow_logger.info("创建工作流", {"test_name": test_case["test_name"]})
        create_response = v1_workflow_create(
            workspace=test_params["workspace"],
            description=test_params["description"],
            name=test_params["name"],
            workflow=workflow,
            workflow_file=workflow_file,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        create_body = create_response["body"]
        assert create_body["code"] == 0, f"创建工作流失败: {create_body}"
        workflow_id = create_body["data"]["workflow_id"]
        workflow_logger.info("工作流创建成功", {"workflow_id": workflow_id})
        
        # 步骤2: 发布工作流
        workflow_logger.info("发布工作流", {"workflow_id": workflow_id})
        publish_response = v1_workflow_publish(
            workflow_id=workflow_id,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        publish_body = publish_response["body"]
        assert publish_body["code"] == 0, f"发布工作流失败: {publish_body}"
        
        # 获取版本信息（如果响应中包含）
        version = publish_body.get("data", {}).get("version", "1")
        workflow_logger.info("工作流发布成功", {
            "workflow_id": workflow_id,
            "version": version
        })
        
        # 等待一下确保发布生效
        time.sleep(1)
        
        # 步骤3-4: 运行工作流并处理用户输入
        messages = test_params.get("messages", [{"role": "user", "content": "开始工作流"}])
        user_inputs_sequence = test_params.get("user_inputs_sequence", [])
        
        # 创建状态收集器
        state_collector = WorkflowStateCollector()

        final_response, all_content, state_collector = run_workflow_with_user_inputs(
            workflow_id=workflow_id,
            messages=messages,
            user_inputs_sequence=user_inputs_sequence,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token,
            state_collector=state_collector
        )
        
        # 验证最终结果
        assert final_response is not None, "未收到最终响应"

        # 验证finish_reason为stop
        if "choices" in final_response:
            finish_reasons = []
            for choice in final_response["choices"]:
                if "finish_reason" in choice:
                    finish_reasons.append(choice["finish_reason"])

            assert "stop" in finish_reasons, f"工作流未正常结束，finish_reasons: {finish_reasons}"

        # 增强的输出验证逻辑
        if "expected_output" in test_case:
            expected_output = test_case["expected_output"]

            # 验证基本的finish_reason
            if "finish_reason" in expected_output:
                assert "stop" in finish_reasons, f"期望finish_reason为{expected_output['finish_reason']}，实际为{finish_reasons}"

            # 验证用户输入是否正确处理
            if "user_inputs_processed" in expected_output:
                expected_inputs = expected_output["user_inputs_processed"]
                actual_inputs = [inp.get("UserResponse", "") for inp in user_inputs_sequence]
                assert actual_inputs == expected_inputs, f"用户输入处理不匹配。期望: {expected_inputs}, 实际: {actual_inputs}"
                workflow_logger.info("✅ 用户输入序列验证通过", {
                    "expected": expected_inputs,
                    "actual": actual_inputs
                })

            # 验证最终输出内容（如果有的话）
            if "output_contains" in expected_output:
                expected_content_parts = expected_output["output_contains"]

                workflow_logger.info("最终输出内容", {"content": all_content})

                # 验证输出内容包含期望的部分
                for expected_part in expected_content_parts:
                    assert expected_part in all_content, f"输出内容缺少期望的部分: '{expected_part}'"

                workflow_logger.info("✅ 输出内容验证通过", {
                    "expected_parts": expected_content_parts,
                    "found_in_content": True
                })

            # 验证用户输入传递的完整性
            if "validate_user_input_flow" in expected_output and expected_output["validate_user_input_flow"]:
                # 验证每个用户输入都被正确处理
                for i, user_input in enumerate(user_inputs_sequence):
                    input_value = user_input.get("UserResponse", "")
                    assert input_value, f"第{i+1}个用户输入为空"
                    workflow_logger.info(f"✅ 用户输入{i+1}验证通过", {"input": input_value})

                workflow_logger.info("✅ 用户输入流程验证通过", {
                    "total_inputs": len(user_inputs_sequence),
                    "all_processed": True
                })

            # 验证状态转换逻辑
            if "validate_state_transitions" in expected_output and expected_output["validate_state_transitions"]:
                actual_states = state_collector.get_state_sequence()

                # 验证必要的状态转换
                assert WorkflowState.WORKFLOW_STARTED in actual_states, "缺少工作流开始状态"
                assert WorkflowState.WORKFLOW_COMPLETED in actual_states, "缺少工作流完成状态"

                # 验证状态转换的逻辑顺序
                start_index = actual_states.index(WorkflowState.WORKFLOW_STARTED)
                end_index = actual_states.index(WorkflowState.WORKFLOW_COMPLETED)
                assert start_index < end_index, "工作流开始状态应该在完成状态之前"

                # 如果有用户输入，验证相关状态
                if len(user_inputs_sequence) > 0:
                    assert WorkflowState.WAITING_USER_INPUT in actual_states, "缺少等待用户输入状态"
                    assert WorkflowState.USER_INPUT_SUBMITTED in actual_states, "缺少用户输入提交状态"

                    # 验证用户输入状态的顺序
                    wait_index = actual_states.index(WorkflowState.WAITING_USER_INPUT)
                    submit_index = actual_states.index(WorkflowState.USER_INPUT_SUBMITTED)
                    assert wait_index < submit_index, "等待用户输入状态应该在提交状态之前"

                workflow_logger.info("✅ 状态转换验证通过", {
                    "state_sequence": [state.value for state in actual_states],
                    "transitions_valid": True
                })

            # 验证状态统计信息
            if "expected_state_counts" in expected_output:
                expected_counts = expected_output["expected_state_counts"]
                state_summary = state_collector.get_state_summary()

                for state_name, expected_count in expected_counts.items():
                    state_enum = WorkflowState(state_name)
                    actual_count = len(state_collector.get_states_by_type(state_enum))
                    assert actual_count == expected_count, \
                        f"状态 {state_name} 出现次数不匹配。期望: {expected_count}, 实际: {actual_count}"

                workflow_logger.info("✅ 状态统计验证通过", {
                    "expected_counts": expected_counts,
                    "state_summary": state_summary
                })

            # 输出状态摘要
            state_summary = state_collector.get_state_summary()
            workflow_logger.info("🔍 工作流状态摘要", state_summary)
        
        workflow_logger.info("测试通过", {
            "test_name": test_case["test_name"],
            "workflow_id": workflow_id
        })
        
    except Exception as e:
        workflow_logger.error("测试失败", {
            "test_name": test_case["test_name"],
            "error": str(e)
        })
        raise