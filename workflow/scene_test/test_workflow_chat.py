'''
Author       : winsonyang 
Date         : 2025-03-11 16:39:31
LastEditors  : winsonyang 
LastEditTime : 2025-07-02 11:14:53
FilePath     : /aigc-api-test/workflow/scene_test/test_workflow_chat.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
import json
import pytest # type: ignore
from workflow.api.v1_workflow_create import v1_workflow_create
from workflow.api.v1_workflow_detail import v1_workflow_detail
from workflow.api.v1_workflow_execution_chat import v1_workflow_execution_chat
from workflow.utils.test_utils import load_test_cases, get_test_case_ids

# 加载测试用例并提取ID
test_cases = load_test_cases("workflow/scene_test/test_cases_chat.json")

@pytest.mark.parametrize("test_case", test_cases, ids=get_test_case_ids(test_cases))
def test_create_and_query_and_invoke_workflow(test_case, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    测试创建workflow后查询新创建的workflow，并断言查询出来的workflow跟创建的是否一致
    """
    test_params = test_case["test_params"]
    workflow_file = test_params.get("workflow_file")
    workflow = test_params.get("workflow")
    
    # 确保 test_params 包含 workflow 或 workflow_file
    if workflow_file is None and workflow is None:
        pytest.fail("Either workflow or workflow_file must be provided in test case")
    
    # 更新test_params中的route_env和base_url
    test_params["route_env"] = workflow_route_env
    test_params["base_url"] = workflow_base_url
    
    # 提取 is_async 和 parameters 参数
    parameters = test_params.pop("parameters", {})
    
    try:
        # 调用API创建workflow
        create_response = v1_workflow_create(workspace=test_params["workspace"],description=test_params["description"],name=test_params["name"],workflow=workflow,workflow_file=workflow_file, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
        create_body = create_response["body"]
        
        # 断言响应包含必要字段
        assert "code" in create_body
        assert create_body["code"] == 0  # 验证返回码为0表示成功
        assert "data" in create_body
        assert "workflow_id" in create_body["data"]  # workflow_id在data字段中
        assert isinstance(create_body["data"]["workflow_id"], str)  # 验证workflow_id是字符串
        assert len(create_body["data"]["workflow_id"]) > 0  # 验证workflow_id不为空
        
        # 获取创建的workflow_id
        workflow_id = create_body["data"]["workflow_id"]
        
        # 打印创建的workflow信息
        print(f'Workflow created successfully! Workflow ID: {workflow_id}')
        
        # 调用API查询workflow
        detail_response = v1_workflow_detail(workflow_id, route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token)
        detail_body = detail_response["body"]
        
        # 断言响应包含必要字段
        assert "code" in detail_body
        assert detail_body["code"] == 0  # 验证返回码为0表示成功
        assert "data" in detail_body
        assert "workflow_id" in detail_body["data"]  # workflow_id在data字段中
        
        # 获取创建和查询的workflow数据
        if workflow_file:
            with open(workflow_file, 'r') as f:
                created_workflow = json.load(f)
        elif workflow:
            created_workflow = workflow
        else:
            pytest.fail("Either workflow or workflow_file must be provided in test case")
        queried_workflow = detail_body["data"]["workflow_dsl"]
        
        # 断言查询出来的workflow与创建的是否一致
        assert created_workflow["nodes"] == queried_workflow["nodes"] , "Created and queried workflows are not identical"
        
        # 打印查询的workflow信息
        print(f'Workflow queried successfully! Workflow ID: {detail_body["data"]["workflow_id"]}')
        
        # 调用API执行workflow chat
        try:
            # 明确传递route_env和base_url参数
            chat_response = v1_workflow_execution_chat(
                workflow_id=workflow_id, 
                route_env=workflow_route_env, 
                base_url=workflow_base_url,
                auth_token=workflow_auth_token,
                parameters=parameters
            )
            
            # 处理SSE流式响应
            full_output = ""
            chunk_count = 0
                
            # 添加调试信息
            print(f"Starting to process SSE stream for workflow_id: {workflow_id}")
            
            for chunk in chat_response:
                chunk_body, chunk_headers = chunk
                chunk_count += 1
                
                # 添加调试信息
                print(f"Received chunk {chunk_count}: {json.dumps(chunk_body)[:100]}...")
                
                # 验证SSE响应头
                assert chunk_headers.get("Content-Type") == "text/event-stream", "Response should be SSE"
                
                # 收集输出
                if "data" in chunk_body and "output" in chunk_body["data"]:
                    full_output += chunk_body["data"]["output"]
                
                # 保存最后一个chunk
                # last_chunk = chunk
            
            # 添加调试信息
            print(f"Received {chunk_count} chunks from SSE stream")

        except Exception as e:
            error_message = f"Failed to invoke workflow: {str(e)}"
            # 这里不能直接检查chat_response的headers，因为它是一个生成器
            pytest.fail(error_message)

    except Exception as e:
        error_message = f"Failed to create, query, or invoke workflow: {str(e)}"
        if 'create_response' in locals() and isinstance(create_response, dict) and "headers" in create_response:
            print(f'Create Response X-Trace-Id: {create_response["headers"].get("X-Trace-Id")}')
            error_message += f"\nCreate Response: {create_response}"
        if 'detail_response' in locals() and isinstance(detail_response, dict) and "headers" in detail_response:
            print(f'Detail Response X-Trace-Id: {detail_response["headers"].get("X-Trace-Id")}')
            error_message += f"\nDetail Response: {detail_response}"
        pytest.fail(error_message)
