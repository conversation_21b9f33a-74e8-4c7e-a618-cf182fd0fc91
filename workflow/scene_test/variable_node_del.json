{"nodes": [{"input": null, "next": ["var1"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START", "output": {"properties": {"setdata": {"type": "string"}}}}, {"input": [{"input_type": "reference", "name": "data2", "reference": {"name": "setdata", "node_id": "START", "type": "string"}, "required": true}, {"input_type": "literal", "name": "data1", "literal": "var1setsuccess", "required": true}], "output": {"properties": {"var1": {"type": "string"}, "var2": {"type": "string"}}}, "node_id": "var1", "node_type": "VARIABLE", "node_meta": {"description": "", "name": ""}, "next": ["END"], "variable": {"actions": [{"action": "del", "variable_name": "var1", "variable_type": "", "value": {"input_type": "literal", "name": "data1", "literal": "var1setsuccess", "required": true, "literal_type": "string"}}, {"action": "del", "variable_name": "var2", "variable_type": "", "value": {"input_type": "reference", "name": "data2", "reference": {"name": "setdata", "node_id": "START", "type": "string"}, "required": true}}]}}, {"input": [{"id": "var1", "input_type": "reference", "name": "var1", "reference": {"name": "var1", "node_id": "var1", "type": "string"}, "required": true}, {"id": "var2", "input_type": "reference", "name": "var2", "reference": {"name": "var2", "node_id": "var1", "type": "string"}, "required": true}], "next": [], "node_id": "END", "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE", "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}}]}