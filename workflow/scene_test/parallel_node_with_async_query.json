{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": ""}, "next": ["140001_929b4bb3-fe49-4584-f77-10be6a626dd4"], "input": null, "output": {"type": "object", "required": ["task_list"], "properties": {"chatHistory": {"type": "string", "description": "历史对话记录，最多30轮"}, "fileUrls": {"type": "array<object>", "description": "包含用户当前轮次上传的文件列表"}, "task_list": {"type": "array<string>", "description": "1"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": ""}, "next": null, "input": [{"id": "data", "name": "data", "input_type": "reference", "reference": {"node_id": "140001_929b4bb3-fe49-4584-f77-10be6a626dd4", "name": "data", "type": "array<string>"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "140001_929b4bb3-fe49-4584-f77-10be6a626dd4", "node_type": "PARALLEL", "node_meta": {"name": "批处理", "description": "通过设定批量运行次数和逻辑，运行批处理体内的任务"}, "next": ["END"], "input": null, "parallel": {"batch_size": 0, "batch_size_fields": {"id": "concurrent_size.concurrentSize", "name": "concurrentSize", "input_type": "literal", "literal": "3", "literal_type": "integer"}, "max_cnt": 0, "max_cnt_fields": {"id": "max_count.maxCount", "name": "maxCount", "input_type": "literal", "literal": "10", "literal_type": "integer"}, "arrays": [{"id": "array.input2", "name": "input2", "input_type": "reference", "reference": {"node_id": "START", "name": "task_list", "type": "array<string>"}}], "parallel_output": [{"id": "parallel_output.data", "name": "data", "input_type": "reference", "reference": {"node_id": "20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97", "name": "key0", "type": "array<string>"}}], "references": [], "sub_graph": {"graph_id": "70cfa587-b58f-44ca-b519-4e20f86667ab", "type": "WORKFLOW", "nodes": [{"node_id": "120001_0783d2e2-9587-471d-0c51-4825f4211a6f", "node_type": "ASYNC", "node_meta": {"name": "异步", "description": "进行异步http接口的调用"}, "next": ["END"], "input": [{"id": "query.body.index", "name": "index", "input_type": "reference", "reference": {"node_id": "20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97", "name": "key0", "type": "string"}}], "output": {"type": "object", "properties": {"data": {"type": "string"}}}, "async": {"async_type": "QUERY", "timeout": 300, "submit_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com:80/query", "method": "POST", "headers": [], "query": null, "body": [], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "task_id_json_path": "task_id"}, "query_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com:80/query", "method": "POST", "headers": [], "query": null, "body": [{"id": "query.body.index", "name": "index", "input_type": "reference", "reference": {"node_id": "20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97", "name": "key0", "type": "string"}}], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "end_config": {"response_status_jsonpath": "code", "final_state_list": ["1", "0"], "failed_state_list": ["0"]}, "query_interval_time": 10}}}, {"node_id": "20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97", "node_type": "CODE", "node_meta": {"name": "代码", "description": "使用python3对入参进行处理"}, "next": ["120001_0783d2e2-9587-471d-0c51-4825f4211a6f"], "input": [{"id": "test_2", "name": "test_2", "input_type": "iter_variable", "reference": {"name": "input2", "type": "string"}}, {"id": "index", "name": "index", "input_type": "iter_variable", "reference": {"name": "index", "type": "integer"}}], "output": {"type": "object", "properties": {"key0": {"type": "string"}, "key1": {"type": "integer"}, "key2": {"type": "array", "items": {"type": "string"}}, "key3": {"type": "object", "properties": {"address": {"type": "string"}, "age": {"type": "integer"}, "name": {"type": "string"}}}}}, "code": {"code": "async def main(args):\n    ret = {\n        \"key0\": \"task_\" + str(args['index']) + \"_\" + str(args['test_2']),\n        \"key1\": 7,\n        \"key2\": ['hunyuan', 'yuanqi'],\n        \"key3\": {\n            'name': 'hunyuan',\n            'age': 1,\n            'address': 'shenzhen'\n        }\n    }\n    print('result is: ', ret)\n    return ret"}}, {"node_id": "START", "node_type": "START", "node_meta": {"name": "批处理", "description": ""}, "next": ["20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97"], "input": null}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "批处理", "description": ""}, "next": null, "input": [{"id": "parallel_output.data", "name": "data", "input_type": "reference", "reference": {"node_id": "20001_f4e0ef5e-bd06-48e9-042d-5354cf5b4f97", "name": "key0", "type": "array<string>"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}}}]}