{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["get_node", "post_node", "put_node", "delete_node"], "input": null, "output": {"properties": {"api_url": {"type": "string"}, "request_body": {"type": "string"}}}}, {"node_id": "get_node", "node_type": "HTTP", "node_meta": {"name": "GET请求", "description": "测试GET请求方法"}, "next": ["END"], "input": [], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/get", "method": "GET", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/json"}], "query": [{"id": "query.test", "name": "test", "required": true, "input_type": "literal", "literal": "get_test"}], "body_type": "json", "retry_times": 1, "timeout": 5000}}, {"node_id": "post_node", "node_type": "HTTP", "node_meta": {"name": "POST请求", "description": "测试POST请求方法"}, "next": ["END"], "input": [{"id": "body.data", "name": "request_body", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "request_body", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/post", "method": "POST", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/json"}], "body": [{"id": "body.data", "name": "request_body", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "request_body", "type": "string"}}], "body_type": "json", "retry_times": 1, "timeout": 5000}}, {"node_id": "put_node", "node_type": "HTTP", "node_meta": {"name": "PUT请求", "description": "测试PUT请求方法"}, "next": ["END"], "input": [{"id": "body.data", "name": "request_body", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "request_body", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/put", "method": "PUT", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/json"}], "body": [{"id": "body.data", "name": "request_body", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "request_body", "type": "string"}}], "body_type": "json", "retry_times": 1, "timeout": 5000}}, {"node_id": "delete_node", "node_type": "HTTP", "node_meta": {"name": "DELETE请求", "description": "测试DELETE请求方法"}, "next": ["END"], "input": [], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/delete", "method": "DELETE", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/json"}], "body_type": "json", "retry_times": 1, "timeout": 5000}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "get_status", "required": true, "input_type": "reference", "reference": {"node_id": "get_node", "name": "statusCode", "type": "integer"}}, {"name": "post_status", "required": true, "input_type": "reference", "reference": {"node_id": "post_node", "name": "statusCode", "type": "integer"}}, {"name": "put_status", "required": true, "input_type": "reference", "reference": {"node_id": "put_node", "name": "statusCode", "type": "integer"}}, {"name": "delete_status", "required": true, "input_type": "reference", "reference": {"node_id": "delete_node", "name": "statusCode", "type": "integer"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}