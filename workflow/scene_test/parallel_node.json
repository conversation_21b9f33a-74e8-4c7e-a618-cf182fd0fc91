{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["def_code_node"], "input": null}, {"node_id": "def_code_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["parallel_node"], "input": [], "output": {"type": "object", "properties": {"temp_val": {"type": "string"}}}, "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"temp_val\": \"temp_val_1\",\n    }\n    return res,nil\n}"}}, {"node_id": "parallel_node", "node_type": "PARALLEL", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "local_variables.last", "name": "last", "required": true, "input_type": "literal", "literal": "abcd", "literal_type": "string"}, {"id": "def_code_node.prefix", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "def_code_node", "name": "temp_val", "type": "string"}}], "parallel": {"batch_size": 3, "max_cnt": 100, "arrays": [{"id": "iter_array.input", "name": "input", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input", "type": "array"}}, {"id": "iter_array.input2", "name": "input2", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input2", "type": "array"}}], "parallel_output": [{"id": "output.output", "name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "code_node", "name": "output", "type": "string"}}], "references": [{"id": "def_code_node", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "def_code_node", "name": "@this", "type": "object"}}], "sub_graph": {"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["code_node"], "input": null}, {"node_id": "code_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["http_node"], "input": [{"id": "input", "name": "input", "required": true, "input_type": "literal", "literal": "temp1", "literal_type": "string"}, {"id": "input2", "name": "input2", "required": true, "input_type": "literal", "literal": "temp2", "literal_type": "string"}, {"id": "start_node.index", "name": "index", "required": true, "input_type": "iter_variable", "reference": {"node_id": "START", "name": "index", "type": "integer"}}, {"id": "code_node.prefix", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "def_code_node.temp_val", "type": "string"}}], "output": {"type": "object", "properties": {"output": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"output\": args[\"prefix\"].(string) + fmt.Sprintf(\"_%v_\",args[\"index\"]) + args[\"input\"].(string) + args[\"input2\"].(string),\n    }\n    return res,nil\n}"}}, {"node_id": "http_node", "node_type": "HTTP", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.1", "name": "b1", "required": true, "input_type": "literal", "literal": "{\"a\":{\"b\":"}, {"id": "body.2", "name": "b2", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input.name", "type": "string"}}, {"id": "body.3", "name": "b3", "required": true, "input_type": "literal", "literal": "}}"}], "output": {"type": "object", "properties": {"body": {"type": "string"}, "headers": {"type": "string"}, "status_code": {"type": "integer"}}}, "http": {"url": "https://www.baidu.com", "method": "GET", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 123456"}], "query": null, "body": [{"id": "body.1", "name": "b1", "required": true, "input_type": "literal", "literal": "{\"a\":{\"b\":"}, {"id": "body.2", "name": "b2", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input.name", "type": "string"}}, {"id": "body.3", "name": "b3", "required": true, "input_type": "literal", "literal": "}}"}], "body_type": "json-string", "timeout": 0, "retry_times": 0}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": null, "input": [{"name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "code_node", "name": "output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "parallel_node", "name": "output", "type": "array"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}