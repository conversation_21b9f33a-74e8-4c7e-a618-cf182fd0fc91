{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["not_found_node", "server_error_node", "bad_request_node"], "input": null}, {"node_id": "not_found_node", "node_type": "HTTP", "node_meta": {"name": "404错误", "description": "测试404错误处理"}, "next": ["END"], "input": [], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/status/404", "method": "GET", "headers": [{"id": "headers.Accept", "name": "Accept", "required": true, "input_type": "literal", "literal": "application/json"}], "body_type": "json", "retry_times": 0, "timeout": 5000}}, {"node_id": "server_error_node", "node_type": "HTTP", "node_meta": {"name": "服务器错误", "description": "测试500错误处理"}, "next": ["END"], "input": [], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/status/500", "method": "GET", "headers": [{"id": "headers.Accept", "name": "Accept", "required": true, "input_type": "literal", "literal": "application/json"}], "body_type": "json", "retry_times": 0, "timeout": 5000}}, {"node_id": "bad_request_node", "node_type": "HTTP", "node_meta": {"name": "错误请求", "description": "测试400错误处理"}, "next": ["END"], "input": [], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/status/400", "method": "GET", "headers": [{"id": "headers.Accept", "name": "Accept", "required": true, "input_type": "literal", "literal": "application/json"}], "body_type": "json", "retry_times": 0, "timeout": 5000}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "not_found_status", "required": true, "input_type": "reference", "reference": {"node_id": "not_found_node", "name": "statusCode", "type": "integer"}}, {"name": "server_error_status", "required": true, "input_type": "reference", "reference": {"node_id": "server_error_node", "name": "statusCode", "type": "integer"}}, {"name": "bad_request_status", "required": true, "input_type": "reference", "reference": {"node_id": "bad_request_node", "name": "statusCode", "type": "integer"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}