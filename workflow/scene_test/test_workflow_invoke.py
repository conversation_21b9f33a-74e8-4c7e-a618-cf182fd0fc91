# -*- coding: utf-8 -*-

'''
Author       : winsonyang
Date         : 2025-03-11 16:39:31
LastEditors  : winsonyang 
LastEditTime : 2025-07-14 16:37:36
FilePath     : /aigc-api-test/workflow/scene_test/test_workflow_invoke.py
Description  : 统一的工作流测试文件，合并同步、异步查询、异步回调测试功能

Copyright (c) 2025 by Tencent, All Rights Reserved.
'''
import json
import requests
import time
from time import sleep
import pytest  # type: ignore
from workflow.api.v1_workflow_create import v1_workflow_create
from workflow.api.v1_workflow_detail import v1_workflow_detail
from workflow.api.v1_workflow_execution_invoke import v1_workflow_execution_invoke
from workflow.api.v1_workflow_execution_detail import v1_workflow_execution_detail
from workflow.utils.test_utils import load_test_cases, get_test_case_ids
from workflow.utils.workflow_compare import compare_workflows, format_workflow_differences
from workflow.utils.error_handling import workflow_logger
from workflow.utils.test_executor import WorkflowTestExecutor

# 常量定义
EXECUTION_STATUS = {
    'INIT': 0,
    'RUNNING': 1,
    'SUCCESS': 2,
    'FAILED': 3,
    'CANCELLED': 4,
    'WAITING_CALLBACK': 5,
    'RECOVERING': 6
}

# 重试配置
RETRY_CONFIG = {
    'MAX_RETRIES': 5,
    'INITIAL_WAIT': 1,
    'ASYNC_QUERY_WAIT': 10,
    'CALLBACK_WAIT': 3
}

# 加载统一的测试用例
test_cases = load_test_cases("workflow/scene_test/test_cases.json")

def create_workflow_helper(test_params, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    通用的工作流创建函数
    
    Args:
        test_params: 测试参数
        workflow_route_env: 路由环境
        workflow_base_url: 基础URL
        workflow_auth_token: 认证令牌
    
    Returns:
        tuple: (workflow_id, create_response)
    """
    workflow_file = test_params.get("workflow_file")
    workflow = test_params.get("workflow")
    
    # 确保 test_params 包含 workflow 或 workflow_file
    if workflow_file is None and workflow is None:
        raise ValueError("Either workflow or workflow_file must be provided in test case")
    
    workflow_logger.info(f"Creating workflow with params: workspace={test_params['workspace']}, "
          f"description={test_params['description']}, name={test_params['name']}")
    
    # 调用API创建workflow
    create_response = v1_workflow_create(
        workspace=test_params["workspace"],
        description=test_params["description"],
        name=test_params["name"],
        workflow=workflow,
        workflow_file=workflow_file,
        route_env=workflow_route_env,
        base_url=workflow_base_url,
        auth_token=workflow_auth_token
    )
    
    create_body = create_response["body"]
    
    # 断言响应包含必要字段
    assert "code" in create_body
    assert create_body["code"] == 0, f"工作流创建失败，错误码: {create_body['code']}"
    assert "data" in create_body
    assert "workflow_id" in create_body["data"]
    assert isinstance(create_body["data"]["workflow_id"], str)
    assert len(create_body["data"]["workflow_id"]) > 0
    
    workflow_id = create_body["data"]["workflow_id"]
    workflow_logger.info(f'Workflow created successfully! Workflow ID: {workflow_id}')
    
    return workflow_id, create_response

def query_workflow_helper(workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    通用的工作流查询函数
    
    Args:
        workflow_id: 工作流ID
        workflow_route_env: 路由环境
        workflow_base_url: 基础URL
        workflow_auth_token: 认证令牌
    
    Returns:
        dict: 查询响应
    """
    workflow_logger.info(f"Querying workflow with params: workflow_id={workflow_id}")
    
    detail_response = v1_workflow_detail(
        workflow_id,
        route_env=workflow_route_env,
        base_url=workflow_base_url,
        auth_token=workflow_auth_token
    )
    
    detail_body = detail_response["body"]
    
    # 断言响应包含必要字段
    assert "code" in detail_body
    assert detail_body["code"] == 0, f"工作流查询失败，错误码: {detail_body['code']}"
    assert "data" in detail_body
    assert "workflow_id" in detail_body["data"]
    
    workflow_logger.info(f'Workflow queried successfully! Workflow ID: {detail_body["data"]["workflow_id"]}')
    
    return detail_response

def invoke_workflow_helper(workflow_id, test_case, test_params, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    通用的工作流调用函数，包含完整的失败处理逻辑
    
    Args:
        workflow_id: 工作流ID
        test_case: 完整的测试用例，用于检查expect_execution_failure
        test_params: 测试参数
        workflow_route_env: 路由环境
        workflow_base_url: 基础URL
        workflow_auth_token: 认证令牌
    
    Returns:
        invoke_response
    """
    # 使用pop来提取参数，和原始文件保持一致
    is_async = test_params.pop("is_async", False)
    parameters = test_params.pop("parameters", {})
    
    workflow_logger.info(f"Invoking workflow with params: workflow_id={workflow_id}, "
          f"is_async={is_async}, parameters={parameters}")
    
    # 检查是否期望工作流执行失败
    expect_execution_failure = test_case.get("expect_execution_failure", False)
    
    try:
        invoke_response = v1_workflow_execution_invoke(
            workflow_id,
            is_async=is_async,
            parameters=parameters,
            route_env=workflow_route_env,
            base_url=workflow_base_url,
            auth_token=workflow_auth_token
        )
        
        workflow_logger.info(invoke_response)
        invoke_body = invoke_response["body"]
        
        # 如果期望工作流执行失败，但实际成功了，则测试失败
        if expect_execution_failure:
            assert invoke_body["code"] != 0, "工作流执行应该失败，但实际成功了"
            workflow_logger.info(f"工作流执行失败，符合预期。错误码: {invoke_body['code']}")
            return is_async, parameters, invoke_response
        else:
            # 断言响应包含必要字段
            assert "code" in invoke_body
            assert invoke_body["code"] == 0, f"工作流调用失败，错误码: {invoke_body['code']}"
            assert "data" in invoke_body
            assert "execute_id" in invoke_body["data"]
            assert isinstance(invoke_body["data"]["execute_id"], str)
            
            workflow_logger.info(f'Workflow invoked successfully! Execute ID: {invoke_body["data"]["execute_id"]}')
            
            return is_async, parameters, invoke_response
    
    except Exception as e:
        # 如果期望工作流执行失败，则捕获异常并继续
        if expect_execution_failure:
            workflow_logger.info(f"工作流执行失败，符合预期。异常: {str(e)}")
            # 创建一个模拟的失败响应
            mock_response = {
                "body": {"code": -1, "message": str(e)},
                "headers": {}
            }
            return is_async, parameters, mock_response
        else:
            # 如果不期望工作流执行失败，则重新抛出异常
            raise

def wait_for_status_change(workflow_id, execute_id, target_statuses, workflow_route_env, workflow_base_url, workflow_auth_token, max_retries=None, wait_time=None):
    """
    等待工作流状态变化到目标状态
    
    Args:
        workflow_id: 工作流ID
        execute_id: 执行ID
        target_statuses: 目标状态列表
        max_retries: 最大重试次数
        wait_time: 初始等待时间
    
    Returns:
        tuple: (final_status, response)
    """
    # 导入指标收集器
    try:
        from workflow.utils.test_metrics import get_metrics_collector
        metrics_collector = get_metrics_collector()
    except ImportError:
        metrics_collector = None
    
    if max_retries is None:
        max_retries = RETRY_CONFIG['MAX_RETRIES']
    if wait_time is None:
        wait_time = RETRY_CONFIG['INITIAL_WAIT']
    
    retry_count = 0
    start_time = time.time()
    first_error = None
    trace_id = None
    
    while retry_count < max_retries:
        api_start = time.time()
        response = v1_workflow_execution_detail(
            workflow_id, execute_id=execute_id,
            route_env=workflow_route_env, base_url=workflow_base_url, auth_token=workflow_auth_token
        )
        api_duration = time.time() - api_start
        
        # 获取trace_id
        if not trace_id and "headers" in response:
            trace_id = response["headers"].get("X-Trace-Id")
        
        # 检查响应
        if response["body"]["code"] != 0:
            error_code = response["body"]["code"]
            error_msg = response["body"].get("message", f"错误码: {error_code}")
            
            # 记录第一次错误
            if not first_error:
                first_error = (error_code, error_msg)
            
            # 记录指标
            if metrics_collector:
                metrics_collector.record_api_call(
                    api_name="workflow_execution_detail",
                    success=False,
                    retry_count=retry_count,
                    duration=api_duration,
                    error_code=error_code,
                    error_msg=error_msg,
                    trace_id=trace_id,
                    workflow_id=workflow_id,
                    execute_id=execute_id
                )
            
            # 仍然使用assert保持测试行为不变
            assert False, f"查询执行状态失败，错误码: {error_code}, 错误信息: {error_msg}"
        
        current_status = response["body"]["data"]["execute_status"]
        workflow_logger.info(f"当前执行状态: {current_status} (尝试 {retry_count + 1}/{max_retries})")
        
        if current_status in target_statuses:
            # 记录成功的调用
            total_duration = time.time() - start_time
            if metrics_collector:
                metrics_collector.record_api_call(
                    api_name="workflow_execution_detail",
                    success=True,
                    retry_count=retry_count,
                    duration=total_duration,
                    error_code=first_error[0] if first_error else None,
                    error_msg=first_error[1] if first_error else None,
                    trace_id=trace_id,
                    workflow_id=workflow_id,
                    execute_id=execute_id,
                    final_status=current_status
                )
            return current_status, response
        
        retry_count += 1
        if retry_count < max_retries:
            actual_wait = wait_time * retry_count
            workflow_logger.info(f"等待{actual_wait}秒后重试...")
            sleep(actual_wait)
    
    # 如果达到最大重试次数仍未达到目标状态
    total_duration = time.time() - start_time
    if metrics_collector:
        metrics_collector.record_api_call(
            api_name="workflow_execution_detail",
            success=False,  # 未达到目标状态视为失败
            retry_count=retry_count,
            duration=total_duration,
            error_code=first_error[0] if first_error else None,
            error_msg=first_error[1] if first_error else "未达到目标状态",
            trace_id=trace_id,
            workflow_id=workflow_id,
            execute_id=execute_id,
            timeout=True
        )
    
    return current_status, response

def manual_callback(base_url, task_id, auth_token=None):
    """
    手动触发异步任务回调
    
    Args:
        base_url: API基础URL
        task_id: 任务ID，可以是单个ID字符串或ID数组
        auth_token: 认证令牌
    
    Returns:
        回调请求的响应列表
    """
    import time
    
    # 导入指标收集器
    try:
        from workflow.utils.test_metrics import get_metrics_collector
        metrics_collector = get_metrics_collector()
    except ImportError:
        metrics_collector = None
    
    callback_url = f"{base_url}/openapi/v1/workflow/async/callback"
    headers = {"Content-Type": "application/json"}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    # 如果task_id不是列表，转换为列表
    if not isinstance(task_id, list):
        task_id_list = [task_id]
    else:
        task_id_list = task_id
    
    workflow_logger.info(f"==================== 开始手动回调 ====================")
    workflow_logger.info(f"回调URL: {callback_url}")
    workflow_logger.info(f"任务ID列表: {task_id_list}")
    workflow_logger.info(f"请求头: {headers}")
    
    responses = []
    for index, tid in enumerate(task_id_list, 1):
        workflow_logger.info(f"---------- 处理第 {index}/{len(task_id_list)} 个回调 ----------")
        
        payload = {
            "task_id": tid,
            "body": "{\n  \"code\": \"1\"  \n,  \"data\": \"response_data\" \n }"
        }
        
        workflow_logger.info(f"Task ID: {tid}")
        workflow_logger.info(f"请求体: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        start_time = time.time()
        trace_id = None
        try:
            workflow_logger.info(f"发送POST请求到: {callback_url}")
            response = requests.post(callback_url, headers=headers, json=payload)
            end_time = time.time()
            request_duration = round((end_time - start_time) * 1000, 2)  # 转换为毫秒
            
            # 获取trace_id
            trace_id = response.headers.get("X-Trace-Id", response.headers.get("X-Traceid"))
            
            workflow_logger.info(f"请求耗时: {request_duration}ms")
            workflow_logger.info(f"响应状态码: {response.status_code}")
            workflow_logger.info(f"响应头: {dict(response.headers)}")
            workflow_logger.info(f"响应内容: {response.text}")
            
            # 尝试解析JSON响应
            try:
                response_json = response.json()
                workflow_logger.info(f"响应JSON解析: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
            except:
                workflow_logger.info("响应内容无法解析为JSON")
            
            # 记录指标
            if metrics_collector:
                success = response.status_code < 400
                metrics_collector.record_api_call(
                    api_name="workflow_async_callback",
                    success=success,
                    retry_count=0,  # 回调通常不重试
                    duration=request_duration / 1000,  # 转换回秒
                    error_code=response.status_code if not success else None,
                    error_msg=response.text if not success else None,
                    trace_id=trace_id,
                    task_id=tid
                )
            
            responses.append({
                "task_id": tid,
                "status_code": response.status_code, 
                "response": response.text,
                "request_duration_ms": request_duration,
                "response_headers": dict(response.headers)
            })
            
            workflow_logger.info(f"✅ Task ID {tid} 回调成功")
            
        except Exception as e:
            end_time = time.time()
            request_duration = round((end_time - start_time) * 1000, 2)
            
            workflow_logger.error(f"❌ Task ID {tid} 回调失败")
            workflow_logger.error(f"请求耗时: {request_duration}ms")
            workflow_logger.error(f"错误详情: {str(e)}")
            workflow_logger.error(f"错误类型: {type(e).__name__}")
            
            # 记录失败的指标
            if metrics_collector:
                metrics_collector.record_api_call(
                    api_name="workflow_async_callback",
                    success=False,
                    retry_count=0,
                    duration=request_duration / 1000,
                    error_code=None,  # 网络错误没有HTTP状态码
                    error_msg=str(e),
                    trace_id=trace_id,
                    task_id=tid,
                    error_type=type(e).__name__
                )
            
            responses.append({
                "task_id": tid,
                "status_code": None, 
                "error": str(e),
                "error_type": type(e).__name__,
                "request_duration_ms": request_duration
            })
    
    workflow_logger.info(f"==================== 回调完成统计 ====================")
    success_count = len([r for r in responses if "error" not in r])
    failure_count = len([r for r in responses if "error" in r])
    workflow_logger.info(f"总计: {len(responses)} 个回调")
    workflow_logger.info(f"成功: {success_count} 个")
    workflow_logger.info(f"失败: {failure_count} 个")
    workflow_logger.info(f"成功率: {(success_count/len(responses)*100):.1f}%")
    
    # 如果原始输入是单个ID，返回单个响应；如果是数组，返回响应数组
    return responses[0] if not isinstance(task_id, list) else responses

def handle_sync_execution(test_case, invoke_response, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    处理同步执行逻辑
    """
    invoke_body = invoke_response["body"]
    expected_execute_status = test_case["expected_execute_status"]
    expected_output = test_case.get("expected_output")

    # 检查是否期望工作流执行失败
    expect_execution_failure = test_case.get("expect_execution_failure", False)

    if expect_execution_failure:
        # 期望失败的情况，在invoke_workflow_helper中已经处理
        return

    execute_status = invoke_body["data"].get("execute_status")

    # 增加轮询逻辑：如果同步调用的期望状态不是最终成功状态，则轮询等待
    if execute_status != expected_execute_status:
        workflow_logger.info(f"同步调用状态({execute_status})与期望({expected_execute_status})不符，启动轮询等待...")
        workflow_id = invoke_body["data"]["workflow_id"]
        execute_id = invoke_body["data"]["execute_id"]

        target_statuses = [EXECUTION_STATUS['SUCCESS'], EXECUTION_STATUS['FAILED']]
        final_status, final_response = wait_for_status_change(
            workflow_id, execute_id, target_statuses,
            workflow_route_env, workflow_base_url, workflow_auth_token
        )

        # 使用轮询后的最终状态和响应进行后续断言
        execute_status = final_status
        invoke_response = final_response
        invoke_body = invoke_response["body"]

    assert execute_status == expected_execute_status, \
        f"最终执行状态 ({execute_status}) 与期望状态 ({expected_execute_status}) 不符"

    # 新增：检查工作流执行结果码
    data_code = invoke_body["data"].get("code", 0)
    data_message = invoke_body["data"].get("message", "")
    assert data_code == 0, f"工作流执行失败，结果码: {data_code}, 消息: {data_message}"

    # 验证输出
    if expected_output is not None:
        assert invoke_body["data"]["output"] == expected_output
    
    # 检查variables字段 - 和原始文件保持一致
    if "variables" in invoke_body["data"] and invoke_body["data"]["variables"]["data"]:
        expected_variables = test_case.get("expected_variables")
        if expected_variables:
            assert invoke_body["data"]["variables"] == expected_variables

def handle_async_query_execution(test_case, invoke_response, workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    处理异步查询执行逻辑
    """
    invoke_body = invoke_response["body"]
    expected_execute_status = test_case["expected_execute_status"]
    expected_output = test_case["expected_output"]

    # 断言初始状态
    assert invoke_body["data"]["execute_status"] == expected_execute_status

    execute_id = invoke_body["data"]["execute_id"]

    # 等待异步执行完成
    final_status, final_response = wait_for_status_change(
        workflow_id, execute_id,
        [EXECUTION_STATUS['SUCCESS'], EXECUTION_STATUS['FAILED']],
        workflow_route_env, workflow_base_url, workflow_auth_token,
        max_retries=20,  # 增加重试次数以适应长时间运行的测试
        wait_time=RETRY_CONFIG['INITIAL_WAIT']  # 使用较短的轮询间隔
    )

    # 断言最终状态
    assert final_status == EXECUTION_STATUS['SUCCESS'], \
        f"工作流最终状态与预期不符. 实际状态: {final_status}, 预期状态: {EXECUTION_STATUS['SUCCESS']}"

    # 新增：检查工作流执行结果码
    final_body = final_response["body"]
    data_code = final_body["data"].get("code", 0)
    data_message = final_body["data"].get("message", "")
    assert data_code == 0, f"工作流执行失败，结果码: {data_code}, 消息: {data_message}"

    # 验证输出
    final_output = final_response["body"]["data"].get("output", {})
    assert final_output == test_case["expected_output"], \
        f"工作流输出与预期不符. 实际输出: {final_output}, 预期输出: {test_case['expected_output']}"

def handle_async_callback_execution(test_case, invoke_response, workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    处理异步回调执行逻辑
    """
    invoke_body = invoke_response["body"]
    expected_execute_status = test_case.get("expected_execute_status", EXECUTION_STATUS['RUNNING'])
    
    # 断言初始状态
    assert invoke_body["data"]["execute_status"] == expected_execute_status
    
    execute_id = invoke_body["data"]["execute_id"]
    
    # 等待状态变为等待回调
    final_status, response = wait_for_status_change(
        workflow_id, execute_id, [EXECUTION_STATUS['WAITING_CALLBACK']],
        workflow_route_env, workflow_base_url, workflow_auth_token
    )
    
    assert final_status == EXECUTION_STATUS['WAITING_CALLBACK'], f"Expected status to be 5 (等待回调), but got {final_status}"
    workflow_logger.info("⚠️ 工作流正在等待回调（状态=5）")
    
    # 手动触发回调，支持从测试用例中读取task_id参数
    callback_task_ids = test_case["test_params"].get("callback_task_ids", "q123123123")
    
    sleep(5)

    callback_response = manual_callback(workflow_base_url, callback_task_ids, workflow_auth_token)
    workflow_logger.info(f"手动回调响应: {callback_response}")
    
    # 等待回调处理完成
    workflow_logger.info(f"等待{RETRY_CONFIG['CALLBACK_WAIT']}秒后查询回调处理结果...")
    sleep(RETRY_CONFIG['CALLBACK_WAIT'])
    
    # 等待最终状态变为成功
    final_status, final_response = wait_for_status_change(
        workflow_id, execute_id, [EXECUTION_STATUS['SUCCESS']],
        workflow_route_env, workflow_base_url, workflow_auth_token
    )
    
    assert final_status == EXECUTION_STATUS['SUCCESS'], f"Expected final status to be 2 (成功) after callback, but got {final_status}"

    # 新增：检查工作流执行结果码
    final_body = final_response["body"]
    data_code = final_body["data"].get("code", 0)
    data_message = final_body["data"].get("message", "")
    assert data_code == 0, f"工作流执行失败，结果码: {data_code}, 消息: {data_message}"

    # 验证输出
    expected_output = test_case.get("expected_output")
    if expected_output == "__DYNAMIC_VALIDATION__":
        # 动态验证输出内容
        final_output = final_response["body"]["data"].get("output", {})
        workflow_logger.info(f"动态验证模式，实际输出: {final_output}")
        
        # 针对并行节点异步回调测试用例的特殊验证逻辑
        if test_case["test_name"] == "并行节点-子流程包含异步节点-callback":
            # 验证输出结构
            assert "res" in final_output, f"输出中缺少res字段: {final_output}"
            res_output = final_output["res"]
            assert isinstance(res_output, list), f"res字段应该是数组: {res_output}"
            assert len(res_output) == 2, f"res数组长度应该是2: {len(res_output)}"
            
            # 验证每个输出项的格式
            callback_task_ids = test_case["test_params"].get("callback_task_ids", [])
            if isinstance(callback_task_ids, list) and len(callback_task_ids) == 2:
                expected_patterns = [f"{task_id} task done" for task_id in callback_task_ids]
                for i, actual_output in enumerate(res_output):
                    assert actual_output == expected_patterns[i], \
                        f"输出项 {i} 不匹配. 实际: {actual_output}, 期望: {expected_patterns[i]}"
                workflow_logger.info(f"✅ 动态验证通过，输出匹配预期格式")
            else:
                workflow_logger.warning(f"⚠️ 无法获取有效的callback_task_ids进行详细验证: {callback_task_ids}")
    elif expected_output is not None:
        # 静态验证输出内容
        final_output = final_response["body"]["data"].get("output", {})
        assert final_output == expected_output, \
            f"工作流输出与预期不符. 实际输出: {final_output}, 预期输出: {expected_output}"

def verify_workflow_consistency(test_case, detail_response):
    """
    验证工作流一致性
    """
    test_params = test_case["test_params"]
    workflow_file = test_params.get("workflow_file")
    workflow = test_params.get("workflow")
    
    # 获取创建和查询的workflow数据
    if workflow_file:
        with open(workflow_file, 'r') as f:
            created_workflow = json.load(f)
    elif workflow:
        created_workflow = workflow
    else:
        raise ValueError("Either workflow or workflow_file must be provided in test case")
    
    queried_workflow = detail_response["body"]["data"]["workflow_dsl"]
    
    workflow_logger.info(f'Created Workflow: {created_workflow}')
    workflow_logger.info(f'Queried Workflow: {queried_workflow}')
    
    # 使用智能比较函数比较工作流
    is_consistent, differences = compare_workflows(created_workflow, queried_workflow)
    
    if not is_consistent:
        diff_report = format_workflow_differences(differences)
        workflow_logger.info(diff_report)
        
        # 如果test_params中有skip_workflow_check=true，则只打印差异但不导致测试失败
        if not test_params.get("skip_workflow_check", False):
            critical_differences = [diff for diff in differences if "关键差异" in diff]
            if critical_differences:
                assert False, f"工作流存在关键差异:\n{diff_report}"

def format_error_message(error, responses):
    """
    统一的错误消息格式化
    """
    error_message = f"Failed to create, query, or invoke workflow: {str(error)}"
    
    for response_name, response in responses.items():
        if response and "headers" in response:
            trace_id = response["headers"].get("X-Trace-Id")
            workflow_logger.info(f'{response_name} Response X-Trace-Id: {trace_id}')
            error_message += f"\n{response_name} Response: {response}"
    
    return error_message


def _verify_workflow_consistency_by_type(test_case, test_params, detail_response):
    """根据测试类型验证工作流一致性"""
    test_type = test_case.get("test_type", "sync")
    
    if test_type == "sync":
        verify_workflow_consistency(test_case, detail_response)
    else:
        # 对异步类型进行基本的结构检查
        created_workflow_file = test_params.get("workflow_file")
        if created_workflow_file:
            with open(created_workflow_file, 'r') as f:
                created_workflow = json.load(f)
            queried_workflow = detail_response["body"]["data"]["workflow_dsl"]
            assert len(created_workflow["nodes"]) == len(queried_workflow["nodes"]), "Node count mismatch"
            assert queried_workflow["type"] == "WORKFLOW", "Workflow type mismatch"


def _handle_execution_by_type(test_case, invoke_response, workflow_id, 
                            workflow_route_env, workflow_base_url, workflow_auth_token):
    """根据测试类型处理执行结果"""
    test_type = test_case.get("test_type", "sync")
    
    if test_type == "sync":
        handle_sync_execution(test_case, invoke_response, workflow_route_env, workflow_base_url, workflow_auth_token)
    elif test_type == "async_query":
        handle_async_query_execution(test_case, invoke_response, workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token)
    elif test_type == "async_callback":
        handle_async_callback_execution(test_case, invoke_response, workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token)
    else:
        raise ValueError(f"Unsupported test_type: {test_type}")


@pytest.mark.parametrize("test_case", test_cases, ids=get_test_case_ids(test_cases))
def test_workflow_execution(test_case, workflow_route_env, workflow_base_url, workflow_auth_token):
    """
    统一的工作流测试，根据test_case中的test_type自动选择执行策略
    """
    # 创建测试执行器
    executor = WorkflowTestExecutor(
        route_env=workflow_route_env,
        base_url=workflow_base_url,
        auth_token=workflow_auth_token
    )
    
    # 准备测试参数（处理随机数占位符）
    test_params = executor.prepare_test_parameters(test_case["test_params"])
    
    # 更新test_params中的route_env和base_url
    test_params["route_env"] = workflow_route_env
    test_params["base_url"] = workflow_base_url
    
    workflow_logger.info(f"执行测试: {test_case['test_name']} (类型: {test_case.get('test_type', 'sync')})")
    workflow_logger.info(test_params)
    
    # 初始化响应变量
    responses = {
        "create_response": None,
        "detail_response": None,
        "invoke_response": None
    }
    
    try:
        # 1. 创建工作流
        workflow_id, create_response = create_workflow_helper(
            test_params, workflow_route_env, workflow_base_url, workflow_auth_token
        )
        responses["create_response"] = create_response
        
        # 2. 查询工作流
        detail_response = query_workflow_helper(
            workflow_id, workflow_route_env, workflow_base_url, workflow_auth_token
        )
        responses["detail_response"] = detail_response
        
        # 3. 验证工作流一致性
        _verify_workflow_consistency_by_type(test_case, test_params, detail_response)
        
        # 4. 调用工作流
        is_async, parameters, invoke_response = invoke_workflow_helper(
            workflow_id, test_case, test_params, workflow_route_env, workflow_base_url, workflow_auth_token
        )
        responses["invoke_response"] = invoke_response
        
        # 检查是否期望工作流执行失败
        if test_case.get("expect_execution_failure", False):
            workflow_logger.info(f"测试 '{test_case['test_name']}' 执行成功 (期望失败)")
            return
        
        # 5. 根据test_type选择相应的处理策略
        _handle_execution_by_type(test_case, invoke_response, workflow_id, 
                                workflow_route_env, workflow_base_url, workflow_auth_token)
        
        workflow_logger.info(f"测试 '{test_case['test_name']}' 执行成功")
        
    except Exception as e:
        error_message = format_error_message(e, responses)
        pytest.fail(error_message)