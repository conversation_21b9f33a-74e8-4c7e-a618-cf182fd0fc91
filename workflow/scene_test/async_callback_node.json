{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": ""}, "next": ["120001_27e35652-6b58-4ce7-0a83-a5efdd8ad813"], "input": null, "output": {"type": "object", "required": ["task_id"], "properties": {"chatHistory": {"type": "string", "description": "历史对话记录，最多30轮"}, "fileUrls": {"type": "array", "description": "包含用户当前轮次上传的文件列表"}, "task_id": {"type": "string", "description": "1"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": ""}, "next": null, "input": [{"id": "content", "name": "content", "input_type": "reference", "reference": {"node_id": "120001_27e35652-6b58-4ce7-0a83-a5efdd8ad813", "name": "data", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "120001_27e35652-6b58-4ce7-0a83-a5efdd8ad813", "node_type": "ASYNC", "node_meta": {"name": "异步", "description": "进行异步http接口的调用"}, "next": ["END"], "input": [{"id": "submit.body.task_id", "name": "task_id", "input_type": "reference", "reference": {"node_id": "START", "name": "task_id", "type": "string"}}], "output": {"type": "object", "properties": {"code": {"type": "string", "description": "执行状态"}, "data": {"type": "string", "description": "数据"}}}, "async": {"async_type": "CALLBACK", "timeout": 100, "submit_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com:80/callback", "method": "POST", "headers": [], "query": null, "body": [{"id": "submit.body.task_id", "name": "task_id", "input_type": "reference", "reference": {"node_id": "START", "name": "task_id", "type": "string"}}], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "task_id_json_path": "task_id"}, "callback_config": {"end_config": {"response_status_jsonpath": "code", "final_state_list": ["0", "1"], "failed_state_list": ["0"]}}}}]}