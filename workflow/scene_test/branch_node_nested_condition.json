{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始节点", "description": ""}, "next": ["node1"], "input": null, "output": {"properties": {"condA": {"type": "string"}, "condB": {"type": "string"}, "condC": {"type": "string"}, "condD": {"type": "string"}, "sleep_time": {"type": "integer"}}}}, {"node_id": "node1", "node_type": "BRANCH", "node_meta": {"name": "多层嵌套条件分支节点", "description": "测试形如 (D AND A AND B) OR (D AND C) 的多层嵌套条件"}, "next": [], "input": [{"id": "condA", "name": "condA", "input_type": "reference", "reference": {"node_id": "START", "name": "condA", "type": "string"}}, {"id": "condB", "name": "condB", "input_type": "reference", "reference": {"node_id": "START", "name": "condB", "type": "string"}}, {"id": "condC", "name": "condC", "input_type": "reference", "reference": {"node_id": "START", "name": "condC", "type": "string"}}, {"id": "condD", "name": "condD", "input_type": "reference", "reference": {"node_id": "START", "name": "condD", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "and", "if": [{"left": {"id": "condD", "name": "condD", "input_type": "reference", "reference": {"node_id": "START", "name": "condD", "type": "string"}}, "right": {"id": "D条件", "name": "输入", "input_type": "literal", "literal": "true"}, "op": "eq"}, {"left": {"id": "condA", "name": "condA", "input_type": "reference", "reference": {"node_id": "START", "name": "condA", "type": "string"}}, "right": {"id": "A条件", "name": "输入", "input_type": "literal", "literal": "true"}, "op": "eq"}, {"left": {"id": "condB", "name": "condB", "input_type": "reference", "reference": {"node_id": "START", "name": "condB", "type": "string"}}, "right": {"id": "B条件", "name": "输入", "input_type": "literal", "literal": "true"}, "op": "eq"}]}, "next": ["node2"]}, {"expr": "", "param": {"operator": "and", "if": [{"left": {"id": "condD", "name": "condD", "input_type": "reference", "reference": {"node_id": "START", "name": "condD", "type": "string"}}, "right": {"id": "D条件", "name": "输入", "input_type": "literal", "literal": "true"}, "op": "eq"}, {"left": {"id": "condC", "name": "condC", "input_type": "reference", "reference": {"node_id": "START", "name": "condC", "type": "string"}}, "right": {"id": "C条件", "name": "输入", "input_type": "literal", "literal": "true"}, "op": "eq"}]}, "next": ["node2"]}], "default_next": ["node3"]}}, {"node_id": "node2", "node_type": "FUNC", "node_meta": {"name": "条件满足执行节点", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "condition_met"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "node3", "node_type": "FUNC", "node_meta": {"name": "默认分支执行节点", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "default_executed"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束节点", "description": ""}, "next": null, "input": [{"id": "condition_output", "name": "condition_output", "input_type": "reference", "reference": {"node_id": "node2", "name": "node_output", "type": "string"}}, {"id": "default_output", "name": "default_output", "input_type": "reference", "reference": {"node_id": "node3", "name": "node_output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}