{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["intent_node"], "input": null, "output": {"properties": {"userPrompt": {"type": "string"}}}}, {"node_id": "intent_node", "node_type": "INTENT", "node_meta": {"name": "", "description": ""}, "input": [{"id": "query", "name": "query", "input_type": "reference", "reference": {"node_id": "START", "name": "userPrompt", "type": "string"}}], "intent": {"polaris_name": "trpc.amai.hunyuan-openapi", "polaris_namespace": "Production", "api_token": "Wbhy4RiZnAy0zH1XAtDPLaVlqqT11WBd", "provider": "hun<PERSON>", "model": "hunyuan-turbo", "url": "/openapi/v1/chat/completions", "need_reason": false, "conditions": [{"intent": "查询订单", "next": ["node1"]}, {"intent": "取消订单", "next": ["node2"]}], "default_next": ["node3"], "user_prompt": [{"id": "query", "name": "query", "input_type": "reference", "reference": {"node_id": "START", "name": "userPrompt", "type": "string"}}]}, "next": null, "output": {"properties": {"intent": {"type": "string"}}}}, {"node_id": "node1", "node_type": "MESSAGE", "node_meta": {"name": "node1", "description": "node1"}, "next": ["END"], "input": [{"id": "userPrompt", "name": "userPrompt", "input_type": "reference", "reference": {"node_id": "intent_node", "name": "intent", "type": "string"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "查询"}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "node2", "node_type": "MESSAGE", "node_meta": {"name": "node1", "description": "node1"}, "next": ["END"], "input": [{"id": "userPrompt", "name": "userPrompt", "input_type": "reference", "reference": {"node_id": "intent_node", "name": "intent", "type": "string"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "取消"}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "node3", "node_type": "MESSAGE", "node_meta": {"name": "node1", "description": "node1"}, "next": ["END"], "input": [{"id": "userPrompt", "name": "userPrompt", "input_type": "reference", "reference": {"node_id": "intent_node", "name": "intent", "type": "string"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "其他"}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "x1", "name": "node1_val", "input_type": "reference", "reference": {"node_id": "node1", "name": "node_output", "type": "string"}}, {"id": "x2", "name": "node2_val", "input_type": "reference", "reference": {"node_id": "node2", "name": "node_output", "type": "string"}}, {"id": "node3_val", "name": "node3_val", "input_type": "reference", "reference": {"node_id": "node3", "name": "node_output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}