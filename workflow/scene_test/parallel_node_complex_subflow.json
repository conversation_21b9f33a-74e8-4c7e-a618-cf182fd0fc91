{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["def_code_node"], "input": null}, {"node_id": "def_code_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["parallel_node"], "input": [], "output": {"type": "object", "properties": {"temp_val": {"type": "string"}}}, "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"temp_val\": \"complex_subflow_test\",\n    }\n    return res,nil\n}"}}, {"node_id": "parallel_node", "node_type": "PARALLEL", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "def_code_node.prefix", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "def_code_node", "name": "temp_val", "type": "string"}}], "parallel": {"batch_size": 2, "max_cnt": 100, "arrays": [{"id": "iter_array.input", "name": "input", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input", "type": "array"}}], "parallel_output": [{"id": "output.final_output", "name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "merge_node", "name": "final_output", "type": "string"}}], "references": [{"id": "def_code_node", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "def_code_node", "name": "@this", "type": "object"}}], "sub_graph": {"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["condition_node"], "input": null}, {"node_id": "condition_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["branch_node"], "input": [{"id": "input", "name": "input", "required": true, "input_type": "iter_variable", "reference": {"node_id": "START", "name": "input", "type": "string"}}, {"id": "start_node.index", "name": "index", "required": true, "input_type": "iter_variable", "reference": {"node_id": "START", "name": "index", "type": "integer"}}], "output": {"type": "object", "properties": {"condition_result": {"type": "string"}, "processed_input": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n    \"strings\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    // 根据索引和输入内容判断分支条件\n    var index int\n    switch idx := args[\"index\"].(type) {\n    case float64:\n        index = int(idx)\n    case int64:\n        index = int(idx)\n    case int:\n        index = idx\n    default:\n        return nil, fmt.Errorf(\"不支持的index类型: %T\", args[\"index\"])\n    }\n    \n    input := fmt.Sprintf(\"%v\", args[\"input\"])\n    \n    var condResult string\n    if index%2 == 0 {\n        condResult = \"even_path\"\n    } else if strings.Contains(input, \"special\") {\n        condResult = \"special_path\"\n    } else {\n        condResult = \"default_path\"\n    }\n    \n    var res = map[string]interface{}{\n        \"condition_result\": condResult,\n        \"processed_input\": fmt.Sprintf(\"processed_%s_%d\", input, index),\n    }\n    return res,nil\n}"}}, {"node_id": "branch_node", "node_type": "BRANCH", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "condition_result", "name": "condition_result", "input_type": "reference", "reference": {"node_id": "condition_node", "name": "condition_result", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "", "if": [{"left": {"id": "condition_result", "name": "condition_result", "input_type": "reference", "reference": {"node_id": "condition_node", "name": "condition_result", "type": "string"}}, "right": {"id": "even_path_literal", "name": "输入", "input_type": "literal", "literal": "even_path"}, "op": "eq"}]}, "next": ["even_handler"]}, {"expr": "", "param": {"operator": "", "if": [{"left": {"id": "condition_result", "name": "condition_result", "input_type": "reference", "reference": {"node_id": "condition_node", "name": "condition_result", "type": "string"}}, "right": {"id": "special_path_literal", "name": "输入", "input_type": "literal", "literal": "special_path"}, "op": "eq"}]}, "next": ["special_handler"]}], "default_next": ["default_handler"]}}, {"node_id": "even_handler", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["merge_node"], "input": [{"id": "condition_node.processed_input", "name": "processed_input", "required": true, "input_type": "reference", "reference": {"node_id": "condition_node", "name": "processed_input", "type": "string"}}], "output": {"type": "object", "properties": {"branch_output": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"branch_output\": \"even_\" + args[\"processed_input\"].(string),\n    }\n    return res,nil\n}"}}, {"node_id": "special_handler", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["merge_node"], "input": [{"id": "condition_node.processed_input", "name": "processed_input", "required": true, "input_type": "reference", "reference": {"node_id": "condition_node", "name": "processed_input", "type": "string"}}], "output": {"type": "object", "properties": {"branch_output": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"branch_output\": \"special_\" + args[\"processed_input\"].(string),\n    }\n    return res,nil\n}"}}, {"node_id": "default_handler", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["merge_node"], "input": [{"id": "condition_node.processed_input", "name": "processed_input", "required": true, "input_type": "reference", "reference": {"node_id": "condition_node", "name": "processed_input", "type": "string"}}], "output": {"type": "object", "properties": {"branch_output": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"branch_output\": \"default_\" + args[\"processed_input\"].(string),\n    }\n    return res,nil\n}"}}, {"node_id": "merge_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "even_handler.branch_output", "name": "even_output", "required": false, "input_type": "reference", "reference": {"node_id": "even_handler", "name": "branch_output", "type": "string"}}, {"id": "special_handler.branch_output", "name": "special_output", "required": false, "input_type": "reference", "reference": {"node_id": "special_handler", "name": "branch_output", "type": "string"}}, {"id": "default_handler.branch_output", "name": "default_output", "required": false, "input_type": "reference", "reference": {"node_id": "default_handler", "name": "branch_output", "type": "string"}}, {"id": "code_node.prefix", "name": "prefix", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "def_code_node.temp_val", "type": "string"}}], "output": {"type": "object", "properties": {"final_output": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    // 合并不同分支的输出结果\n    var branchResult string\n    \n    if args[\"even_output\"] != nil {\n        branchResult = args[\"even_output\"].(string)\n    } else if args[\"special_output\"] != nil {\n        branchResult = args[\"special_output\"].(string)\n    } else if args[\"default_output\"] != nil {\n        branchResult = args[\"default_output\"].(string)\n    } else {\n        branchResult = \"no_branch_executed\"\n    }\n    \n    var res = map[string]interface{}{\n        \"final_output\": args[\"prefix\"].(string) + \"_complex_\" + branchResult,\n    }\n    return res,nil\n}"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": null, "input": [{"name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "merge_node", "name": "final_output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "output", "required": true, "input_type": "reference", "reference": {"node_id": "parallel_node", "name": "output", "type": "array"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}