{"nodes": [{"input": null, "next": ["complex_array_loop"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "complex_array_loop", "node_meta": {"description": "处理复杂数据结构数组", "name": "复杂数据循环"}, "node_type": "ITER", "iter": {"iter_type": "array", "arrays": [{"id": "iter_array.complex_items", "input_type": "reference", "reference": {"name": "complex_items", "node_id": "START", "type": "array"}, "name": "complex_items", "required": true}], "references": [], "iter_output": [{"id": "output.item_result", "input_type": "reference", "name": "item_result", "reference": {"name": "item_result", "node_id": "process_item", "type": "object"}, "required": true}], "sub_graph": {"type": "WORKFLOW", "graph_id": "", "nodes": [{"input": null, "next": ["process_item"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "process_item", "node_meta": {"description": "处理复杂数据结构项", "name": "处理项目"}, "node_type": "EVAL", "code": {"code": "func main(args map[string]interface{}) (map[string]interface{}, error) {\n    // 获取当前循环项\n    item := args[\"item\"].(map[string]interface{})\n    \n    // 提取基本属性\n    id := int(item[\"id\"].(float64))\n    \n    // 特殊处理第三个项目（id=1003）\n    if id == 1003 {\n        // 只返回project_id\n        result := map[string]interface{}{\n            \"project_id\": id,\n        }\n        return map[string]interface{}{\n            \"item_result\": result,\n        }, nil\n    }\n    \n    // 处理其他项目\n    name := item[\"name\"].(string)\n    active := item[\"active\"].(bool)\n    \n    // 提取嵌套对象中的属性\n    metadata := item[\"metadata\"].(map[string]interface{})\n    creator := metadata[\"creator\"].(string)\n    department := metadata[\"department\"].(string)\n    \n    // 创建结果对象\n    result := map[string]interface{}{\n        \"project_id\": id,\n        \"project_name\": name,\n        \"is_active\": active,\n        \"creator_info\": creator + \" (\" + department + \")\",\n    }\n    \n    return map[string]interface{}{\n        \"item_result\": result,\n    }, nil\n}"}, "input": [{"id": "item", "name": "item", "input_type": "iter_variable", "reference": {"name": "complex_items", "node_id": "START", "type": "object"}, "required": true}], "output": {"type": "object", "properties": {"item_result": {"type": "object"}}}, "next": ["END"]}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"input_type": "reference", "name": "item_result", "reference": {"name": "item_result", "node_id": "process_item", "type": "object"}, "required": true}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}}, "input": [], "next": ["END"]}, {"input": [{"input_type": "reference", "name": "item_result", "reference": {"name": "item_result", "node_id": "complex_array_loop", "type": "array"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}, "next": [], "node_id": "END", "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE"}], "type": "WORKFLOW", "graph_id": ""}