{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": "工作流起始节点"}, "next": ["knowledge_node"], "input": null, "output": {"properties": {"userPrompt": {"type": "string"}}}}, {"node_id": "knowledge_node", "node_type": "KNOWLEDGE", "node_meta": {"name": "知识库检索", "description": "根据用户查询检索相关知识"}, "next": ["END"], "input": [{"id": "query", "name": "query", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "userPrompt", "type": "string"}}], "knowledge": {"polaris_name": "trpc.amai.hunyuan-openapi", "polaris_namespace": "Production", "api_token": "Wbhy4RiZnAy0zH1XAtDPLaVlqqT11WBd", "min_score": 0.5, "top_k": 3, "strategy": "semantic", "knowledgeIds": ["686f34fb28e389a7b9814dbc"]}, "output": {"properties": {"content": {"type": "string"}, "score": {"type": "number"}, "source": {"type": "string"}}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": "输出检索结果"}, "next": [], "input": [{"id": "knowledge_result", "name": "knowledge_result", "input_type": "reference", "reference": {"node_id": "knowledge_node", "name": "content", "type": "string"}, "required": true}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}