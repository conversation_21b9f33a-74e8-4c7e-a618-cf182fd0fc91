# -*- coding: utf-8 -*-

'''
Author       : winsonyang 
Date         : 2025-03-20 19:13:35
LastEditors  : winsonyang 
LastEditTime : 2025-04-16 16:41:58
FilePath     : /aigc-api-test/workflow/scene_test/conftest.py
Description  : 测试环境配置文件，提供环境变量处理和测试用例共享配置

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import os
import pytest # type: ignore
import logging
from workflow.utils.log_utils import workflow_logger
from workflow.utils.test_metrics import get_metrics_collector, reset_metrics_collector


def pytest_configure(config):
    """配置pytest日志处理"""
    # 配置pytest的日志捕获
    config.option.log_cli = True
    config.option.log_cli_level = "INFO"
    config.option.log_cli_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 配置日志处理器
    logging.getLogger().setLevel(logging.INFO)
    
    # 配置HTML报告中的日志格式
    config.option.html_report_log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    config.option.html_report_log_level = "INFO"


@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """自定义测试报告生成钩子"""
    outcome = yield
    report = outcome.get_result()
    
    if report.when == "call":
        # 获取测试用例的日志
        test_logs = []
        for handler in logging.getLogger().handlers:
            if isinstance(handler, logging.StreamHandler):
                # 获取当前测试的所有日志记录
                for record in handler.records if hasattr(handler, 'records') else []:
                    test_logs.append(handler.formatter.format(record))
        
        # 将日志添加到测试报告中
        if test_logs:
            report.sections.append(("Log", "\n".join(test_logs)))


# 在测试会话开始时获取环境变量，并共享给所有测试用例
@pytest.fixture(scope="session", autouse=True)
def workflow_env_config():
    """
    在测试会话开始时获取环境变量配置
    使用session级别确保只获取一次
    autouse=True 自动应用到所有测试用例
    """
    class EnvConfig:
        def __init__(self):
            # 获取环境变量，处理环境变量为空的情况
            self.route_env = self._get_env_or_none("WORKFLOW_ROUTE_ENV")
            self.base_url = self._get_env_or_default("WORKFLOW_BASE_URL", "http://hunyuanapitest.woa.com")
            self.auth_token = self._get_env_or_default("WORKFLOW_AUTH_TOKEN", "XQnpVnimz3v7BMNspsRHwHWPTA111111")
            
            # 记录配置信息
            workflow_logger.info("工作流测试环境配置", {
                "route_env": self.route_env,
                "base_url": self.base_url
            })
            
            # 对敏感信息只记录是否已配置
            if self.auth_token == "XQnpVnimz3v7BMNspsRHwHWPTA111111":
                workflow_logger.warning("使用默认认证令牌，建议在生产环境中设置WORKFLOW_AUTH_TOKEN环境变量")
            else:
                workflow_logger.info("已配置自定义认证令牌")
        
        def _get_env_or_default(self, env_name: str, default_value: str) -> str:
            """获取环境变量，如果为空则使用默认值"""
            value = os.getenv(env_name, "").strip()
            if not value:
                workflow_logger.warning("环境变量未设置或为空", {
                    "env_name": env_name,
                    "default_value": default_value
                })
                return default_value
            return value
        
        def _get_env_or_none(self, env_name: str) -> str:
            """获取环境变量，如果为空则返回None"""
            value = os.getenv(env_name, "").strip()
            if not value:
                workflow_logger.info("环境变量未设置，将不传递该参数", {
                    "env_name": env_name
                })
                return None
            return value
    
    # 返回包含环境变量配置的对象
    return EnvConfig()


# 提供route_env的fixture
@pytest.fixture
def workflow_route_env(workflow_env_config):
    """
    提供route_env的fixture
    """
    return workflow_env_config.route_env


# 提供base_url的fixture
@pytest.fixture
def workflow_base_url(workflow_env_config):
    """
    提供base_url的fixture
    """
    return workflow_env_config.base_url


# 提供auth_token的fixture
@pytest.fixture
def workflow_auth_token(workflow_env_config):
    """
    提供auth_token的fixture
    """
    return workflow_env_config.auth_token


# 测试指标收集器相关fixture
@pytest.fixture(scope="session")
def metrics_collector():
    """
    会话级别的指标收集器
    在整个测试会话期间收集所有API调用的稳定性数据
    """
    # 重置指标收集器，确保每次测试会话都是干净的
    reset_metrics_collector()
    collector = get_metrics_collector()
    
    # 返回收集器供测试使用
    yield collector
    
    # 测试会话结束后生成报告
    try:
        # 生成并打印摘要
        collector.print_summary()
        
        # 导出详细数据
        import os
        from datetime import datetime
        
        # 创建报告目录
        report_dir = "workflow/reports/stability"
        os.makedirs(report_dir, exist_ok=True)
        
        # 生成文件名（包含时间戳）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(report_dir, f"stability_metrics_{timestamp}.json")
        
        # 导出报告
        collector.export_to_json(report_file)
        
        # 如果有问题API，生成警告
        problematic_apis = collector.get_problematic_apis(error_threshold=0.05)  # 5%错误率阈值
        if problematic_apis:
            workflow_logger.warning("发现稳定性问题的API", {
                "count": len(problematic_apis),
                "apis": [api["api_name"] for api in problematic_apis]
            })
            
    except Exception as e:
        workflow_logger.error(f"生成稳定性报告时出错: {e}")


@pytest.fixture(autouse=True)
def inject_metrics_collector(metrics_collector):
    """
    自动注入指标收集器到每个测试
    确保即使测试没有显式使用collector，API调用也会被记录
    """
    # 这个fixture会自动应用到每个测试
    # 仅通过依赖metrics_collector来确保它被创建
    pass


# 添加命令行选项以控制是否收集指标
def pytest_addoption(parser):
    """添加自定义命令行选项"""
    parser.addoption(
        "--collect-metrics",
        action="store_true",
        default=True,
        help="收集API稳定性指标（默认启用）"
    )
    parser.addoption(
        "--metrics-threshold",
        action="store",
        default="0.05",
        help="问题API的错误率阈值（默认0.05，即5%）"
    )