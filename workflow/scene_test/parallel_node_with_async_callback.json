{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "140001_83e48eb0-8482-4044-0086-432379495ba9", "node_type": "PARALLEL", "node_meta": {"name": "批处理", "description": "通过设定批量运行次数和逻辑，运行批处理体内的任务"}, "next": ["END"], "input": null, "parallel": {"batch_size": 0, "batch_size_fields": {"id": "concurrent_size.concurrentSize", "name": "concurrentSize", "input_type": "literal", "literal": "3", "literal_type": "integer"}, "max_cnt": 0, "max_cnt_fields": {"id": "max_count.maxCount", "name": "maxCount", "input_type": "literal", "literal": "10", "literal_type": "integer"}, "arrays": [{"id": "array.parallel_Input", "name": "parallel_Input", "input_type": "reference", "reference": {"node_id": "START", "name": "task_id", "type": "array<string>"}}], "parallel_output": [{"id": "parallel_output.res", "name": "res", "input_type": "reference", "reference": {"node_id": "20001_600ac02e-9117-4860-637-362b9f934a87", "name": "key0", "type": "array<string>"}}], "references": [], "sub_graph": {"graph_id": "0354a13b-abfe-4fe0-ad41-6da4c1c64fb3", "type": "WORKFLOW", "nodes": [{"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "批处理", "description": ""}, "next": null, "input": [{"id": "parallel_output.res", "name": "res", "input_type": "reference", "reference": {"node_id": "20001_600ac02e-9117-4860-637-362b9f934a87", "name": "key0", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}, {"node_id": "20001_600ac02e-9117-4860-637-362b9f934a87", "node_type": "CODE", "node_meta": {"name": "代码", "description": "使用python3对入参进行处理"}, "next": ["120001_a87da0be-9acd-4c59-8677-61df1429f18b"], "input": [{"id": "test_1", "name": "test_1", "input_type": "iter_variable", "reference": {"name": "parallel_Input", "type": "string"}}], "output": {"type": "object", "properties": {"key0": {"type": "string"}, "key1": {"type": "integer"}, "key2": {"type": "array", "items": {"type": "string"}}, "key3": {"type": "object", "properties": {"address": {"type": "string"}, "age": {"type": "integer"}, "name": {"type": "string"}}}}}, "code": {"code": "async def main(args):\n    ret = {\n        \"key0\": args['test_1'] + ' task done',\n        \"key1\": 7,\n        \"key2\": ['hunyuan', 'yuanqi'],\n        \"key3\": {\n            'name': 'hunyuan',\n            'age': 1,\n            'address': 'shenzhen'\n        }\n    }\n    print('result is: ', ret)\n    return ret"}}, {"node_id": "120001_a87da0be-9acd-4c59-8677-61df1429f18b", "node_type": "ASYNC", "node_meta": {"name": "异步", "description": "进行异步http接口的调用"}, "next": ["END"], "input": [{"id": "submit.body.task_id", "name": "task_id", "input_type": "iter_variable", "reference": {"name": "parallel_Input", "type": "string"}}], "output": {"type": "object", "properties": {"code": {"type": "string"}}}, "async": {"async_type": "CALLBACK", "timeout": 300, "submit_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com:80/callback", "method": "POST", "headers": [], "query": null, "body": [{"id": "submit.body.task_id", "name": "task_id", "input_type": "iter_variable", "reference": {"name": "parallel_Input", "type": "string"}}], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "task_id_json_path": "task_id"}, "callback_config": {"end_config": {"response_status_jsonpath": "code", "final_state_list": ["1", "0"], "failed_state_list": ["0"]}}}}, {"node_id": "START", "node_type": "START", "node_meta": {"name": "批处理", "description": ""}, "next": ["20001_600ac02e-9117-4860-637-362b9f934a87"], "input": null}]}}}, {"node_id": "START", "node_type": "START", "node_meta": {"name": "开始", "description": ""}, "next": ["140001_83e48eb0-8482-4044-0086-432379495ba9"], "input": null, "output": {"type": "object", "required": ["task_id"], "properties": {"chatHistory": {"type": "string", "description": "历史对话记录，最多30轮"}, "fileUrls": {"type": "array<object>", "description": "包含用户当前轮次上传的文件列表"}, "task_id": {"type": "array<string>", "description": "1"}, "userPrompt": {"type": "string", "description": "用户当前轮次的输入问题"}}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束", "description": ""}, "next": null, "input": [{"id": "res", "name": "res", "input_type": "reference", "reference": {"node_id": "140001_83e48eb0-8482-4044-0086-432379495ba9", "name": "res", "type": "array<string>"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}