{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始节点", "description": ""}, "next": ["node1"], "input": null, "output": {"properties": {"branch": {"type": "string"}, "sleep_time": {"type": "integer"}}}}, {"node_id": "node1", "node_type": "BRANCH", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "branch", "name": "branch", "input_type": "reference", "reference": {"node_id": "START", "name": "branch", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "", "if": [{"left": {"id": "branch", "name": "branch", "input_type": "reference", "reference": {"node_id": "START", "name": "branch", "type": "string"}}, "right": {"id": "1左分支", "name": "输入", "input_type": "literal", "literal": "node2"}, "op": "eq"}]}, "next": ["node2"]}], "default_next": ["node4"]}}, {"node_id": "node2", "node_type": "FUNC", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "output_node_2"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "node4", "node_type": "FUNC", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "output_node_4"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": null, "input": [{"id": "x1", "name": "node2_val", "input_type": "reference", "reference": {"node_id": "node2", "name": "node_output", "type": "string"}}, {"id": "node4_val", "name": "node4_val", "input_type": "reference", "reference": {"node_id": "node4", "name": "node_output", "type": "string"}}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}}]}