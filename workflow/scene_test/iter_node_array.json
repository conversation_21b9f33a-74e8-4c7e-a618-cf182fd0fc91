{"nodes": [{"input": null, "next": ["def_code_node"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "def_code_node", "node_meta": {"description": "", "name": ""}, "node_type": "EVAL", "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"temp_val\": \"temp_val_1\",\n    }\n    return res,nil\n}"}, "input": [], "output": {"type": "object", "properties": {"temp_val": {"type": "string"}}}, "next": ["iter_node"]}, {"iter": {"iter_type": "array", "arrays": [{"id": "iter_array.input", "input_type": "reference", "reference": {"name": "input", "node_id": "START", "type": "array"}, "name": "input", "required": true}, {"id": "iter_array.input2", "input_type": "reference", "reference": {"name": "input2", "node_id": "START", "type": "array"}, "name": "input2", "required": true}], "local_variables": [{"id": "local_variables.last", "input_type": "literal", "name": "last", "literal": "abcdd", "literal_type": "string", "required": true}], "iter_output": [{"id": "output.output", "input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}], "references": [{"id": "def_code_node", "input_type": "reference", "name": "prefix", "reference": {"name": "@this", "node_id": "def_code_node", "type": "object"}, "required": true}], "sub_graph": {"type": "WORKFLOW", "graph_id": "", "nodes": [{"input": null, "next": ["code_node"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "code_node", "node_meta": {"description": "", "name": ""}, "node_type": "EVAL", "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    // 不使用索引，直接拼接字符串\n    var res = map[string]interface{}{\n        \"output\": args[\"prefix\"].(string) + args[\"input\"].(string) + args[\"input2\"].(string) + args[\"last\"].(string),\n    }\n    return res,nil\n}"}, "input": [{"id": "input", "input_type": "iter_variable", "name": "input", "reference": {"name": "input.name", "node_id": "START", "type": "string"}, "required": true}, {"id": "input2", "input_type": "iter_variable", "name": "input2", "reference": {"name": "input2.name", "node_id": "START", "type": "string"}, "required": true}, {"id": "last", "input_type": "iter_variable", "name": "last", "reference": {"name": "last", "node_id": "START", "type": "string"}, "required": true}, {"id": "index", "input_type": "iter_variable", "name": "index", "reference": {"name": "index", "node_id": "START", "type": "integer"}, "required": true}, {"id": "code_node.prefix", "input_type": "reference", "name": "prefix", "reference": {"name": "def_code_node.temp_val", "node_id": "START", "type": "string"}, "required": true}], "output": {"type": "object", "properties": {"output": {"type": "string"}}}, "next": ["variable_node"]}, {"input": [{"input_type": "reference", "name": "last", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}], "node_id": "variable_node", "node_meta": {"description": "", "name": ""}, "node_type": "VARIABLE", "next": ["http_node"], "variable": {"actions": [{"action": "set", "variable_name": "last", "variable_type": "local", "value": {"input_type": "reference", "name": "last", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}}]}}, {"node_id": "http_node", "node_type": "HTTP", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.1", "name": "b1", "required": true, "input_type": "literal", "literal": "{\"a\":{\"b\":"}, {"id": "body.2", "name": "b2", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input.name", "type": "string"}}, {"id": "body.3", "name": "b3", "required": true, "input_type": "literal", "literal": "}}"}], "http": {"method": "GET", "url": "https://www.baidu.com", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 123456"}], "body_type": "json-string", "query": null, "retry_times": 0, "timeout": 0, "body": [{"id": "body.1", "name": "b1", "required": true, "input_type": "literal", "literal": "{\"a\":{\"b\":"}, {"id": "body.2", "name": "b2", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input.name", "type": "string"}}, {"id": "body.3", "name": "b3", "required": true, "input_type": "literal", "literal": "}}"}]}, "output": {"type": "object", "properties": {"body": {"type": "string"}, "status_code": {"type": "integer"}, "headers": {"type": "string"}}}}, {"node_id": "END", "next": null, "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE", "input": [{"input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}}]}}, "input": [], "next": ["END"], "node_id": "iter_node", "node_meta": {"description": "", "name": ""}, "node_type": "ITER"}, {"input": [{"input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "iter_node", "type": "array"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}, "next": [], "node_id": "END", "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE"}], "type": "WORKFLOW", "graph_id": ""}