{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["async_query_node"], "input": null}, {"node_id": "async_query_node", "node_type": "ASYNC", "node_meta": {"name": "", "description": ""}, "next": ["parallel_node"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer 0qHtuxlmXiO-BcATjSg2-BBbAofnSyf5"}, {"id": "body.data", "name": "data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "input_data", "type": "string"}}, {"id": "body.run_time", "name": "run_time", "required": true, "input_type": "literal", "literal": 2}], "output": {"type": "object", "properties": {"data": {"type": "string", "description": "异步查询返回的内容"}}}, "async": {"async_type": "QUERY", "timeout": 100, "submit_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com/query", "method": "POST", "headers": [], "query": null, "body": [], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "task_id_json_path": "task_id"}, "query_config": {"http_config": {"url": "http://wftest-9860.mock.ifbook.woa.com/query", "method": "POST", "headers": [], "query": null, "body": [], "body_type": "", "error_config": {"error_json_path": "", "error_code_json_path": ""}, "timeout": 300, "retry_times": 0}, "end_config": {"response_status_jsonpath": "code", "final_state_list": ["0", "1"], "failed_state_list": ["0"]}, "query_interval_time": 1}}}, {"node_id": "parallel_node", "node_type": "PARALLEL", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [], "parallel": {"batch_size": 2, "max_cnt": 100, "arrays": [{"id": "iter_array.task_data", "name": "task_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "task_list", "type": "array"}}], "parallel_output": [{"id": "output.result", "name": "result", "required": true, "input_type": "reference", "reference": {"node_id": "code_node", "name": "processed_data", "type": "string"}}], "references": [{"id": "async_result", "name": "async_result", "required": true, "input_type": "reference", "reference": {"node_id": "async_query_node", "name": "data", "type": "string"}}], "sub_graph": {"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["code_node"], "input": null}, {"node_id": "code_node", "node_type": "EVAL", "node_meta": {"name": "", "description": ""}, "next": ["END"], "input": [{"id": "task_input", "name": "task_input", "required": true, "input_type": "iter_variable", "reference": {"node_id": "START", "name": "task_data", "type": "string"}}, {"id": "start_node.index", "name": "index", "required": true, "input_type": "iter_variable", "reference": {"node_id": "START", "name": "index", "type": "integer"}}, {"id": "async_result", "name": "async_result", "required": true, "input_type": "ref_variable", "reference": {"node_id": "START", "name": "async_result", "type": "string"}}], "output": {"type": "object", "properties": {"processed_data": {"type": "string"}}}, "code": {"code": "import (\n    \"fmt\"\n)\nfunc main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"processed_data\": fmt.Sprintf(\"%s_task_%v_%s\", args[\"async_result\"].(string), args[\"index\"], args[\"task_input\"].(string)),\n    }\n    return res,nil\n}"}}, {"node_id": "END", "node_type": "MESSAGE", "message": {"message_type": "string", "streaming_output": false, "string_format": null}, "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "content", "name": "content", "input_type": "reference", "reference": {"node_id": "code_node", "name": "processed_data", "type": "string"}}]}]}}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "result", "required": true, "input_type": "reference", "reference": {"node_id": "parallel_node", "name": "result", "type": "array"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}