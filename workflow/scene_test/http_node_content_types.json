{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["json_node", "form_node", "text_node"], "input": null, "output": {"properties": {"json_data": {"type": "string"}, "form_data": {"type": "string"}, "text_data": {"type": "string"}}}}, {"node_id": "json_node", "node_type": "HTTP", "node_meta": {"name": "JSON内容类型", "description": "测试JSON内容类型"}, "next": ["END"], "input": [{"id": "body.data", "name": "json_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "json_data", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/post", "method": "POST", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/json"}], "body": [{"id": "body.data", "name": "json_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "json_data", "type": "string"}}], "body_type": "json", "retry_times": 1, "timeout": 5000}}, {"node_id": "form_node", "node_type": "HTTP", "node_meta": {"name": "表单内容类型", "description": "测试表单内容类型"}, "next": ["END"], "input": [{"id": "body.data", "name": "form_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "form_data", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/post", "method": "POST", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "application/x-www-form-urlencoded"}], "body": [{"id": "body.data", "name": "form_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "form_data", "type": "string"}}], "body_type": "form", "retry_times": 1, "timeout": 5000}}, {"node_id": "text_node", "node_type": "HTTP", "node_meta": {"name": "文本内容类型", "description": "测试文本内容类型"}, "next": ["END"], "input": [{"id": "body.data", "name": "text_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "text_data", "type": "string"}}], "output": {"properties": {"statusCode": {"type": "integer"}, "body": {"type": "string"}}}, "http": {"url": "https://httpbin.org/post", "method": "POST", "headers": [{"id": "headers.Content-Type", "name": "Content-Type", "required": true, "input_type": "literal", "literal": "text/plain"}], "body": [{"id": "body.data", "name": "text_data", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "text_data", "type": "string"}}], "body_type": "text", "retry_times": 1, "timeout": 5000}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"name": "json_status", "required": true, "input_type": "reference", "reference": {"node_id": "json_node", "name": "statusCode", "type": "integer"}}, {"name": "form_status", "required": true, "input_type": "reference", "reference": {"node_id": "form_node", "name": "statusCode", "type": "integer"}}, {"name": "text_status", "required": true, "input_type": "reference", "reference": {"node_id": "text_node", "name": "statusCode", "type": "integer"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}