{"nodes": [{"input": null, "next": ["def_code_node"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "def_code_node", "node_meta": {"description": "", "name": ""}, "node_type": "EVAL", "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    var res = map[string]interface{}{\n        \"temp_val\": \"temp_val_1\",\n    }\n    return res,nil\n}"}, "input": [], "output": {"type": "object", "properties": {"temp_val": {"type": "string"}}}, "next": ["iter_node"]}, {"iter": {"iter_type": "array", "arrays": [{"id": "iter_array.input", "input_type": "reference", "reference": {"name": "input", "node_id": "START", "type": "array"}, "name": "input", "required": true}, {"id": "iter_array.input2", "input_type": "reference", "reference": {"name": "input2", "node_id": "START", "type": "array"}, "name": "input2", "required": true}], "local_variables": [{"id": "local_variables.last", "input_type": "literal", "name": "last", "literal": "abcdd", "literal_type": "string", "required": true}], "iter_output": [{"id": "output.output", "input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}], "references": [{"id": "def_code_node", "input_type": "reference", "name": "prefix", "reference": {"name": "@this", "node_id": "def_code_node", "type": "object"}, "required": true}], "sub_graph": {"type": "WORKFLOW", "graph_id": "", "nodes": [{"input": null, "next": ["code_node"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START"}, {"node_id": "code_node", "node_meta": {"description": "", "name": ""}, "node_type": "EVAL", "code": {"code": "func main(args map[string]interface{}) (map[string]interface{},error) {\n    // 获取循环索引，处理不同类型的可能性\n    var index int\n    switch idx := args[\"index\"].(type) {\n    case float64:\n        index = int(idx)\n    case int64:\n        index = int(idx)\n    case int:\n        index = idx\n    default:\n        index = 0\n    }\n    \n    // 获取输入参数\n    input := args[\"input\"].(string)\n    input2 := args[\"input2\"].(string)\n    last := args[\"last\"].(string)\n    \n    // 处理prefix参数，可能是字符串或对象\n    var prefix string\n    switch p := args[\"prefix\"].(type) {\n    case string:\n        prefix = p\n    case map[string]interface{}:\n        if val, ok := p[\"temp_val\"].(string); ok {\n            prefix = val\n        } else {\n            prefix = \"temp_val_1\"\n        }\n    default:\n        prefix = \"temp_val_1\"\n    }\n    \n    // 构建索引字符串\n    var indexStr string\n    if index == 0 {\n        indexStr = \"[0]\"\n    } else if index == 1 {\n        indexStr = \"[1]\"\n    } else if index == 2 {\n        indexStr = \"[2]\"\n    } else {\n        indexStr = \"[?]\"\n    }\n    \n    // 拼接输出字符串\n    output := prefix + indexStr + input + input2 + last\n    \n    var res = map[string]interface{}{\n        \"output\": output,\n    }\n    return res,nil\n}"}, "input": [{"id": "input", "input_type": "iter_variable", "name": "input", "reference": {"name": "input.name", "node_id": "START", "type": "string"}, "required": true}, {"id": "input2", "input_type": "iter_variable", "name": "input2", "reference": {"name": "input2.name", "node_id": "START", "type": "string"}, "required": true}, {"id": "last", "input_type": "iter_variable", "name": "last", "reference": {"name": "last", "node_id": "START", "type": "string"}, "required": true}, {"id": "index", "input_type": "iter_variable", "name": "index", "reference": {"name": "index", "node_id": "START", "type": "integer"}, "required": true}, {"id": "code_node.prefix", "input_type": "reference", "name": "prefix", "reference": {"name": "def_code_node.temp_val", "node_id": "START", "type": "string"}, "required": true}], "output": {"type": "object", "properties": {"output": {"type": "string"}}}, "next": ["END"]}, {"node_id": "END", "next": null, "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE", "input": [{"input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "code_node", "type": "string"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}}]}}, "input": [], "next": ["END"], "node_id": "iter_node", "node_meta": {"description": "", "name": ""}, "node_type": "ITER"}, {"input": [{"input_type": "reference", "name": "output", "reference": {"name": "output", "node_id": "iter_node", "type": "array"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}, "next": [], "node_id": "END", "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE"}], "type": "WORKFLOW", "graph_id": ""}