{"nodes": [{"input": null, "next": ["node3"], "node_id": "START", "node_meta": {"description": "", "name": ""}, "node_type": "START", "output": {"properties": {"test1": {"type": "string"}, "test2": {"type": "string"}}}}, {"code": {"code": "async def main(args):\n    ret = {\n        \"key0\": args['test1'] + ' ' + args['test2'],\n        \"key1\": 7,\n        \"key2\": ['hunyuan', 'yuanqi'],\n        \"key3\": {\n            'name': 'hunyuan',\n            'age': 1,\n            'address': 'shenzhen'\n        }\n    }\n    print('result is: ', ret)\n    return ret"}, "input": [{"id": "test1", "input_type": "reference", "name": "test1", "reference": {"name": "test1", "node_id": "START", "type": "string"}, "required": true}, {"id": "test2", "input_type": "reference", "name": "test2", "reference": {"name": "test2", "node_id": "START", "type": "string"}, "required": true}], "next": ["END"], "node_id": "node3", "node_meta": {"description": "", "name": ""}, "node_type": "CODE", "output": {"properties": {"key2": {"items": {"type": "string"}, "type": "array"}}}}, {"input": [{"id": "content", "input_type": "reference", "name": "content", "reference": {"name": "key2", "node_id": "node3", "type": "array"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}, "next": [], "node_id": "END", "node_meta": {"description": "", "name": ""}, "node_type": "MESSAGE"}]}