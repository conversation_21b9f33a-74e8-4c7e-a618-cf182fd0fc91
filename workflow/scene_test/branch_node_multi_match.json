{"graph_id": "", "type": "WORKFLOW", "nodes": [{"node_id": "START", "node_type": "START", "node_meta": {"name": "开始节点", "description": ""}, "next": ["node1"], "input": null, "output": {"properties": {"value": {"type": "string"}, "sleep_time": {"type": "integer"}}}}, {"node_id": "node1", "node_type": "BRANCH", "node_meta": {"name": "多条件分支节点", "description": "测试同时满足多个分支条件的情况"}, "next": [], "input": [{"id": "value", "name": "value", "input_type": "reference", "reference": {"node_id": "START", "name": "value", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "", "if": [{"left": {"id": "value", "name": "value", "input_type": "reference", "reference": {"node_id": "START", "name": "value", "type": "string"}}, "right": {"id": "条件1", "name": "输入", "input_type": "literal", "literal": "test_value"}, "op": "eq"}]}, "next": ["node2"]}, {"expr": "", "param": {"operator": "", "if": [{"left": {"id": "value", "name": "value", "input_type": "reference", "reference": {"node_id": "START", "name": "value", "type": "string"}}, "right": {"id": "条件2", "name": "输入", "input_type": "literal", "literal": "value_only"}, "op": "eq"}]}, "next": ["node3"]}], "default_next": ["node4"]}}, {"node_id": "node2", "node_type": "FUNC", "node_meta": {"name": "分支1执行节点", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "branch1_executed"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "node3", "node_type": "FUNC", "node_meta": {"name": "分支2执行节点", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "branch2_executed"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "node4", "node_type": "FUNC", "node_meta": {"name": "默认分支执行节点", "description": ""}, "next": ["END"], "input": [{"id": "sleep_time", "name": "sleep_time", "input_type": "reference", "reference": {"node_id": "START", "name": "sleep_time", "type": "int"}}, {"id": "node_output", "name": "node_output", "input_type": "literal", "literal": "default_executed"}], "output": {"properties": {"node_output": {"type": "string"}}}, "func": {"func_name": "sleep"}}, {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "结束节点", "description": ""}, "next": null, "input": [{"id": "branch1_output", "name": "branch1_output", "input_type": "reference", "reference": {"node_id": "node2", "name": "node_output", "type": "string"}}, {"id": "branch2_output", "name": "branch2_output", "input_type": "reference", "reference": {"node_id": "node3", "name": "node_output", "type": "string"}}, {"id": "default_output", "name": "default_output", "input_type": "reference", "reference": {"node_id": "node4", "name": "node_output", "type": "string"}}], "message": {"streaming_output": false, "message_type": "VARIABLE", "string_format": null}}]}