'''
Author       : winsonyang 
Date         : 2025-03-13 10:58:52
LastEditors  : winsonyang 
LastEditTime : 2025-04-22 16:08:57
FilePath     : /aigc-api-test/workflow/utils/query_workflows.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''

import json
import argparse
import sys
import os

# 将项目根目录添加到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from typing import Dict, List, Optional
from pathlib import Path
from workflow.api.v1_workflow_detail import v1_workflow_detail, get_workflow_dsl
from workflow.utils.api_utils import load_workflow_from_file
from workflow.utils.error_handling import (
    workflow_logger, 
    WorkflowError, 
    WorkflowConfigError
)
from workflow.utils.response_utils import ApiResponse


def save_workflow_nodes_to_file(workflow_info: Dict, file_path: Path) -> None:
    """Save workflow nodes information to a JSON file with improved error handling."""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(workflow_info, f, ensure_ascii=False, indent=4)
        workflow_logger.info(f"成功保存工作流节点到：{file_path}")
    except IOError as e:
        error_msg = f"文件写入错误：{e}"
        workflow_logger.error(error_msg)
        raise WorkflowConfigError(
            message=error_msg,
            details={"file_path": str(file_path)}
        ) from e
    except json.JSONDecodeError as e:
        error_msg = f"JSON 编码错误：{e}"
        workflow_logger.error(error_msg)
        raise WorkflowConfigError(
            message=error_msg,
            details={"file_path": str(file_path)}
        ) from e


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='查询工作流详情并保存为JSON文件')
    parser.add_argument('workflow_ids', nargs='+', help='需要查询的一个或多个工作流ID')
    return parser.parse_args()


def get_env_value(name: str, default: str = None) -> Optional[str]:
    """从环境变量获取值，如果不存在则返回默认值。"""
    value = os.environ.get(name, default)
    return value


def query_workflow(workflow_id: str) -> Dict:
    """Query workflow details for a given workflow ID."""
    try:
        # 从环境变量获取API参数
        base_url = get_env_value("WORKFLOW_BASE_URL", "https://test.hunyuan.woa.com")
        auth_token = get_env_value("WORKFLOW_AUTH_TOKEN")
        route_env = get_env_value("WORKFLOW_ROUTE_ENV", "release-20250331")
        
        # 记录从环境变量读取的配置
        workflow_logger.info(f"从环境变量读取配置：URL={base_url}, ROUTE={route_env}, TOKEN={'已设置' if auth_token else '未设置'}")
        
        # 使用get_workflow_dsl直接获取DSL配置，并传入从环境变量读取的参数
        workflow_info = get_workflow_dsl(
            workflow_id,
            route_env=route_env,
            base_url=base_url,
            auth_token=auth_token
        )
        return workflow_info
    except WorkflowError as e:
        # 直接重新抛出工作流错误，它们已经被正确处理
        raise
    except Exception as e:
        error_context = {"workflow_id": workflow_id}
        error_msg = f"查询工作流详情失败（ID: {workflow_id}）：{str(e)}"
        workflow_logger.error(error_msg, error_context)
        raise WorkflowConfigError(
            message=error_msg,
            details=error_context
        ) from e


def main(workflow_ids: List[str]) -> None:
    """Query workflow details and save to JSON files with enhanced logging."""
    workflow_logger.info(f"开始查询工作流详情，共{len(workflow_ids)}个工作流")
    
    for workflow_id in workflow_ids:
        workflow_logger.info(f"开始查询工作流详情：{workflow_id}")
        try:
            workflow_info = query_workflow(workflow_id)
            output_file_path = Path(f"workflow/scene_test/{workflow_id}.json")
            
            # 确保输出目录存在
            output_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            save_workflow_nodes_to_file(workflow_info, output_file_path)
            workflow_logger.info(f"工作流 {workflow_id} 处理完成")
        except WorkflowError as e:
            workflow_logger.error(f"处理工作流 {workflow_id} 时发生错误：{e.message}")
        except Exception as e:
            error_context = {"workflow_id": workflow_id}
            workflow_logger.error(f"处理工作流 {workflow_id} 时发生未预期错误：{str(e)}", error_context)


if __name__ == "__main__":
    args = parse_args()
    main(args.workflow_ids)
