#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工作流比较工具

提供智能比较工作流的功能，忽略非关键差异，关注关键差异
"""

import json
import logging
from typing import Dict, List, Tuple, Any, Set, Optional

# 配置日志
logger = logging.getLogger("workflow")

def normalize_value(value: Any) -> Any:
    """
    规范化值，处理不同表示方式的等价值
    
    Args:
        value: 需要规范化的值
        
    Returns:
        规范化后的值
    """
    if value is None:
        return None
    
    # 处理空列表和None的等价性
    if isinstance(value, list) and len(value) == 0:
        return None
    
    # 处理空字典和None的等价性
    if isinstance(value, dict) and len(value) == 0:
        return None
    
    # 处理空字符串和None的等价性
    if isinstance(value, str) and value.strip() == "":
        return None
    
    return value

def compare_dict_values(dict1: Dict, dict2: Dict, path: str = "", 
                        ignore_order: bool = True, 
                        ignore_nulls: bool = True,
                        ignore_keys: Optional[Set[str]] = None) -> List[str]:
    """
    比较两个字典的值，返回差异列表
    
    Args:
        dict1: 第一个字典
        dict2: 第二个字典
        path: 当前比较路径，用于生成差异报告
        ignore_order: 是否忽略属性顺序
        ignore_nulls: 是否忽略null值和缺失字段的差异
        ignore_keys: 需要忽略的键集合
        
    Returns:
        差异列表
    """
    if ignore_keys is None:
        ignore_keys = set()
    
    differences = []
    
    # 获取所有键的并集
    all_keys = set(dict1.keys()) | set(dict2.keys())
    
    for key in all_keys:
        # 跳过需要忽略的键
        if key in ignore_keys:
            continue
        
        current_path = f"{path}.{key}" if path else key
        
        # 处理键在一个字典中存在但在另一个字典中不存在的情况
        if key not in dict1:
            if not ignore_nulls or normalize_value(dict2[key]) is not None:
                differences.append(f"键 '{current_path}' 在第一个字典中不存在，但在第二个字典中存在: {dict2[key]}")
            continue
            
        if key not in dict2:
            if not ignore_nulls or normalize_value(dict1[key]) is not None:
                differences.append(f"键 '{current_path}' 在第二个字典中不存在，但在第一个字典中存在: {dict1[key]}")
            continue
        
        # 规范化值
        value1 = normalize_value(dict1[key])
        value2 = normalize_value(dict2[key])
        
        # 如果两个值都是None（或等价于None），则跳过
        if ignore_nulls and value1 is None and value2 is None:
            continue
        
        # 比较值
        if isinstance(value1, dict) and isinstance(value2, dict):
            # 递归比较字典
            nested_differences = compare_dict_values(
                value1, value2, current_path, ignore_order, ignore_nulls, ignore_keys
            )
            differences.extend(nested_differences)
        elif isinstance(value1, list) and isinstance(value2, list):
            # 比较列表
            if ignore_order:
                # 如果列表元素是字典，需要特殊处理
                if all(isinstance(item, dict) for item in value1 + value2):
                    # 尝试按照某个关键字段（如"node_id"）进行排序和匹配
                    if all("node_id" in item for item in value1) and all("node_id" in item for item in value2):
                        # 按node_id排序
                        sorted_value1 = sorted(value1, key=lambda x: x.get("node_id", ""))
                        sorted_value2 = sorted(value2, key=lambda x: x.get("node_id", ""))
                        
                        # 检查node_id是否匹配
                        ids1 = [item.get("node_id") for item in sorted_value1]
                        ids2 = [item.get("node_id") for item in sorted_value2]
                        
                        if ids1 != ids2:
                            differences.append(f"列表 '{current_path}' 中的node_id不匹配: {ids1} vs {ids2}")
                        else:
                            # 逐个比较匹配的节点
                            for i, (item1, item2) in enumerate(zip(sorted_value1, sorted_value2)):
                                node_id = item1.get("node_id")
                                nested_differences = compare_dict_values(
                                    item1, item2, f"{current_path}[node_id={node_id}]", 
                                    ignore_order, ignore_nulls, ignore_keys
                                )
                                differences.extend(nested_differences)
                    else:
                        # 无法按node_id匹配，尝试其他方式
                        # 这里简化处理，实际可能需要更复杂的匹配逻辑
                        if len(value1) != len(value2):
                            differences.append(f"列表 '{current_path}' 长度不同: {len(value1)} vs {len(value2)}")
                        else:
                            # 简单比较，可能不准确
                            for i, (item1, item2) in enumerate(zip(value1, value2)):
                                nested_differences = compare_dict_values(
                                    item1, item2, f"{current_path}[{i}]", 
                                    ignore_order, ignore_nulls, ignore_keys
                                )
                                differences.extend(nested_differences)
                else:
                    # 简单元素列表，排序后比较
                    try:
                        if sorted(value1) != sorted(value2):
                            differences.append(f"列表 '{current_path}' 内容不同: {value1} vs {value2}")
                    except TypeError:
                        # 无法排序的元素，直接比较
                        if value1 != value2:
                            differences.append(f"列表 '{current_path}' 内容不同: {value1} vs {value2}")
            else:
                # 不忽略顺序，直接比较
                if value1 != value2:
                    differences.append(f"列表 '{current_path}' 内容不同: {value1} vs {value2}")
        else:
            # 比较简单值
            if value1 != value2:
                differences.append(f"值 '{current_path}' 不同: {value1} vs {value2}")
    
    return differences

def compare_workflows(created: Dict, queried: Dict, 
                     ignore_order: bool = True, 
                     ignore_nulls: bool = True,
                     ignore_keys: Optional[Set[str]] = None) -> Tuple[bool, List[str]]:
    """
    比较创建的工作流和查询的工作流，忽略非关键差异
    
    Args:
        created: 创建的工作流
        queried: 查询的工作流
        ignore_order: 是否忽略属性顺序
        ignore_nulls: 是否忽略null值和缺失字段的差异
        ignore_keys: 需要忽略的键集合
        
    Returns:
        (bool, list): 是否一致，以及差异列表
    """
    if ignore_keys is None:
        # 默认忽略的键
        ignore_keys = {"graph_id"}
    
    differences = compare_dict_values(created, queried, "", ignore_order, ignore_nulls, ignore_keys)
    
    # 过滤掉非关键差异
    critical_differences = [diff for diff in differences if is_critical_difference(diff)]
    
    return len(critical_differences) == 0, critical_differences or differences

def is_critical_difference(difference: str) -> bool:
    """
    判断差异是否为关键差异
    
    Args:
        difference: 差异描述
        
    Returns:
        是否为关键差异
    """
    # 非关键差异的模式
    non_critical_patterns = [
        # 属性顺序不同
        "properties.body",
        "properties.status_code",
        # 空值表示不同
        "query: None vs []",
        "body: None vs []",
        # 其他非关键差异
        "timeout:",
        "retry_times:"
    ]
    
    # 检查是否匹配任何非关键模式
    for pattern in non_critical_patterns:
        if pattern in difference:
            return False
    
    return True

def format_workflow_differences(differences: List[str]) -> str:
    """
    格式化工作流差异，生成易读的报告
    
    Args:
        differences: 差异列表
        
    Returns:
        格式化后的差异报告
    """
    if not differences:
        return "工作流一致，没有发现差异"
    
    report = "工作流差异报告:\n"
    
    # 对差异进行分类
    critical = []
    non_critical = []
    
    for diff in differences:
        if is_critical_difference(diff):
            critical.append(diff)
        else:
            non_critical.append(diff)
    
    # 添加关键差异
    if critical:
        report += "\n关键差异（可能影响工作流行为）:\n"
        for i, diff in enumerate(critical, 1):
            report += f"  {i}. {diff}\n"
    
    # 添加非关键差异
    if non_critical:
        report += "\n非关键差异（不影响工作流行为）:\n"
        for i, diff in enumerate(non_critical, 1):
            report += f"  {i}. {diff}\n"
    
    return report
