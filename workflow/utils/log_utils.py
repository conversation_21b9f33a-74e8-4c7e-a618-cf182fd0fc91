'''
Author       : winsonyang 
Date         : 2025-04-15 15:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 21:00:00
FilePath     : /aigc-api-test/workflow/utils/log_utils.py
Description  : 统一的日志记录工具模块（使用结构化日志）

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
# 导入结构化日志系统
from workflow.utils.structured_logger import workflow_logger, structured_logger

# 向后兼容：导出所有需要的符号
__all__ = ['workflow_logger', 'structured_logger']

# 为了兼容性，保留WorkflowLogger类名
WorkflowLogger = type(workflow_logger).__class__ 