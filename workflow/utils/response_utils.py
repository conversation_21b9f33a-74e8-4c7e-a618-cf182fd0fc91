'''
Author       : winsonyang 
Date         : 2025-04-15 13:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-04-17 18:24:01
FilePath     : /aigc-api-test/workflow/utils/response_utils.py
Description  : API响应处理工具模块，提供统一的响应处理与验证功能

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
from typing import Dict, Any, Optional, Union, List, Callable, TypeVar, Generic, cast

from workflow.utils.error_handling import (
    WorkflowError, 
    WorkflowValidationError,
    ApiRequestError,
    WorkflowNotFoundError,
    workflow_logger
)

# 定义泛型类型变量，用于表示响应中的数据类型
T = TypeVar('T')


class ApiResponse(Generic[T]):
    """API响应封装类，提供统一的响应访问和验证方法"""
    
    def __init__(self, response_data: Dict[str, Any]):
        """
        初始化API响应对象
        
        Args:
            response_data: 包含body和headers的响应数据字典
        """
        self.raw_response = response_data
        self.body = response_data.get("body", {})
        self.headers = response_data.get("headers", {})
        self.code = self.body.get("code")
        self.message = self.body.get("message", "")
        self.data = self.body.get("data", {})
    
    @property
    def trace_id(self) -> str:
        """获取响应的跟踪ID"""
        return self.headers.get("X-Trace-Id", "")
    
    @property
    def is_success(self) -> bool:
        """检查响应是否成功"""
        # 当code不存在时返回False，因为无法确定成功状态
        return self.code is not None and self.code == 0
    
    def validate_success(self, api_name: str) -> 'ApiResponse[T]':
        """
        验证API响应是否成功，如果不成功则抛出异常
        
        Args:
            api_name: API名称，用于错误日志
            
        Returns:
            当前响应对象，方便链式调用
            
        Raises:
            ApiRequestError: 如果响应不成功
        """
        if self.code is None:
            details = {
                "code": self.code,
                "message": self.message,
                "trace_id": self.trace_id
            }
            error_msg = f"API '{api_name}' 请求失败: 响应中缺少code字段"
            workflow_logger.error(error_msg)
            raise ApiRequestError(message=error_msg, status_code=500, details=details)
        
        if not self.is_success:
            details = {
                "code": self.code,
                "message": self.message,
                "trace_id": self.trace_id
            }
            error_msg = f"API '{api_name}' 请求失败: {self.message}"
            workflow_logger.error(error_msg)
            raise ApiRequestError(message=error_msg, status_code=self.code or 500, details=details)
        return self
    
    def get_data_field(self, field_name: str, required: bool = True) -> Any:
        """
        从响应数据中获取指定字段
        
        Args:
            field_name: 要获取的字段名
            required: 字段是否必需，如果为True且字段不存在则抛出异常
            
        Returns:
            字段值
            
        Raises:
            WorkflowValidationError: 如果字段是必需的但不存在
        """
        if field_name not in self.data:
            if required:
                error_msg = f"缺少必需的响应字段: {field_name}"
                workflow_logger.error(error_msg)
                raise WorkflowValidationError(
                    message=error_msg,
                    details={"response_data": self.data, "missing_field": field_name}
                )
            return None
        return self.data[field_name]
    
    def validate_field_exists(self, field_name: str, parent_dict: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """
        验证字段是否存在
        
        Args:
            field_name: 要验证的字段名
            parent_dict: 要验证的父字典，默认为响应数据
            
        Returns:
            当前响应对象，方便链式调用
            
        Raises:
            WorkflowValidationError: 如果字段不存在
        """
        target_dict = self.data if parent_dict is None else parent_dict
        
        if field_name not in target_dict:
            error_msg = f"响应中缺少字段: {field_name}"
            workflow_logger.error(error_msg)
            raise WorkflowValidationError(
                message=error_msg,
                details={"parent_dict": target_dict}
            )
        return self
    
    def validate_field_equals(self, field_name: str, expected_value: Any, parent_dict: Optional[Dict[str, Any]] = None) -> 'ApiResponse[T]':
        """
        验证字段值是否等于预期值
        
        Args:
            field_name: 要验证的字段名
            expected_value: 预期值
            parent_dict: 要验证的父字典，默认为响应数据
            
        Returns:
            当前响应对象，方便链式调用
            
        Raises:
            WorkflowValidationError: 如果字段值不等于预期值
        """
        target_dict = self.data if parent_dict is None else parent_dict
        self.validate_field_exists(field_name, target_dict)
        
        actual_value = target_dict[field_name]
        if actual_value != expected_value:
            error_msg = f"字段值不匹配: {field_name}，期望 {expected_value}，实际 {actual_value}"
            workflow_logger.error(error_msg)
            raise WorkflowValidationError(
                message=error_msg,
                details={
                    "field_name": field_name,
                    "expected_value": expected_value,
                    "actual_value": actual_value
                }
            )
        return self
    
    def extract_typed_data(self, data_class: Callable[[Dict[str, Any]], T]) -> T:
        """
        将响应数据提取为指定类型
        
        Args:
            data_class: 用于转换数据的可调用对象
            
        Returns:
            转换后的数据
        """
        return data_class(self.data)


# 常用响应处理函数

def process_workflow_id_response(response: Dict[str, Any]) -> str:
    """
    处理创建工作流的响应，提取工作流ID
    
    Args:
        response: API响应
        
    Returns:
        工作流ID
        
    Raises:
        ApiRequestError: 如果响应不成功
        WorkflowValidationError: 如果响应中没有工作流ID
    """
    api_resp = ApiResponse(response)
    api_resp.validate_success("create_workflow")
    
    workflow_id = api_resp.get_data_field("workflow_id")
    if not workflow_id or not isinstance(workflow_id, str):
        raise WorkflowValidationError(
            message="无效的工作流ID",
            details={"workflow_id": workflow_id}
        )
    
    workflow_logger.info(f"成功获取工作流ID: {workflow_id}")
    return workflow_id


def process_workflow_detail_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理工作流详情的响应，提取工作流配置
    
    Args:
        response: API响应
        
    Returns:
        工作流配置
        
    Raises:
        ApiRequestError: 如果响应不成功
        WorkflowValidationError: 如果响应中没有工作流配置
    """
    api_resp = ApiResponse(response)
    api_resp.validate_success("workflow_detail")
    
    workflow_dsl = api_resp.get_data_field("workflow_dsl")
    if not workflow_dsl or not isinstance(workflow_dsl, dict):
        raise WorkflowValidationError(
            message="无效的工作流配置",
            details={"workflow_dsl": workflow_dsl}
        )
    
    workflow_logger.info(f"成功获取工作流配置，节点数量: {len(workflow_dsl.get('nodes', []))}")
    return workflow_dsl


def process_execution_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理工作流执行的响应
    
    Args:
        response: API响应
        
    Returns:
        执行结果数据
        
    Raises:
        ApiRequestError: 如果响应不成功
    """
    api_resp = ApiResponse(response)
    api_resp.validate_success("workflow_execution")
    
    # 返回完整的data部分，因为执行结果的结构可能多样
    workflow_logger.info(f"成功获取工作流执行结果")
    return api_resp.data


def compare_workflow_dsl(source_dsl: Dict[str, Any], target_dsl: Dict[str, Any]) -> bool:
    """
    比较两个工作流配置是否一致
    
    Args:
        source_dsl: 源工作流配置
        target_dsl: 目标工作流配置
        
    Returns:
        是否一致
    """
    # 主要比较节点配置
    if "nodes" not in source_dsl or "nodes" not in target_dsl:
        workflow_logger.warning("工作流配置中缺少nodes字段")
        return False
    
    return source_dsl["nodes"] == target_dsl["nodes"] 