import json

def load_test_cases(file_path: str) -> list:
    """
    加载测试用例并确保每个测试用例都有test_name字段
    """
    with open(file_path, 'r') as f:
        test_cases = json.load(f)
        # 确保每个测试用例都有test_name
        for i, tc in enumerate(test_cases):
            if "test_name" not in tc:
                tc["test_name"] = f"test_case{i}"
        return test_cases

def get_test_case_ids(test_cases):
    """从测试用例列表中提取ID"""
    return [tc["test_name"] for tc in test_cases]
