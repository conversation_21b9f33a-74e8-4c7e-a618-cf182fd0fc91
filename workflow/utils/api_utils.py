# -*- coding: utf-8 -*-

'''
Author       : winsonyang 
Date         : 2025-04-15 10:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 16:35:00
FilePath     : /aigc-api-test/workflow/utils/api_utils.py
Description  : API请求工具模块，提供通用的API请求功能

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
import time
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Dict, Any, Optional, Union, Tuple

from workflow.utils.error_handling import (
    handle_api_error, 
    WorkflowValidationError,
    WorkflowConfigError,
    WorkflowAuthError,
    ApiRequestError
)
from workflow.utils.log_utils import workflow_logger


class APIClient:
    """API客户端，支持连接池和重试机制"""
    
    def __init__(self, base_url: str = "https://test.hunyuan.woa.com", timeout: Tuple[int, int] = (5, 30)):
        self.base_url = base_url
        self.timeout = timeout
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建带重试策略的会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy, pool_connections=10, pool_maxsize=20)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _build_headers(self, route_env: Optional[str], auth_token: Optional[str], 
                      extra_headers: Optional[Dict[str, str]]) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        if route_env:
            headers['X-Route-Env'] = route_env
        
        if auth_token:
            headers['Authorization'] = f'Bearer {auth_token}'
        
        if extra_headers:
            headers.update(extra_headers)
        
        return headers
    
    def _handle_response(self, response: requests.Response, url: str, method: str) -> Dict[str, Any]:
        """处理API响应"""
        response_headers = dict(response.headers)
        # 记录API响应，现在会自动提取trace_id等信息
        response_time_ms = response.elapsed.total_seconds() * 1000 if hasattr(response, 'elapsed') else 0
        workflow_logger.api_response(
            method=method,
            url=url,
            status_code=response.status_code,
            response_time_ms=response_time_ms,
            response_data={"headers": response_headers}
        )
        
        try:
            response.raise_for_status()
            # 保持原始的响应格式，确保向后兼容
            result = {
                "body": response.json(),
                "headers": response_headers
            }
            return result
        except requests.exceptions.HTTPError as e:
            error_details = {
                "status_code": response.status_code,
                "response_text": response.text[:500]  # 限制错误信息长度
            }
            # 记录详细错误信息
            workflow_logger.error(f"API请求失败: {response.status_code}", {
                "url": url,
                "method": method,
                "response_body": response.text
            })
            raise ApiRequestError(
                message=f"HTTP错误: {response.status_code}",
                status_code=response.status_code,
                details=error_details
            ) from e
        except json.JSONDecodeError as e:
            raise ApiRequestError(
                message="响应不是有效的JSON格式",
                status_code=response.status_code,
                details={"response_text": response.text[:200]}
            ) from e
    
    def request(self, endpoint: str, payload: Dict[str, Any], method: str = "POST",
                route_env: Optional[str] = None, headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None) -> Dict[str, Any]:
        """
        发送API请求
        
        Args:
            endpoint: API端点路径
            payload: 请求负载数据
            method: 请求方法
            route_env: 路由环境
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            API响应数据
        """
        url = f"{self.base_url}{endpoint}"
        headers = self._build_headers(route_env, auth_token, headers)
        
        # 使用结构化日志记录API请求
        workflow_logger.api_request(method, url, headers, payload)
        
        try:
            response = self.session.request(
                method=method,
                url=url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            return self._handle_response(response, url, method)
            
        except requests.exceptions.RequestException as e:
            error_details = {
                "method": method,
                "url": url,
                "timeout": self.timeout,
                "error_type": type(e).__name__
            }
            raise ApiRequestError(
                message=f"网络请求失败: {str(e)}",
                status_code=500,
                details=error_details
            ) from e
    
    def stream_request(self, endpoint: str, payload: Dict[str, Any], 
                      route_env: Optional[str] = None, headers: Optional[Dict[str, str]] = None,
                      auth_token: Optional[str] = None,
                      accept: str = 'text/event-stream') -> requests.Response:
        """
        发送流式API请求
        
        Args:
            endpoint: API端点路径
            payload: 请求负载数据
            route_env: 路由环境
            headers: 额外的请求头
            auth_token: 认证令牌
            accept: Accept头的值
            
        Returns:
            流式响应对象
        """
        url = f"{self.base_url}{endpoint}"
        
        # 构建请求头，覆盖Accept头
        request_headers = self._build_headers(route_env, auth_token, headers)
        request_headers['Accept'] = accept
        
        # 使用结构化日志记录流式API请求
        workflow_logger.api_request("POST", url, request_headers, payload)
        
        try:
            # 注意：流式请求不使用session，因为需要手动管理连接
            response = requests.post(
                url=url,
                headers=request_headers,
                json=payload,
                stream=True,
                timeout=self.timeout
            )
            
            # 检查HTTP错误
            response.raise_for_status()
            
            # 记录响应
            response_headers = dict(response.headers)
            response_time_ms = response.elapsed.total_seconds() * 1000 if hasattr(response, 'elapsed') else 0
            workflow_logger.api_response(
                method="POST",
                url=url,
                status_code=response.status_code,
                response_time_ms=response_time_ms,
                response_data={"headers": response_headers}
            )
            
            return response
            
        except requests.exceptions.HTTPError as e:
            error_details = {
                "status_code": response.status_code,
                "response_text": response.text[:500] if hasattr(response, 'text') else None
            }
            raise ApiRequestError(
                message=f"HTTP错误: {response.status_code}",
                status_code=response.status_code,
                details=error_details
            ) from e
        except requests.exceptions.RequestException as e:
            error_details = {
                "method": "POST",
                "url": url,
                "timeout": self.timeout,
                "error_type": type(e).__name__
            }
            raise ApiRequestError(
                message=f"网络请求失败: {str(e)}",
                status_code=500,
                details=error_details
            ) from e


# 保持向后兼容的函数
def make_api_request(
    endpoint: str,
    payload: Dict[str, Any],
    method: str = "POST",
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None,
    retry_count: int = 3,
    retry_delay: int = 1,
    timeout: Tuple[int, int] = (5, 30)
) -> Dict[str, Any]:
    """
    向后兼容的API请求函数
    
    Args:
        endpoint: API端点路径
        payload: 请求负载数据
        method: 请求方法，默认为POST
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        retry_count: 重试次数，默认为3 (已弃用，使用APIClient内置重试)
        retry_delay: 初始重试延迟秒数，默认为1 (已弃用)
        timeout: 连接超时和读取超时秒数，默认(5, 30)
        
    Returns:
        API响应结果，包含响应体和响应头
        
    Raises:
        ApiRequestError: 如果API请求失败且重试次数耗尽
        WorkflowValidationError: 如果提供了不支持的HTTP方法
        WorkflowAuthError: 如果认证令牌无效
    """
    client = APIClient(base_url=base_url, timeout=timeout)
    return client.request(
        endpoint=endpoint,
        payload=payload,
        method=method,
        route_env=route_env,
        headers=headers,
        auth_token=auth_token
    )


def load_workflow_from_file(workflow_file: str) -> Dict[str, Any]:
    """
    从文件加载工作流配置
    
    Args:
        workflow_file: 工作流配置文件路径
        
    Returns:
        工作流配置字典
        
    Raises:
        WorkflowConfigError: 如果无法加载工作流配置
    """
    if not workflow_file:
        raise WorkflowConfigError(
            message="工作流配置文件路径不能为空",
            details={"parameter": "workflow_file"}
        )
        
    try:
        with open(workflow_file, 'r') as f:
            workflow_config = json.load(f)
            workflow_logger.info("成功加载工作流配置", {"file_path": workflow_file})
            return workflow_config
    except FileNotFoundError as e:
        workflow_logger.error("工作流配置文件未找到", {
            "file_path": workflow_file,
            "error": str(e)
        })
        raise WorkflowConfigError(
            message=f"工作流配置文件未找到: {workflow_file}",
            details={"file_path": workflow_file}
        ) from e
    except json.JSONDecodeError as e:
        workflow_logger.error("工作流配置文件包含无效的JSON", {
            "file_path": workflow_file,
            "error": str(e)
        })
        raise WorkflowConfigError(
            message=f"工作流配置文件包含无效的JSON: {workflow_file}",
            details={"file_path": workflow_file, "json_error": str(e)}
        ) from e
    except Exception as e:
        workflow_logger.error("加载工作流配置时出错", {
            "file_path": workflow_file,
            "error": str(e)
        })
        raise WorkflowConfigError(
            message=f"加载工作流配置时出错: {str(e)}",
            details={"file_path": workflow_file}
        ) from e 