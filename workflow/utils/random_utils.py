import random
import time
import uuid
from typing import List, Union


def generate_random_task_id(prefix: str = "", length: int = 10) -> str:
    """
    生成随机的task_id
    
    Args:
        prefix: 前缀字符串，默认为空
        length: 随机数部分的长度，默认为10
    
    Returns:
        生成的随机task_id字符串
    """
    # 生成随机数字字符串
    random_part = ''.join([str(random.randint(0, 9)) for _ in range(length)])
    
    if prefix:
        return f"{prefix}_{random_part}"
    else:
        return random_part


def generate_random_task_ids(count: int, prefix: str = "", length: int = 10) -> List[str]:
    """
    生成多个随机的task_id
    
    Args:
        count: 需要生成的task_id数量
        prefix: 前缀字符串，默认为空
        length: 每个随机数部分的长度，默认为10
    
    Returns:
        生成的随机task_id列表
    """
    return [generate_random_task_id(prefix, length) for _ in range(count)]


def generate_uuid_task_id(prefix: str = "") -> str:
    """
    使用UUID生成task_id，确保全局唯一性
    
    Args:
        prefix: 前缀字符串，默认为空
    
    Returns:
        基于UUID的task_id字符串
    """
    uuid_part = str(uuid.uuid4()).replace('-', '')[:12]  # 取UUID的前12位
    
    if prefix:
        return f"{prefix}_{uuid_part}"
    else:
        return uuid_part


def generate_timestamp_task_id(prefix: str = "") -> str:
    """
    使用时间戳生成task_id
    
    Args:
        prefix: 前缀字符串，默认为空
    
    Returns:
        基于时间戳的task_id字符串
    """
    timestamp = str(int(time.time() * 1000))  # 毫秒时间戳
    random_suffix = str(random.randint(100, 999))  # 3位随机后缀
    
    if prefix:
        return f"{prefix}_{timestamp}_{random_suffix}"
    else:
        return f"{timestamp}_{random_suffix}"


def process_random_placeholders(data: Union[str, List, dict]) -> Union[str, List, dict]:
    """
    处理数据中的随机数占位符，将其替换为实际的随机值
    
    支持的占位符格式：
    - "__RANDOM_TASK_ID__": 生成单个随机task_id
    - "__RANDOM_TASK_IDS_N__": 生成N个随机task_id的数组（N为数字）
    
    Args:
        data: 需要处理的数据，可以是字符串、列表或字典
    
    Returns:
        处理后的数据
    """
    if isinstance(data, str):
        if data == "__RANDOM_TASK_ID__":
            return generate_random_task_id()
        elif data.startswith("__RANDOM_TASK_IDS_") and data.endswith("__"):
            # 提取数量，格式如：__RANDOM_TASK_IDS_2__
            try:
                count_str = data[18:-2]  # 去掉前缀和后缀
                count = int(count_str)
                return generate_random_task_ids(count)
            except (ValueError, IndexError):
                return data
        else:
            return data
    elif isinstance(data, list):
        return [process_random_placeholders(item) for item in data]
    elif isinstance(data, dict):
        return {key: process_random_placeholders(value) for key, value in data.items()}
    else:
        return data


# 兼容性别名函数，便于测试用例使用
def random_task_id() -> str:
    """生成单个随机task_id的简化函数"""
    return generate_random_task_id()


def random_task_ids(count: int) -> List[str]:
    """生成多个随机task_id的简化函数"""
    return generate_random_task_ids(count) 