'''
Author       : winsonyang 
Date         : 2025-01-20 21:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 21:00:00
FilePath     : /aigc-api-test/workflow/utils/structured_logger_example.py
Description  : 结构化日志使用示例

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import time
from workflow.utils.structured_logger import structured_logger as logger


def example_basic_usage():
    """基础使用示例"""
    print("\n=== 基础日志示例 ===")
    
    # 简单日志
    logger.info("这是一条普通的信息日志")
    
    # 带结构化数据的日志
    logger.info("用户登录成功", {
        "user_id": "12345",
        "login_type": "password",
        "ip_address": "***********"
    })
    
    # 使用关键字参数
    logger.info("订单创建成功", 
                order_id="ORD-2025-001",
                amount=99.99,
                currency="CNY")


def example_api_logging():
    """API日志示例"""
    print("\n=== API日志示例 ===")
    
    # 记录API请求
    logger.api_request(
        method="POST",
        url="https://api.example.com/v1/workflow/create",
        headers={"Content-Type": "application/json", "Authorization": "Bearer xxx"},
        payload={"name": "测试工作流", "type": "sync"},
        trace_id="trace-123456"
    )
    
    # 模拟请求处理
    time.sleep(0.1)
    
    # 记录API响应
    logger.api_response(
        method="POST",
        url="https://api.example.com/v1/workflow/create",
        status_code=200,
        response_time_ms=123.45,
        response_data={
            "headers": {"X-Trace-Id": "trace-123456"},
            "body": {"code": 0, "data": {"workflow_id": "wf-001"}}
        }
    )


def example_workflow_logging():
    """工作流日志示例"""
    print("\n=== 工作流日志示例 ===")
    
    workflow_id = "wf-20250120-001"
    
    # 工作流创建
    start_time = time.time()
    logger.workflow_operation("create", workflow_id, 
                            name="数据处理工作流",
                            type="async",
                            nodes_count=5)
    
    # 工作流执行
    logger.workflow_operation("execute", workflow_id,
                            execute_id="exec-001",
                            parameters={"input": "data.csv"},
                            duration_ms=(time.time() - start_time) * 1000)
    
    # 工作流状态更新
    logger.workflow_operation("status_change", workflow_id,
                            execute_id="exec-001",
                            from_status="running",
                            to_status="success",
                            duration_ms=1234.56)


def example_error_logging():
    """错误日志示例"""
    print("\n=== 错误日志示例 ===")
    
    # 一般错误
    logger.error("数据库连接失败",
                error_code="DB_CONN_001",
                database="workflow_db",
                retry_count=3,
                last_error="Connection timeout")
    
    # 带异常信息的错误
    try:
        1 / 0
    except Exception as e:
        logger.error("计算错误",
                    error_type=type(e).__name__,
                    error_message=str(e),
                    operation="divide",
                    values={"numerator": 1, "denominator": 0})


def example_request_context():
    """请求上下文示例"""
    print("\n=== 请求上下文示例 ===")
    
    # 设置请求级别的上下文
    logger.set_request_context(
        trace_id="req-trace-789",
        user_id="user-123",
        session_id="session-456"
    )
    
    # 后续的日志都会自动包含这些上下文信息
    logger.info("处理用户请求")
    logger.info("查询用户数据", table="users", query_time_ms=23.4)
    logger.info("更新用户状态", status="active")
    
    # 清除请求上下文
    logger.clear_request_context()
    
    # 这条日志不会包含上面的上下文信息
    logger.info("请求处理完成")


def example_timer_decorator():
    """计时装饰器示例"""
    print("\n=== 计时装饰器示例 ===")
    
    @logger.with_timer("数据处理任务")
    def process_data(size: int):
        # 模拟数据处理
        time.sleep(0.1)
        return f"处理了 {size} 条数据"
    
    # 正常执行
    result = process_data(1000)
    print(f"结果: {result}")
    
    # 异常情况
    @logger.with_timer("错误任务")
    def error_task():
        raise ValueError("模拟错误")
    
    try:
        error_task()
    except ValueError:
        pass  # 错误已被记录


def example_test_logging():
    """测试日志示例"""
    print("\n=== 测试日志示例 ===")
    
    # 测试成功
    logger.test_execution(
        test_name="test_workflow_create",
        status="passed",
        duration_ms=156.78,
        assertions_count=5,
        test_type="integration"
    )
    
    # 测试失败
    logger.test_execution(
        test_name="test_workflow_timeout",
        status="failed",
        duration_ms=5023.45,
        error_message="Workflow execution timeout",
        expected_duration_ms=3000
    )


def example_json_format():
    """JSON格式输出示例"""
    print("\n=== JSON格式输出示例 ===")
    
    # 创建JSON格式的日志器
    from workflow.utils.structured_logger import StructuredLogger
    json_logger = StructuredLogger(name="workflow_json", output_format="json")
    
    # 输出JSON格式的日志
    json_logger.info("JSON格式日志",
                    event="user.action",
                    action="click_button",
                    button_id="submit",
                    timestamp_ms=int(time.time() * 1000))


if __name__ == "__main__":
    # 运行所有示例
    example_basic_usage()
    example_api_logging()
    example_workflow_logging()
    example_error_logging()
    example_request_context()
    example_timer_decorator()
    example_test_logging()
    example_json_format()
    
    print("\n所有示例执行完成！")