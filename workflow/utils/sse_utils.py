'''
Author       : winsonyang 
Date         : 2025-01-04 
LastEditors  : winsonyang 
LastEditTime : 2025-01-04 
FilePath     : /aigc-api-test/workflow/utils/sse_utils.py
Description  : 通用SSE流处理工具

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
from typing import Generator, Dict, Any, Optional
from workflow.utils.log_utils import workflow_logger


def process_sse_stream(
    response,
    logger: Optional[object] = None
) -> Generator[Dict[str, Any], None, None]:
    """
    通用SSE流处理函数
    
    Args:
        response: HTTP响应对象
        logger: 日志记录器，默认使用workflow_logger
    
    Yields:
        解析后的事件数据
    """
    if logger is None:
        logger = workflow_logger
    
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                try:
                    event_data = json.loads(line[6:])
                    yield event_data
                except json.JSONDecodeError as e:
                    logger.error("解析SSE事件数据失败", {
                        "line": line,
                        "error": str(e)
                    })
                    continue


def process_sse_stream_with_headers(
    response,
    response_headers: Dict[str, str],
    logger: Optional[object] = None
) -> Generator[tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    通用SSE流处理函数（带响应头）
    
    Args:
        response: HTTP响应对象
        response_headers: 响应头字典
        logger: 日志记录器，默认使用workflow_logger
    
    Yields:
        (事件数据, 响应头)元组
    """
    if logger is None:
        logger = workflow_logger
    
    for line in response.iter_lines():
        if line:
            line = line.decode('utf-8')
            if line.startswith('data: '):
                try:
                    event_data = json.loads(line[6:])
                    yield event_data, response_headers
                except json.JSONDecodeError as e:
                    logger.error("解析SSE事件数据失败", {
                        "line": line,
                        "error": str(e)
                    })
                    continue