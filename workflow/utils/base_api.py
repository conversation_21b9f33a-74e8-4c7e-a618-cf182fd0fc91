'''
Author       : winsonyang 
Date         : 2025-01-20 17:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 17:00:00
FilePath     : /aigc-api-test/workflow/utils/base_api.py
Description  : 基础API类，提供通用的API功能

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
from typing import Dict, Any, Optional, List, Generator, Tuple, Union
from abc import ABC, abstractmethod
from functools import wraps

from workflow.utils.api_utils import APIClient, load_workflow_from_file
from workflow.utils.error_handling import WorkflowValidationError, workflow_logger
from workflow.utils.sse_utils import process_sse_stream_with_headers


class BaseWorkflowAPI(ABC):
    """工作流API基类，提供通用功能"""
    
    def __init__(self, base_url: str = "https://test.hunyuan.woa.com"):
        self.client = APIClient(base_url=base_url)
        self.base_url = base_url
    
    def validate_required_params(self, **params) -> None:
        """
        验证必需参数
        
        Args:
            **params: 参数名和参数值的键值对
            
        Raises:
            WorkflowValidationError: 如果有必需参数未提供
        """
        errors = {}
        for param_name, param_value in params.items():
            if param_value is None or (isinstance(param_value, str) and not param_value.strip()):
                errors[param_name] = f"{param_name}必须提供"
        
        if errors:
            raise WorkflowValidationError(
                message="参数验证失败",
                details={"validation_errors": errors, "provided_parameters": params}
            )
    
    def validate_workflow_id(self, workflow_id: str) -> None:
        """验证工作流ID"""
        self.validate_required_params(workflow_id=workflow_id)
    
    def validate_messages(self, messages: List[Dict[str, str]]) -> None:
        """
        验证聊天消息格式
        
        Args:
            messages: 消息列表
            
        Raises:
            WorkflowValidationError: 如果消息格式不正确
        """
        if not messages or not isinstance(messages, list):
            raise WorkflowValidationError(
                message="messages必须提供且为非空数组",
                details={"provided_parameters": {"messages": messages}}
            )
        
        for i, message in enumerate(messages):
            if not isinstance(message, dict) or 'role' not in message or 'content' not in message:
                raise WorkflowValidationError(
                    message=f"messages[{i}]格式不正确，必须包含role和content字段",
                    details={"message_index": i, "message": message}
                )
    
    def build_headers(self, route_env: Optional[str] = None, 
                     auth_token: Optional[str] = None,
                     extra_headers: Optional[Dict[str, str]] = None,
                     accept: str = 'application/json') -> Dict[str, str]:
        """
        构建请求头
        
        Args:
            route_env: 路由环境
            auth_token: 认证令牌
            extra_headers: 额外的请求头
            accept: Accept头的值
            
        Returns:
            完整的请求头字典
        """
        headers = {
            'Content-Type': 'application/json',
            'Accept': accept
        }
        
        if route_env:
            headers['X-Route-Env'] = route_env
        
        if auth_token:
            headers['Authorization'] = f'Bearer {auth_token}'
        
        if extra_headers:
            headers.update(extra_headers)
        
        return headers
    
    def log_operation(self, operation: str, workflow_id: Optional[str] = None, **kwargs):
        """记录工作流操作"""
        workflow_logger.workflow_operation(operation, workflow_id, parameters=kwargs)
    
    @abstractmethod
    def get_endpoint(self) -> str:
        """获取API端点路径（子类必须实现）"""
        pass
    
    def make_request(self, 
                    payload: Dict[str, Any],
                    method: str = "POST",
                    route_env: Optional[str] = None,
                    headers: Optional[Dict[str, str]] = None,
                    auth_token: Optional[str] = None) -> Dict[str, Any]:
        """
        发送同步API请求
        
        Args:
            payload: 请求负载
            method: HTTP方法
            route_env: 路由环境
            headers: 额外请求头
            auth_token: 认证令牌
            
        Returns:
            API响应
        """
        endpoint = self.get_endpoint()
        return self.client.request(
            endpoint=endpoint,
            payload=payload,
            method=method,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token
        )
    
    def make_streaming_request(self,
                             payload: Dict[str, Any],
                             route_env: Optional[str] = None,
                             headers: Optional[Dict[str, str]] = None,
                             auth_token: Optional[str] = None,
                             stream: bool = True) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
        """
        发送流式API请求
        
        Args:
            payload: 请求负载
            route_env: 路由环境
            headers: 额外请求头
            auth_token: 认证令牌
            stream: 是否流式返回
            
        Yields:
            (事件数据, 响应头)元组
        """
        endpoint = self.get_endpoint()
        
        if stream:
            # 使用APIClient的stream_request方法
            accept = 'text/event-stream'
            with self.client.stream_request(
                endpoint=endpoint,
                payload=payload,
                route_env=route_env,
                headers=headers,
                auth_token=auth_token,
                accept=accept
            ) as response:
                response_headers = dict(response.headers)
                
                # 处理SSE流
                for event_data, headers in process_sse_stream_with_headers(response, response_headers):
                    yield event_data, headers
        else:
            # 非流式请求使用普通request方法
            result = self.client.request(
                endpoint=endpoint,
                payload=payload,
                route_env=route_env,
                headers=headers,
                auth_token=auth_token
            )
            yield result["body"], result["headers"]


def api_endpoint(operation_name: str):
    """
    API端点装饰器，用于记录操作日志和处理通用错误
    
    Args:
        operation_name: 操作名称
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # 记录操作开始
            workflow_id = kwargs.get('workflow_id')
            if not workflow_id and args:
                # 如果kwargs中没有workflow_id，尝试从位置参数获取
                workflow_id = args[0] if len(args) > 0 else None
            
            # 创建日志参数，避免重复workflow_id
            log_params = {k: v for k, v in kwargs.items() if k != 'workflow_id'}
            self.log_operation(operation_name, workflow_id, **log_params)
            
            try:
                return func(self, *args, **kwargs)
            except WorkflowValidationError:
                # 验证错误直接抛出
                raise
            except Exception as e:
                # 其他错误记录后抛出
                workflow_logger.error("{} 操作失败".format(operation_name), {
                    "error": str(e),
                    "workflow_id": workflow_id
                })
                raise
        
        return wrapper
    return decorator


class WorkflowParameterValidator:
    """工作流参数验证器"""
    
    @staticmethod
    def validate_workspace(workspace: str) -> None:
        """验证工作空间参数"""
        if not workspace or not isinstance(workspace, str):
            raise WorkflowValidationError(
                message="workspace必须是非空字符串",
                details={"provided_value": workspace}
            )
    
    @staticmethod
    def validate_name(name: str) -> None:
        """验证名称参数"""
        if not name or not isinstance(name, str):
            raise WorkflowValidationError(
                message="name必须是非空字符串",
                details={"provided_value": name}
            )
        
        if len(name) > 100:
            raise WorkflowValidationError(
                message="name长度不能超过100个字符",
                details={"name_length": len(name)}
            )
    
    @staticmethod
    def validate_workflow_config(workflow: Optional[Dict[str, Any]] = None, 
                               workflow_file: Optional[str] = None) -> Dict[str, Any]:
        """
        验证并获取工作流配置
        
        Args:
            workflow: 工作流配置字典
            workflow_file: 工作流配置文件路径
            
        Returns:
            工作流配置字典
            
        Raises:
            WorkflowValidationError: 如果配置无效
        """
        if workflow is None and workflow_file is None:
            raise WorkflowValidationError(
                message="必须提供workflow或workflow_file参数",
                details={"workflow": workflow, "workflow_file": workflow_file}
            )
        
        if workflow is not None and workflow_file is not None:
            raise WorkflowValidationError(
                message="workflow和workflow_file参数不能同时提供",
                details={"workflow": workflow, "workflow_file": workflow_file}
            )
        
        if workflow_file:
            return load_workflow_from_file(workflow_file)
        
        return workflow