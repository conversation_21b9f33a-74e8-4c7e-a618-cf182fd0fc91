'''
Author       : winsonyang 
Date         : 2025-01-20 21:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 21:00:00
FilePath     : /aigc-api-test/workflow/utils/structured_logger.py
Description  : 结构化日志记录器，提供更好的日志格式和字段管理

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import logging
import json
import time
from datetime import datetime
from typing import Dict, Any, Optional, Union
from functools import wraps
import threading
import os


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, output_format: str = "json"):
        """
        初始化格式化器
        
        Args:
            output_format: 输出格式，支持 "json" 和 "readable"
        """
        super().__init__()
        self.output_format = output_format
        self._hostname = os.uname().nodename if hasattr(os, 'uname') else 'unknown'
        
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录"""
        # 基础字段
        log_data = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "hostname": self._hostname,
            "process_id": os.getpid(),
            "thread_name": threading.current_thread().name,
        }
        
        # 添加位置信息
        if record.pathname:
            log_data["location"] = {
                "file": record.pathname,
                "line": record.lineno,
                "function": record.funcName
            }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info)
            }
        
        # 合并额外的结构化数据
        if hasattr(record, 'structured_data'):
            # 提取特定字段到顶层
            structured = record.structured_data
            
            # 标准字段映射
            if "trace_id" in structured:
                log_data["trace_id"] = structured.pop("trace_id")
            if "api_name" in structured:
                log_data["api_name"] = structured.pop("api_name")
            if "duration_ms" in structured:
                log_data["duration_ms"] = structured.pop("duration_ms")
            if "error_code" in structured:
                log_data["error_code"] = structured.pop("error_code")
            if "workflow_id" in structured:
                log_data["workflow_id"] = structured.pop("workflow_id")
            if "execute_id" in structured:
                log_data["execute_id"] = structured.pop("execute_id")
            
            # 其余字段放入 context
            if structured:
                log_data["context"] = structured
        
        # 根据输出格式返回
        if self.output_format == "json":
            return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))
        else:
            # 可读格式
            parts = [
                f"[{log_data['timestamp']}]",
                f"[{log_data['level']}]",
                f"[{log_data.get('trace_id', '-')}]",
                log_data['message']
            ]
            
            # 添加关键字段
            if "api_name" in log_data:
                parts.append(f"api={log_data['api_name']}")
            if "duration_ms" in log_data:
                parts.append(f"duration={log_data['duration_ms']}ms")
            if "error_code" in log_data:
                parts.append(f"error_code={log_data['error_code']}")
                
            # 添加上下文
            if "context" in log_data:
                parts.append(f"context={json.dumps(log_data['context'], ensure_ascii=False)}")
                
            return " | ".join(parts)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str = "workflow", output_format: str = "readable"):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            output_format: 输出格式，"json" 或 "readable"
        """
        self.logger = logging.getLogger(name)
        self.output_format = output_format
        self._setup_logger()
        self._request_context = threading.local()
    
    def _setup_logger(self) -> None:
        """配置日志记录器"""
        if not self.logger.handlers:
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 设置结构化格式化器
            formatter = StructuredFormatter(output_format=self.output_format)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            self.logger.addHandler(console_handler)
            
            # 设置日志级别
            self.logger.setLevel(logging.INFO)
    
    def set_request_context(self, **kwargs):
        """设置请求级别的上下文信息"""
        if not hasattr(self._request_context, 'data'):
            self._request_context.data = {}
        self._request_context.data.update(kwargs)
    
    def clear_request_context(self):
        """清除请求上下文"""
        if hasattr(self._request_context, 'data'):
            self._request_context.data = {}
    
    def _get_context(self) -> Dict[str, Any]:
        """获取当前上下文"""
        return getattr(self._request_context, 'data', {}).copy()
    
    def _log(self, level: str, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """内部日志记录方法"""
        # 合并请求上下文、传入的数据和kwargs
        context = self._get_context()
        if data:
            context.update(data)
        if kwargs:
            context.update(kwargs)
        
        # 创建日志记录
        log_method = getattr(self.logger, level.lower())
        record = log_method.__self__.makeRecord(
            self.logger.name,
            getattr(logging, level.upper()),
            "(structured)",
            0,
            message,
            (),
            None
        )
        
        # 添加结构化数据
        record.structured_data = context
        
        # 处理日志记录
        self.logger.handle(record)
    
    def debug(self, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录调试日志"""
        self._log("debug", message, data, **kwargs)
    
    def info(self, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录信息日志"""
        self._log("info", message, data, **kwargs)
    
    def warning(self, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录警告日志"""
        self._log("warning", message, data, **kwargs)
    
    def error(self, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录错误日志"""
        self._log("error", message, data, **kwargs)
    
    def critical(self, message: str, data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录严重错误日志"""
        self._log("critical", message, data, **kwargs)
    
    def api_request(self, method: str, url: str, headers: Dict[str, str], 
                   payload: Optional[Dict[str, Any]] = None, **kwargs):
        """记录API请求"""
        # 提取API名称
        api_name = url.split('/')[-1] if '/' in url else url
        
        # 准备日志数据
        data = {
            "event": "api.request",
            "api_name": api_name,
            "method": method,
            "url": url,
            "headers": {k: v for k, v in headers.items() if k.lower() != 'authorization'},
        }
        
        if payload:
            data["payload_size"] = len(json.dumps(payload))
            # 只记录关键字段
            if "workflow_id" in payload:
                data["workflow_id"] = payload["workflow_id"]
            if "execute_id" in payload:
                data["execute_id"] = payload["execute_id"]
        
        data.update(kwargs)
        self.info("API请求", data)
    
    def api_response(self, method: str, url: str, status_code: int, 
                    response_time_ms: float, response_data: Optional[Dict[str, Any]] = None, **kwargs):
        """记录API响应"""
        # 提取API名称
        api_name = url.split('/')[-1] if '/' in url else url
        
        # 准备日志数据
        data = {
            "event": "api.response",
            "api_name": api_name,
            "method": method,
            "url": url,
            "status_code": status_code,
            "duration_ms": round(response_time_ms, 2),
            "success": 200 <= status_code < 300
        }
        
        # 提取响应中的关键信息
        if response_data:
            if "headers" in response_data:
                trace_id = response_data["headers"].get("X-Trace-Id", "")
                if trace_id:
                    data["trace_id"] = trace_id
            
            if "body" in response_data:
                body = response_data["body"]
                if "code" in body:
                    data["response_code"] = body["code"]
                if "message" in body:
                    data["response_message"] = body["message"]
        
        data.update(kwargs)
        
        # 根据状态码选择日志级别
        if status_code >= 500:
            self.error("API响应-服务器错误", data)
        elif status_code >= 400:
            self.warning("API响应-客户端错误", data)
        else:
            self.info("API响应成功", data)
    
    def workflow_operation(self, operation: str, workflow_id: str, 
                         duration_ms: Optional[float] = None, **kwargs):
        """记录工作流操作"""
        data = {
            "event": f"workflow.{operation}",
            "workflow_id": workflow_id,
            "operation": operation
        }
        
        if duration_ms is not None:
            data["duration_ms"] = round(duration_ms, 2)
        
        data.update(kwargs)
        self.info(f"工作流操作-{operation}", data)
    
    def test_execution(self, test_name: str, status: str, duration_ms: float, **kwargs):
        """记录测试执行结果"""
        data = {
            "event": "test.execution",
            "test_name": test_name,
            "status": status,
            "duration_ms": round(duration_ms, 2),
            "success": status == "passed"
        }
        
        data.update(kwargs)
        
        if status == "passed":
            self.info("测试执行成功", data)
        else:
            self.error("测试执行失败", data)
    
    def with_timer(self, operation_name: str):
        """装饰器：自动记录操作耗时"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration_ms = (time.time() - start_time) * 1000
                    self.info(f"{operation_name}完成", {
                        "operation": operation_name,
                        "duration_ms": round(duration_ms, 2),
                        "success": True
                    })
                    return result
                except Exception as e:
                    duration_ms = (time.time() - start_time) * 1000
                    self.error(f"{operation_name}失败", {
                        "operation": operation_name,
                        "duration_ms": round(duration_ms, 2),
                        "success": False,
                        "error_type": type(e).__name__,
                        "error_message": str(e)
                    })
                    raise
            return wrapper
        return decorator


# 创建全局结构化日志实例
structured_logger = StructuredLogger(name="workflow", output_format="readable")

# 向后兼容：保留原有的workflow_logger名称
workflow_logger = structured_logger