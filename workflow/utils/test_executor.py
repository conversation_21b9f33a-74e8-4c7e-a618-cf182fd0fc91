'''
Author       : winsonyang 
Date         : 2025-01-04 
LastEditors  : winsonyang 
LastEditTime : 2025-01-04 
FilePath     : /aigc-api-test/workflow/utils/test_executor.py
Description  : 工作流测试执行器

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, List
from workflow.utils.random_utils import process_random_placeholders
from workflow.utils.log_utils import workflow_logger


class WorkflowTestExecutor:
    """工作流测试执行器"""
    
    def __init__(self, route_env: str, base_url: str, auth_token: str):
        self.route_env = route_env
        self.base_url = base_url
        self.auth_token = auth_token
        self.random_cache = {}
    
    def prepare_test_parameters(self, test_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备测试参数，处理随机数占位符
        
        Args:
            test_params: 原始测试参数
            
        Returns:
            处理后的测试参数
        """
        # 深拷贝防止修改原始参数
        import copy
        prepared_params = copy.deepcopy(test_params)
        
        # 处理parameters中的随机数
        if "parameters" in prepared_params and isinstance(prepared_params["parameters"], dict):
            for key, value in prepared_params["parameters"].items():
                if value == "__RANDOM_TASK_ID__":
                    if "random_task_id" not in self.random_cache:
                        self.random_cache["random_task_id"] = process_random_placeholders(value)
                    prepared_params["parameters"][key] = self.random_cache["random_task_id"]
                elif value == "__RANDOM_TASK_IDS_2__":
                    if "random_task_ids" not in self.random_cache:
                        self.random_cache["random_task_ids"] = process_random_placeholders(value)
                    prepared_params["parameters"][key] = self.random_cache["random_task_ids"]
        
        # 处理callback_task_ids
        if "callback_task_ids" in prepared_params:
            if prepared_params["callback_task_ids"] == "__RANDOM_TASK_ID__":
                if "random_task_id" in self.random_cache:
                    prepared_params["callback_task_ids"] = self.random_cache["random_task_id"]
            elif prepared_params["callback_task_ids"] == "__RANDOM_TASK_IDS_2__":
                if "random_task_ids" in self.random_cache:
                    prepared_params["callback_task_ids"] = self.random_cache["random_task_ids"]
        
        return prepared_params
    
    def execute_sync_test(self, test_case: Dict[str, Any], workflow_id: str) -> None:
        """
        执行同步测试
        
        Args:
            test_case: 测试用例
            workflow_id: 工作流ID
        """
        # 导入必要的函数（避免循环导入）
        from workflow.scene_test.test_workflow_invoke import execute_workflow_and_assert
        
        test_params = test_case["test_params"]
        expected_execute_status = test_case.get("expected_execute_status", 2)
        expected_output = test_case.get("expected_output", {})
        
        workflow_logger.info(f"开始执行同步测试: {test_case['test_name']}")
        
        response = execute_workflow_and_assert(
            workflow_id=workflow_id,
            test_params=test_params,
            expected_execute_status=expected_execute_status,
            expected_output=expected_output,
            route_env=self.route_env,
            base_url=self.base_url,
            auth_token=self.auth_token
        )
        
        workflow_logger.info(f"同步测试完成: {test_case['test_name']}")
        return response
    
    def execute_async_test(self, test_case: Dict[str, Any], workflow_id: str) -> None:
        """
        执行异步测试
        
        Args:
            test_case: 测试用例
            workflow_id: 工作流ID
        """
        # 导入必要的函数（避免循环导入）
        from workflow.scene_test.test_workflow_invoke import execute_async_workflow_and_assert
        
        test_params = test_case["test_params"]
        expected_execute_status = test_case.get("expected_execute_status", 2)
        expected_output = test_case.get("expected_output", {})
        
        workflow_logger.info(f"开始执行异步测试: {test_case['test_name']}")
        
        response = execute_async_workflow_and_assert(
            workflow_id=workflow_id,
            test_params=test_params,
            expected_execute_status=expected_execute_status,
            expected_output=expected_output,
            route_env=self.route_env,
            base_url=self.base_url,
            auth_token=self.auth_token
        )
        
        workflow_logger.info(f"异步测试完成: {test_case['test_name']}")
        return response
    
    def validate_test_case(self, test_case: Dict[str, Any]) -> None:
        """
        验证测试用例格式
        
        Args:
            test_case: 测试用例
            
        Raises:
            ValueError: 如果测试用例格式不正确
        """
        required_fields = ["test_name", "test_params"]
        for field in required_fields:
            if field not in test_case:
                raise ValueError(f"测试用例缺少必需字段: {field}")
        
        # 验证test_type
        test_type = test_case.get("test_type", "sync")
        if test_type not in ["sync", "async"]:
            raise ValueError(f"不支持的测试类型: {test_type}")
    
    def execute_test(self, test_case: Dict[str, Any], workflow_id: str) -> Any:
        """
        根据测试类型执行相应的测试
        
        Args:
            test_case: 测试用例
            workflow_id: 工作流ID
            
        Returns:
            测试响应结果
        """
        # 验证测试用例
        self.validate_test_case(test_case)
        
        # 根据测试类型执行相应的测试
        test_type = test_case.get("test_type", "sync")
        
        if test_type == "sync":
            return self.execute_sync_test(test_case, workflow_id)
        elif test_type == "async":
            return self.execute_async_test(test_case, workflow_id)
        else:
            raise ValueError(f"不支持的测试类型: {test_type}")


class TestParameterProcessor:
    """测试参数处理器，用于处理随机数占位符等"""
    
    @staticmethod
    def process_random_values(data: Any, random_cache: Dict[str, Any] = None) -> Any:
        """
        递归处理数据中的随机数占位符
        
        Args:
            data: 要处理的数据
            random_cache: 随机数缓存
            
        Returns:
            处理后的数据
        """
        if random_cache is None:
            random_cache = {}
            
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                result[key] = TestParameterProcessor.process_random_values(value, random_cache)
            return result
        elif isinstance(data, list):
            return [TestParameterProcessor.process_random_values(item, random_cache) for item in data]
        elif isinstance(data, str) and data.startswith("__RANDOM_"):
            if data not in random_cache:
                random_cache[data] = process_random_placeholders(data)
            return random_cache[data]
        else:
            return data