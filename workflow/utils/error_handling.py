'''
Author       : winsonyang 
Date         : 2025-04-15 11:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 15:20:00
FilePath     : /aigc-api-test/workflow/utils/error_handling.py
Description  : 统一的错误处理和日志管理模块

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import traceback
import json
from typing import Dict, Any, Optional, Union, Type

from workflow.utils.log_utils import workflow_logger


class WorkflowError(Exception):
    """工作流相关错误的基类"""
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class WorkflowValidationError(WorkflowError):
    """工作流验证错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)


class WorkflowNotFoundError(WorkflowError):
    """工作流未找到错误"""
    def __init__(self, workflow_id: str):
        super().__init__(
            message=f"工作流未找到: {workflow_id}", 
            status_code=404, 
            details={"workflow_id": workflow_id}
        )


class WorkflowConfigError(WorkflowError):
    """工作流配置错误"""
    pass


class WorkflowAuthError(WorkflowError):
    """工作流认证错误"""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, details=details)


class ApiRequestError(WorkflowError):
    """API请求错误"""
    def __init__(self, message: str, status_code: int = 500, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=status_code, details=details)


def handle_api_error(error: Exception, api_name: str, retry_attempts: Optional[int] = None, **extra_context) -> None:
    """
    处理API错误并记录日志
    
    Args:
        error: 异常对象
        api_name: API名称
        retry_attempts: 已尝试的重试次数（如果有）
        **extra_context: 额外的上下文信息
    """
    # 添加重试信息到上下文
    if retry_attempts is not None:
        extra_context["retry_attempts"] = retry_attempts
        
    context = {"api_name": api_name, **extra_context}
    
    # 如果是HTTP错误，转换为我们自己的ApiRequestError
    if hasattr(error, 'response') and hasattr(error.response, 'status_code'):
        status_code = error.response.status_code
        try:
            response_json = error.response.json()
            message = response_json.get('message', str(error))
            details = {"response": response_json}
        except ValueError:
            message = error.response.text or str(error)
            details = {"response_text": error.response.text}
            
        api_error = ApiRequestError(
            message=f"API请求失败: {message}",
            status_code=status_code,
            details={**details, "retry_attempts": retry_attempts}
        )
        workflow_logger.workflow_error("api_request", api_name, api_error, **context)
        raise api_error from error
    
    # 如果已经是WorkflowError，直接记录并重新抛出
    if isinstance(error, WorkflowError):
        workflow_logger.workflow_error("workflow", api_name, error, **context)
        raise
    
    # 其他类型的错误，包装为ApiRequestError
    api_error = ApiRequestError(
        message=f"API调用时发生错误: {str(error)}",
        details={"original_error": str(error), "retry_attempts": retry_attempts}
    )
    workflow_logger.workflow_error("api_request", api_name, api_error, **context)
    raise api_error from error 