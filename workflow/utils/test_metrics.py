'''
Author       : winsonyang 
Date         : 2025-01-20 20:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 20:00:00
FilePath     : /aigc-api-test/workflow/utils/test_metrics.py
Description  : 测试指标收集器，用于记录和分析API调用的稳定性

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
import time
import threading
from typing import Dict, Any, List, Optional, Tuple
from collections import defaultdict
from datetime import datetime
import os


class TestMetricsCollector:
    """测试指标收集器，记录API调用的稳定性数据"""
    
    def __init__(self):
        self.api_calls = []
        self.lock = threading.Lock()
        self.start_time = time.time()
        
    def record_api_call(self, 
                       api_name: str,
                       success: bool,
                       retry_count: int = 0,
                       duration: float = 0.0,
                       error_code: Optional[int] = None,
                       error_msg: Optional[str] = None,
                       trace_id: Optional[str] = None,
                       **extra_data):
        """
        记录API调用信息
        
        Args:
            api_name: API名称
            success: 是否最终成功
            retry_count: 重试次数
            duration: 总耗时（秒）
            error_code: 错误码（如果有）
            error_msg: 错误消息（如果有）
            trace_id: 追踪ID
            **extra_data: 其他额外数据
        """
        with self.lock:
            call_record = {
                "api_name": api_name,
                "success": success,
                "retry_count": retry_count,
                "duration": round(duration, 3),
                "error_code": error_code,
                "error_msg": error_msg,
                "trace_id": trace_id,
                "timestamp": time.time(),
                "timestamp_str": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                **extra_data
            }
            self.api_calls.append(call_record)
    
    def get_api_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取每个API的统计信息"""
        api_stats = defaultdict(lambda: {
            "total_calls": 0,
            "success_calls": 0,
            "failed_calls": 0,
            "retry_calls": 0,
            "total_duration": 0.0,
            "error_codes": defaultdict(int),
            "max_retry_count": 0
        })
        
        with self.lock:
            for call in self.api_calls:
                api = call["api_name"]
                stats = api_stats[api]
                
                stats["total_calls"] += 1
                if call["success"]:
                    stats["success_calls"] += 1
                else:
                    stats["failed_calls"] += 1
                
                if call["retry_count"] > 0:
                    stats["retry_calls"] += 1
                    stats["max_retry_count"] = max(stats["max_retry_count"], call["retry_count"])
                
                stats["total_duration"] += call["duration"]
                
                if call["error_code"]:
                    stats["error_codes"][str(call["error_code"])] += 1
        
        # 计算成功率和平均耗时
        for api, stats in api_stats.items():
            total = stats["total_calls"]
            if total > 0:
                stats["success_rate"] = round(stats["success_calls"] / total, 3)
                stats["retry_rate"] = round(stats["retry_calls"] / total, 3)
                stats["avg_duration"] = round(stats["total_duration"] / total, 3)
            else:
                stats["success_rate"] = 0.0
                stats["retry_rate"] = 0.0
                stats["avg_duration"] = 0.0
        
        return dict(api_stats)
    
    def get_error_distribution(self) -> Dict[str, int]:
        """获取错误分布（按HTTP状态码分类）"""
        error_dist = {
            "4xx": 0,
            "5xx": 0,
            "other": 0
        }
        
        with self.lock:
            for call in self.api_calls:
                if call["error_code"]:
                    code = call["error_code"]
                    if 400 <= code < 500:
                        error_dist["4xx"] += 1
                    elif 500 <= code < 600:
                        error_dist["5xx"] += 1
                    else:
                        error_dist["other"] += 1
        
        return error_dist
    
    def get_problematic_apis(self, error_threshold: float = 0.1) -> List[Dict[str, Any]]:
        """
        获取有问题的API列表
        
        Args:
            error_threshold: 错误率阈值，超过此值认为有问题
        
        Returns:
            问题API列表，按错误率降序排列
        """
        api_stats = self.get_api_statistics()
        problematic = []
        
        for api_name, stats in api_stats.items():
            error_rate = 1.0 - stats["success_rate"]
            if error_rate >= error_threshold or stats["retry_rate"] >= error_threshold:
                problematic.append({
                    "api_name": api_name,
                    "error_rate": error_rate,
                    "retry_rate": stats["retry_rate"],
                    "common_errors": sorted(
                        stats["error_codes"].items(), 
                        key=lambda x: x[1], 
                        reverse=True
                    )[:3]  # 前3个最常见的错误
                })
        
        # 按错误率降序排列
        problematic.sort(key=lambda x: x["error_rate"], reverse=True)
        return problematic
    
    def get_time_distribution(self, bucket_minutes: int = 1) -> Dict[str, Dict[str, int]]:
        """
        获取错误的时间分布
        
        Args:
            bucket_minutes: 时间桶的大小（分钟）
        
        Returns:
            时间分布统计
        """
        time_buckets = defaultdict(lambda: {"success": 0, "failure": 0, "retry": 0})
        bucket_seconds = bucket_minutes * 60
        
        with self.lock:
            for call in self.api_calls:
                # 计算时间桶
                bucket = int((call["timestamp"] - self.start_time) / bucket_seconds)
                bucket_time = self.start_time + bucket * bucket_seconds
                bucket_key = datetime.fromtimestamp(bucket_time).strftime("%H:%M:%S")
                
                if call["success"]:
                    time_buckets[bucket_key]["success"] += 1
                else:
                    time_buckets[bucket_key]["failure"] += 1
                
                if call["retry_count"] > 0:
                    time_buckets[bucket_key]["retry"] += 1
        
        return dict(time_buckets)
    
    def generate_stability_report(self) -> Dict[str, Any]:
        """生成稳定性报告"""
        with self.lock:
            total_calls = len(self.api_calls)
            success_calls = sum(1 for call in self.api_calls if call["success"])
            retry_calls = sum(1 for call in self.api_calls if call["retry_count"] > 0)
            total_duration = sum(call["duration"] for call in self.api_calls)
        
        report = {
            "summary": {
                "total_calls": total_calls,
                "success_calls": success_calls,
                "failed_calls": total_calls - success_calls,
                "success_rate": round(success_calls / total_calls, 3) if total_calls > 0 else 0.0,
                "calls_with_retry": retry_calls,
                "retry_rate": round(retry_calls / total_calls, 3) if total_calls > 0 else 0.0,
                "total_duration": round(total_duration, 2),
                "avg_duration": round(total_duration / total_calls, 3) if total_calls > 0 else 0.0,
                "test_duration": round(time.time() - self.start_time, 2)
            },
            "api_statistics": self.get_api_statistics(),
            "problematic_apis": self.get_problematic_apis(),
            "error_distribution": self.get_error_distribution(),
            "time_distribution": self.get_time_distribution(),
            "report_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return report
    
    def export_to_json(self, filepath: str):
        """
        导出详细数据和报告到JSON文件
        
        Args:
            filepath: 输出文件路径
        """
        data = {
            "report": self.generate_stability_report(),
            "raw_calls": self.api_calls
        }
        
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"稳定性数据已导出到: {filepath}")
    
    def print_summary(self):
        """打印稳定性摘要"""
        report = self.generate_stability_report()
        summary = report["summary"]
        
        print("\n" + "="*60)
        print("API稳定性测试报告")
        print("="*60)
        print(f"总调用次数: {summary['total_calls']}")
        print(f"成功率: {summary['success_rate']*100:.1f}%")
        print(f"重试率: {summary['retry_rate']*100:.1f}%")
        print(f"平均耗时: {summary['avg_duration']:.3f}秒")
        
        if report["problematic_apis"]:
            print("\n问题API:")
            for api in report["problematic_apis"][:5]:  # 只显示前5个
                print(f"  - {api['api_name']}: 错误率={api['error_rate']*100:.1f}%, 重试率={api['retry_rate']*100:.1f}%")
        
        error_dist = report["error_distribution"]
        if any(error_dist.values()):
            print(f"\n错误分布: 4xx={error_dist['4xx']}, 5xx={error_dist['5xx']}, 其他={error_dist['other']}")
        
        print("="*60 + "\n")


# 全局实例，供测试使用
global_metrics_collector = None

def get_metrics_collector() -> TestMetricsCollector:
    """获取全局指标收集器实例"""
    global global_metrics_collector
    if global_metrics_collector is None:
        global_metrics_collector = TestMetricsCollector()
    return global_metrics_collector

def reset_metrics_collector():
    """重置指标收集器"""
    global global_metrics_collector
    global_metrics_collector = TestMetricsCollector()