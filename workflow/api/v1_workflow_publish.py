'''
Author       : winsonyang 
Date         : 2025-03-21 16:28:43
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 18:00:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_publish.py
Description  : 发布工作流API接口 - 重构版

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional

from workflow.utils.base_api import BaseWorkflowAPI, api_endpoint
from workflow.utils.error_handling import WorkflowValidationError


class WorkflowPublishAPI(BaseWorkflowAPI):
    """工作流发布API"""
    
    def get_endpoint(self) -> str:
        """获取API端点路径"""
        return "/api/workflow/publish"
    
    @api_endpoint("workflow_publish")
    def execute(self,
                workflow_id: str,
                route_env: Optional[str] = None,
                headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None
                ) -> Dict[str, Any]:
        """
        发布工作流
        
        Args:
            workflow_id: 要发布的工作流ID
            route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            API响应结果，包含body和headers
            
        Raises:
            WorkflowValidationError: 如果必需参数未提供
            ApiRequestError: 如果API请求失败
        """
        # 验证必需参数
        self.validate_workflow_id(workflow_id)
        
        # 构建请求负载
        payload = {
            "workflow_id": workflow_id
        }
        
        # 使用基类的请求方法
        return self.make_request(
            payload=payload,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token
        )


# 创建向后兼容的函数
def v1_workflow_publish(
    workflow_id: str,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用Hunyuan API发布工作流（向后兼容接口）
    
    Args:
        workflow_id: 要发布的工作流ID
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 请求头信息
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        API响应结果，包含body和headers
        
    Raises:
        requests.exceptions.HTTPError: 如果请求失败
    """
    api = WorkflowPublishAPI(base_url=base_url)
    return api.execute(
        workflow_id=workflow_id,
        route_env=route_env,
        headers=headers,
        auth_token=auth_token
    )
