'''
Author       : winsonyang 
Date         : 2025-01-20 17:50:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 17:50:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_create_refactored.py
Description  : 重构后的创建工作流API接口，使用基础类

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional

from workflow.utils.base_api import BaseWorkflowAPI, api_endpoint, WorkflowParameterValidator
from workflow.utils.error_handling import WorkflowValidationError
from workflow.utils.api_utils import load_workflow_from_file


class WorkflowCreateAPI(BaseWorkflowAPI):
    """工作流创建API"""
    
    def get_endpoint(self) -> str:
        """获取API端点路径"""
        return "/api/workflow/create"
    
    @api_endpoint("create")
    def execute(self,
                workspace: str,
                description: str,
                name: str,
                workflow: Optional[Dict[str, Any]] = None,
                workflow_file: Optional[str] = None,
                route_env: Optional[str] = None,
                headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None
                ) -> Dict[str, Any]:
        """
        创建新的工作流
        
        Args:
            workspace: 工作空间
            description: 工作流描述
            name: 工作流名称
            workflow: 工作流配置字典（与workflow_file二选一）
            workflow_file: 工作流配置文件路径（与workflow二选一）
            route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            API响应字典，包含body和headers
            
        Raises:
            WorkflowValidationError: 如果参数验证失败
            ApiRequestError: 如果API请求失败
        """
        # 使用参数验证器
        validator = WorkflowParameterValidator()
        validator.validate_workspace(workspace)
        validator.validate_name(name)
        
        # 验证并获取工作流配置
        if workflow_file:
            workflow_config = load_workflow_from_file(workflow_file)
        else:
            workflow_config = workflow
        
        # 构建请求负载
        payload = {
            "workspace": workspace,
            "name": name,
            "description": description,
            "workflow_dsl": workflow_config
        }
        
        # 使用基类的同步请求方法
        return self.make_request(
            payload=payload,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token
        )


# 创建向后兼容的函数
def v1_workflow_create(
    workspace: str,
    description: str,
    name: str,
    workflow: Optional[Dict[str, Any]] = None,
    workflow_file: Optional[str] = None,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建新的工作流（向后兼容接口）
    
    Args:
        workspace: 工作空间
        description: 工作流描述
        name: 工作流名称
        workflow: 工作流配置字典（与workflow_file二选一）
        workflow_file: 工作流配置文件路径（与workflow二选一）
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        API响应字典，包含body和headers
        
    Raises:
        WorkflowValidationError: 如果参数验证失败
        ApiRequestError: 如果API请求失败
    """
    api = WorkflowCreateAPI(base_url=base_url)
    return api.execute(
        workspace=workspace,
        description=description,
        name=name,
        workflow=workflow,
        workflow_file=workflow_file,
        route_env=route_env,
        headers=headers,
        auth_token=auth_token
    )