'''
Author       : winsonyang 
Date         : 2025-03-18 16:41:43
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 11:30:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_execution_detail.py
Description  : 获取工作流执行详情的API接口

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional
from workflow.utils.api_utils import make_api_request
from workflow.utils.error_handling import WorkflowValidationError, workflow_logger


def v1_workflow_execution_detail(
    workflow_id: str,
    execute_id: str,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取工作流执行详情
    
    Args:
        workflow_id: 工作流ID
        execute_id: 执行ID
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        API响应结果，包含响应体和响应头
        
    Raises:
        WorkflowValidationError: 如果workflow_id或execute_id未提供
        ApiRequestError: 如果API请求失败
    """
    validation_errors = {}
    
    if not workflow_id:
        validation_errors["workflow_id"] = "必须提供"
    if not execute_id:
        validation_errors["execute_id"] = "必须提供"
        
    if validation_errors:
        raise WorkflowValidationError(
            message="获取工作流执行详情参数验证失败",
            details={"validation_errors": validation_errors}
        )
    
    workflow_logger.info(f"获取工作流执行详情: 工作流ID={workflow_id}, 执行ID={execute_id}")
        
    endpoint = "/api/workflow/execution/detail"
    
    payload = {
        "workflow_id": workflow_id,
        "execute_id": execute_id
    }
    
    return make_api_request(
        endpoint=endpoint,
        payload=payload,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
