'''
Author       : winsonyang 
Date         : 2025-03-12 11:44:13
LastEditors  : winsonyang 
LastEditTime : 2025-07-03 11:17:55
FilePath     : /aigc-api-test/workflow/api/v1_workflow_execution_chat.py
Description  : 工作流执行聊天API接口

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
import json
import requests
from typing import Dict, Any, Optional, Generator, Tuple
from workflow.utils.api_utils import make_api_request
from workflow.utils.error_handling import WorkflowValidationError
from workflow.utils.log_utils import workflow_logger
from workflow.utils.sse_utils import process_sse_stream_with_headers


def v1_workflow_execution_chat(
    workflow_id: str,
    route_env: Optional[str] = None,
    parameters: Dict[str, Any] = {},
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    执行工作流聊天API
    
    Args:
        workflow_id: 工作流ID
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        parameters: 执行参数
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        生成器，返回(响应数据, 响应头)元组
        
    Raises:
        WorkflowValidationError: 如果workflow_id未提供
        ApiRequestError: 如果API请求失败
    """
    if not workflow_id:
        raise WorkflowValidationError(
            message="workflow_id必须提供",
            details={"provided_parameters": {"workflow_id": workflow_id}}
        )
    
    workflow_logger.workflow_operation("chat", workflow_id, parameters=parameters)
    
    endpoint = "/api/workflow/execution/chat"
    
    default_headers = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
    }
    
    # 只有在route_env不为空时才添加X-Route-Env头
    if route_env:
        default_headers['X-Route-Env'] = route_env
    
    if auth_token:
        default_headers['Authorization'] = f'Bearer {auth_token}'
    
    if headers:
        default_headers.update(headers)
    
    url = f"{base_url}{endpoint}"
    
    payload = {
        "workflow_id": workflow_id,
        "parameters": parameters
    }
    
    workflow_logger.api_request("POST", url, default_headers, payload)
    
    # Use stream=True to handle the streaming response
    with requests.post(
        url,
        headers=default_headers,
        data=json.dumps(payload),
        stream=True
    ) as response:
        # Raise an exception for HTTP errors
        response.raise_for_status()
        
        # Get the headers once at the beginning
        response_headers = dict(response.headers)
        workflow_logger.api_response("POST", url, response.status_code, 0, {"headers": response_headers})
        
        # Use the common SSE processing utility
        for event_data, headers in process_sse_stream_with_headers(response, response_headers):
            yield event_data, headers