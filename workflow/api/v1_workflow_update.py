'''
Author       : winsonyang 
Date         : 2025-03-19 16:16:20
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 10:15:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_update.py
Description  : 更新工作流API接口

Copyright (c) 2025 by Ten<PERSON>, All Rights Reserved. 
'''
from typing import Dict, Any, Optional
from workflow.utils.api_utils import make_api_request


def v1_workflow_update(
    workflow_id: str,
    workflow: Dict[str, Any],
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    使用Hunyuan API更新工作流
    
    Args:
        workflow_id: 要更新的工作流ID
        workflow: 工作流配置数据
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 请求头信息
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        API响应结果，包含body和headers
        
    Raises:
        requests.exceptions.HTTPError: 如果请求失败
    """
    endpoint = "/api/workflow/update"
    
    payload = {
        "workflow_id": workflow_id,
        "workflow_dsl": workflow
    }
    
    return make_api_request(
        endpoint=endpoint,
        payload=payload,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
