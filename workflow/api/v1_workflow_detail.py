'''
Author       : winsonyang 
Date         : 2025-03-11 16:21:13
LastEditors  : winsonyang 
LastEditTime : 2025-04-15 13:20:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_detail.py
Description  : 

Copyright (c) 2025 by <PERSON><PERSON>, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Union
from workflow.utils.api_utils import make_api_request
from workflow.utils.error_handling import WorkflowValidationError, workflow_logger
from workflow.utils.response_utils import ApiResponse, process_workflow_detail_response


def v1_workflow_detail(
    workflow_id: str,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get details of a workflow using the Hunyuan API.
    
    Args:
        workflow_id: The ID of the workflow to query
        route_env: The route environment (optional, no X-Route-Env header if not set)
        headers: Additional headers to include in the request
        base_url: The base URL for the API (default: https://test.hunyuan.woa.com)
        auth_token: Authentication token for Bearer authorization
        
    Returns:
        The API response as a dictionary
        
    Raises:
        WorkflowValidationError: If workflow_id is not provided
        ApiRequestError: If the API request fails
    """
    if not workflow_id:
        raise WorkflowValidationError(
            message="workflow_id must be provided",
            details={"provided_parameters": {"workflow_id": workflow_id}}
        )
    
    workflow_logger.info(f"获取工作流详情: ID={workflow_id}")
        
    endpoint = "/api/workflow/detail"
        
    payload = {
        "workflow_id": workflow_id
    }
    
    response = make_api_request(
        endpoint=endpoint,
        payload=payload,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    # 对响应进行基本验证，但不提取数据，以保持原有API的返回结构
    ApiResponse(response).validate_success("workflow_detail")
    
    return response


def get_workflow_dsl(
    workflow_id: str,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取工作流配置DSL
    
    此函数是v1_workflow_detail的便捷包装，直接返回工作流DSL而不是完整响应
    
    Args:
        与v1_workflow_detail相同
        
    Returns:
        工作流DSL配置
        
    Raises:
        与v1_workflow_detail相同，以及：
        WorkflowValidationError: 如果响应中没有有效的工作流DSL
    """
    response = v1_workflow_detail(
        workflow_id=workflow_id,
        route_env=route_env,
        headers=headers,
        base_url=base_url,
        auth_token=auth_token
    )
    
    return process_workflow_detail_response(response)
