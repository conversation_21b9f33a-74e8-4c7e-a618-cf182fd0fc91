'''
Author       : winsonyang 
Date         : 2025-01-20 18:00:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 18:00:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_submit_user_outputs.py
Description  : 提交用户输出API接口，用于继续工作流执行

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Generator, Tuple

from workflow.utils.base_api import BaseWorkflowAPI, api_endpoint
from workflow.utils.error_handling import WorkflowValidationError


class WorkflowSubmitUserOutputsAPI(BaseWorkflowAPI):
    """工作流提交用户输出API"""
    
    def get_endpoint(self) -> str:
        """获取API端点路径"""
        return "/openapi/v1/workflow/submit_user_outputs"
    
    @api_endpoint("submit_user_outputs")
    def execute(self,
                workflow_id: str,
                execute_id: str,
                user_outputs: Dict[str, Any],
                stream: bool = True,
                route_env: Optional[str] = None,
                headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None
                ) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
        """
        提交用户输出以继续工作流执行
        
        Args:
            workflow_id: 工作流ID
            execute_id: 执行ID
            user_outputs: 用户输出内容，根据工作流定义的要求提供
            stream: 是否流式返回 (默认: True)
            route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            生成器，返回(响应数据, 响应头)元组
            
        Raises:
            WorkflowValidationError: 如果必需参数未提供
            ApiRequestError: 如果API请求失败
        """
        # 验证必需参数
        self.validate_workflow_id(workflow_id)
        self.validate_required_params(execute_id=execute_id)
        
        # 支持两种格式：字典格式（旧）和数组格式（新）
        if not isinstance(user_outputs, (dict, list)):
            raise WorkflowValidationError(
                message="user_outputs必须是字典或数组类型",
                details={"provided_type": type(user_outputs).__name__}
            )

        # 如果是数组格式，验证每个元素的结构
        if isinstance(user_outputs, list):
            for item in user_outputs:
                if not isinstance(item, dict):
                    raise WorkflowValidationError(
                        message="user_outputs数组中的每个元素必须是字典类型",
                        details={"item_type": type(item).__name__}
                    )
                if "user_reply_id" not in item or "output" not in item:
                    raise WorkflowValidationError(
                        message="user_outputs数组中的每个元素必须包含user_reply_id和output字段",
                        details={"item_keys": list(item.keys())}
                    )
        
        # 构建请求负载
        payload = {
            "workflow_id": workflow_id,
            "execute_id": execute_id,
            "user_outputs": user_outputs,
            "stream": stream
        }
        
        # 使用基类的流式请求方法
        return self.make_streaming_request(
            payload=payload,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token,
            stream=stream
        )


# 创建向后兼容的函数
def v1_workflow_submit_user_outputs(
    workflow_id: str,
    execute_id: str,
    user_outputs: Dict[str, Any],
    stream: bool = True,
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    提交用户输出以继续工作流执行（向后兼容接口）
    
    Args:
        workflow_id: 工作流ID
        execute_id: 执行ID
        user_outputs: 用户输出内容，根据工作流定义的要求提供
        stream: 是否流式返回 (默认: True)
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        生成器，返回(响应数据, 响应头)元组
        
    Raises:
        requests.exceptions.HTTPError: 如果请求失败
    """
    api = WorkflowSubmitUserOutputsAPI(base_url=base_url)
    return api.execute(
        workflow_id=workflow_id,
        execute_id=execute_id,
        user_outputs=user_outputs,
        stream=stream,
        route_env=route_env,
        headers=headers,
        auth_token=auth_token
    )