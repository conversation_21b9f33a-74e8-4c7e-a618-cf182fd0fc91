'''
Author       : winsonyang 
Date         : 2025-01-20 17:40:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 17:40:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_chat_completions_refactored.py
Description  : 重构后的工作流聊天完成API接口，使用基础类

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Generator, Tuple, List

from workflow.utils.base_api import BaseWorkflowAPI, api_endpoint
from workflow.utils.error_handling import WorkflowValidationError


class WorkflowChatCompletionsAPI(BaseWorkflowAPI):
    """工作流聊天完成API"""
    
    def get_endpoint(self) -> str:
        """获取API端点路径"""
        return "/openapi/v1/workflow/chat/completions/id"
    
    @api_endpoint("chat_completions")
    def execute(self,
                workflow_id: str,
                messages: List[Dict[str, str]],
                version: str = "1",
                stream: bool = True,
                parameters: Dict[str, Any] = {},
                route_env: Optional[str] = None,
                headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None
                ) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
        """
        执行工作流聊天完成API
        
        Args:
            workflow_id: 工作流ID
            messages: 聊天消息数组，每个消息包含role和content字段
            version: API版本 (默认: "1")
            stream: 是否流式返回 (默认: True)
            parameters: 额外的执行参数 (默认: {})
            route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            生成器，返回(响应数据, 响应头)元组
            
        Raises:
            WorkflowValidationError: 如果必需参数未提供或格式不正确
            ApiRequestError: 如果API请求失败
        """
        # 验证必需参数
        self.validate_workflow_id(workflow_id)
        self.validate_messages(messages)
        
        # 构建请求负载
        payload = {
            "workflow_id": workflow_id,
            "messages": messages,
            "version": version,
            "stream": stream,
            "parameters": parameters
        }
        
        # 使用基类的流式请求方法
        return self.make_streaming_request(
            payload=payload,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token,
            stream=stream
        )


# 创建向后兼容的函数
def v1_workflow_chat_completions(
    workflow_id: str,
    messages: List[Dict[str, str]],
    version: str = "1",
    stream: bool = True,
    parameters: Dict[str, Any] = {},
    route_env: Optional[str] = None,
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    执行工作流聊天完成API（向后兼容接口）
    
    Args:
        workflow_id: 工作流ID
        messages: 聊天消息数组，每个消息包含role和content字段
        version: API版本 (默认: "1")
        stream: 是否流式返回 (默认: True)
        parameters: 额外的执行参数 (默认: {})
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        生成器，返回(响应数据, 响应头)元组
        
    Raises:
        WorkflowValidationError: 如果必需参数未提供或格式不正确
        ApiRequestError: 如果API请求失败
    """
    api = WorkflowChatCompletionsAPI(base_url=base_url)
    return api.execute(
        workflow_id=workflow_id,
        messages=messages,
        version=version,
        stream=stream,
        parameters=parameters,
        route_env=route_env,
        headers=headers,
        auth_token=auth_token
    )