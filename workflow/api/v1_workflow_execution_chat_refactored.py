'''
Author       : winsonyang 
Date         : 2025-01-20 17:30:00
LastEditors  : winsonyang 
LastEditTime : 2025-01-20 17:30:00
FilePath     : /aigc-api-test/workflow/api/v1_workflow_execution_chat_refactored.py
Description  : 重构后的工作流执行聊天API接口，使用基础类

Copyright (c) 2025 by Tencent, All Rights Reserved. 
'''
from typing import Dict, Any, Optional, Generator, Tuple

from workflow.utils.base_api import BaseWorkflowAPI, api_endpoint
from workflow.utils.error_handling import WorkflowValidationError


class WorkflowExecutionChatAPI(BaseWorkflowAPI):
    """工作流执行聊天API"""
    
    def get_endpoint(self) -> str:
        """获取API端点路径"""
        return "/api/workflow/execution/chat"
    
    @api_endpoint("chat")
    def execute(self,
                workflow_id: str,
                route_env: Optional[str] = None,
                parameters: Dict[str, Any] = {},
                headers: Optional[Dict[str, str]] = None,
                auth_token: Optional[str] = None
                ) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
        """
        执行工作流聊天API
        
        Args:
            workflow_id: 工作流ID
            route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
            parameters: 执行参数
            headers: 额外的请求头
            auth_token: 认证令牌
            
        Returns:
            生成器，返回(响应数据, 响应头)元组
            
        Raises:
            WorkflowValidationError: 如果workflow_id未提供
            ApiRequestError: 如果API请求失败
        """
        # 验证必需参数
        self.validate_workflow_id(workflow_id)
        
        # 构建请求负载
        payload = {
            "workflow_id": workflow_id,
            "parameters": parameters
        }
        
        # 使用基类的流式请求方法
        return self.make_streaming_request(
            payload=payload,
            route_env=route_env,
            headers=headers,
            auth_token=auth_token,
            stream=True
        )


# 创建向后兼容的函数
def v1_workflow_execution_chat(
    workflow_id: str,
    route_env: Optional[str] = None,
    parameters: Dict[str, Any] = {},
    headers: Optional[Dict[str, str]] = None,
    base_url: str = "https://test.hunyuan.woa.com",
    auth_token: Optional[str] = None
) -> Generator[Tuple[Dict[str, Any], Dict[str, str]], None, None]:
    """
    执行工作流聊天API（向后兼容接口）
    
    Args:
        workflow_id: 工作流ID
        route_env: 路由环境 (可选，不设置时不传递X-Route-Env头)
        parameters: 执行参数
        headers: 额外的请求头
        base_url: API基础URL (默认: https://test.hunyuan.woa.com)
        auth_token: 认证令牌
        
    Returns:
        生成器，返回(响应数据, 响应头)元组
        
    Raises:
        WorkflowValidationError: 如果workflow_id未提供
        ApiRequestError: 如果API请求失败
    """
    api = WorkflowExecutionChatAPI(base_url=base_url)
    return api.execute(
        workflow_id=workflow_id,
        route_env=route_env,
        parameters=parameters,
        headers=headers,
        auth_token=auth_token
    )