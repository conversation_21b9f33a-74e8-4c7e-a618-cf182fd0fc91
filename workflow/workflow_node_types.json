{"node_types": {"START": {"description": "工作流的起始节点，定义输入参数", "structure": {"node_id": "START", "node_type": "START", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": null, "output": {"properties": {"参数名1": {"type": "string"}, "参数名2": {"type": "integer"}}}}}, "END": {"description": "工作流的结束节点，定义输出结果", "structure": {"node_id": "END", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "参数ID", "name": "参数名", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}, "required": true}], "message": {"message_type": "VARIABLE", "streaming_output": false, "string_format": null}}}, "HTTP": {"description": "发送HTTP请求的节点", "structure": {"node_id": "节点ID", "node_type": "HTTP", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}, {"id": "body.参数名", "name": "参数名", "required": true, "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "output": {"properties": {"data.字段名": {"type": "integer"}, "data": {"type": "string"}}}, "http": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "url": "接口路径", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}], "method": "HTTP方法", "timeout": 0, "body": [{"id": "body.参数名", "name": "参数名", "required": true, "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "body_type": "", "query": null, "retry_times": 0}}}, "LLM": {"description": "调用大语言模型的节点", "structure": {"node_id": "节点ID", "node_type": "LLM", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}, {"id": "user_prompt_2", "name": "prompt", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}, "literal": "默认提示词"}], "llm": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "api_token": "API令牌", "provider": "提供商", "model": "模型名称", "stream": true, "chat_history": {"id": "chat_history_count", "name": "chat_history_count", "input_type": "literal", "literal": "历史记录数量"}, "system_prompt": [{"id": "system_prompt", "name": "system_prompt", "input_type": "literal", "literal": "系统提示词"}], "user_prompt": [{"id": "user_prompt_1", "name": "user_prompt_1", "input_type": "literal", "literal": "用户提示词前缀"}, {"id": "user_prompt_2", "name": "prompt", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}]}}}, "CODE": {"description": "执行代码的节点", "structure": {"node_id": "节点ID", "node_type": "CODE", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": [{"id": "参数ID", "input_type": "reference", "name": "参数名", "reference": {"name": "参数名", "node_id": "来源节点ID", "type": "string"}, "required": true}], "code": {"code": "代码内容"}, "output": {"properties": {"输出字段名": {"type": "string"}}}}}, "BRANCH": {"description": "条件分支节点", "structure": {"node_id": "节点ID", "node_type": "BRANCH", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "branch", "name": "branch", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "branch", "type": "string"}}], "branch": {"conditions": [{"expr": "", "param": {"operator": "", "if": [{"left": {"id": "branch", "name": "branch", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "branch", "type": "string"}}, "right": {"id": "分支名", "name": "输入", "input_type": "literal", "literal": "分支值"}, "op": "eq"}]}, "next": ["分支节点ID"]}], "default_next": ["默认分支节点ID"]}}}, "EXTRACT": {"description": "从输入中提取特定信息的节点", "structure": {"node_id": "节点ID", "node_type": "EXTRACT", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": [{"name": "input", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "extract": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "api_token": "API令牌", "provider": "提供商", "model": "模型名称", "temperature": 1, "top_p": 1, "top_k": 40, "properties": {"属性名1": {"type": "boolean", "description": "属性描述", "default": false}, "属性名2": {"type": "string", "description": "属性描述", "default": "默认值"}, "属性名3": {"type": "number", "description": "属性描述", "default": 1}}}, "output": {"properties": {"属性名1": {"type": "boolean"}, "属性名2": {"type": "string"}, "属性名3": {"type": "integer"}}}}}, "INTENT": {"description": "识别用户意图的节点", "structure": {"node_id": "节点ID", "node_type": "INTENT", "node_meta": {"name": "", "description": ""}, "input": [{"id": "query", "name": "query", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "intent": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "api_token": "API令牌", "provider": "提供商", "model": "模型名称", "url": "接口路径", "need_reason": false, "conditions": [{"intent": "意图名称", "next": ["意图对应的节点ID"]}], "default_next": ["默认节点ID"], "user_prompt": [{"id": "user_prompt_1", "name": "user_prompt_1", "input_type": "literal", "literal": "提示词前缀"}, {"id": "user_prompt_2", "name": "prompt", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}]}, "next": null, "output": {"properties": {"intent": {"type": "string"}}}}}, "ITER": {"description": "迭代处理节点，支持数组迭代、条件迭代和固定次数迭代", "structure": {"node_id": "节点ID", "node_type": "ITER", "node_meta": {"description": "", "name": ""}, "iter": {"iter_type": "array/condition/literal", "arrays": [{"id": "iter_array.input", "input_type": "reference", "reference": {"name": "参数名", "node_id": "来源节点ID", "type": "array"}, "name": "参数名", "required": true}], "local_variables": [{"id": "local_variables.变量名", "input_type": "literal", "name": "变量名", "literal": "变量值", "literal_type": "string", "required": true}], "iter_output": [{"id": "output.输出名", "input_type": "reference", "name": "输出名", "reference": {"name": "输出名", "node_id": "子图节点ID", "type": "string"}, "required": true}], "references": [{"id": "引用节点ID", "input_type": "reference", "name": "引用名", "reference": {"name": "@this", "node_id": "引用节点ID", "type": "object"}, "required": true}], "iter_count": {"id": "iter.count", "input_type": "literal", "name": "count", "literal": "迭代次数", "literal_type": "integer", "required": true}, "sub_graph": {"type": "WORKFLOW", "graph_id": "", "nodes": []}}, "input": [], "next": ["下一个节点ID"], "output": {"properties": {"输出名": {"type": "array"}}}}}, "VARIABLE": {"description": "变量管理节点，支持设置和删除变量", "structure": {"node_id": "节点ID", "node_type": "VARIABLE", "node_meta": {"description": "", "name": ""}, "input": [{"input_type": "reference", "name": "参数名", "reference": {"name": "参数名", "node_id": "来源节点ID", "type": "string"}, "required": true}, {"input_type": "literal", "name": "参数名", "literal": "参数值", "required": true}], "output": {"properties": {"变量名1": {"type": "string"}, "变量名2": {"type": "string"}}}, "next": ["下一个节点ID"], "variable": {"actions": [{"action": "set/del", "variable_name": "变量名", "variable_type": "", "value": {"input_type": "literal/reference", "name": "参数名", "literal": "参数值", "required": true, "literal_type": "string"}}]}}}, "ASYNC": {"description": "异步处理节点，支持回调式和查询式异步", "structure": {"node_id": "节点ID", "node_type": "ASYNC", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}, {"id": "body.参数名", "name": "参数名", "required": true, "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "output": {"properties": {"输出名": {"type": "string"}}}, "async": {"async_type": "CALLBACK/QUERY", "timeout": 20, "submit_config": {"http_config": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "url": "提交接口路径", "method": "", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}], "body": [{"id": "body.参数名", "name": "参数名", "required": true, "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}}], "timeout": 0, "body_type": "", "query": null, "retry_times": 0}, "task_id_json_path": "task_id"}, "callback_config": {"end_config": {"response_status_jsonpath": "status", "final_state_list": ["succeeded", "failed", "cancelled", "unknown"], "failed_state_list": ["failed"]}}, "query_config": {"http_config": {"polaris_name": "服务名", "polaris_namespace": "命名空间", "url": "查询接口路径", "headers": [{"id": "headers.Authorization", "name": "Authorization", "required": true, "input_type": "literal", "literal": "Bearer token值"}], "method": "", "timeout": 0, "body": [{"id": "body.task_id", "name": "task_id", "required": true, "input_type": "submit_response", "reference": {"name": "task_id", "type": "string"}}], "body_type": "", "query": null, "retry_times": 0}, "end_config": {"response_status_jsonpath": "status", "final_state_list": ["succeeded", "failed", "cancelled", "unknown"], "failed_state_list": ["failed"]}, "query_interval_time": 2}}}}, "STOP_ITER": {"description": "停止迭代的节点", "structure": {"node_id": "节点ID", "node_type": "STOP_ITER", "node_meta": {"name": "", "description": ""}, "next": ["下一个节点ID"], "input": []}}, "EVAL": {"description": "执行代码的节点，与CODE节点类似", "structure": {"node_id": "节点ID", "node_meta": {"description": "", "name": ""}, "node_type": "EVAL", "code": {"code": "代码内容"}, "input": [], "output": {"type": "object", "properties": {"属性名": {"type": "string"}}}, "next": ["下一个节点ID"]}}, "MESSAGE": {"description": "消息输出节点，与END节点类似", "structure": {"node_id": "节点ID", "node_type": "MESSAGE", "node_meta": {"name": "", "description": ""}, "next": [], "input": [{"id": "参数ID", "name": "参数名", "input_type": "reference", "reference": {"node_id": "来源节点ID", "name": "参数名", "type": "string"}, "required": true}], "message": {"message_type": "VARIABLE/STRING", "streaming_output": false, "string_format": null}}}, "KNOWLEDGE": {"node_id": "节点ID", "node_type": "KNOWLEDGE", "node_meta": {"name": "", "description": "根据入参召回最佳匹配的信息"}, "next": ["END"], "input": [{"id": "query", "name": "query", "required": true, "input_type": "reference", "reference": {"node_id": "START", "name": "userpromot", "type": "string"}}], "knowledge": {"min_score": 0.5, "top_k": 3, "strategy": "semantic", "knowledgeIds": ["686f34fb28e389a7b9814dbc"]}}}}